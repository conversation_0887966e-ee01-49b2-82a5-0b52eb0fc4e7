"""
参数探索模块

本模块提供分级参数探索策略的实现，用于处理参数之间的依赖关系，
确保参数组合的有效性。通过按优先级顺序探索参数，避免无效的参数组合。

参数优先级分级:
----------
1. 第一优先级：无约束参数
   - 完全独立，不依赖于其他参数的参数

2. 第二优先级：基础约束参数
   - 有基础约束关系，但不依赖于其他复杂参数的参数

3. 第三优先级：复杂约束参数
   - 依赖于多个其他参数，有复杂的约束关系的参数

4. 第四优先级：最高约束参数
   - 依赖于多个其他参数，且有严格的兼容性要求的参数

主要组件:
----------
1. HierarchicalParameterExplorer: 分级参数探索器，管理参数之间的依赖关系
2. 参数依赖关系处理函数: 用于处理特定参数之间的依赖关系
3. 参数取值范围生成函数: 根据高优先级参数的值生成低优先级参数的取值范围

使用方式:
----------
```python
from src.optimization.parameter_exploration import HierarchicalParameterExplorer

# 创建分级参数探索器
explorer = HierarchicalParameterExplorer()

# 在Optuna目标函数中使用
def objective(trial):
    # 使用分级参数探索器配置参数
    config = explorer.configure_parameters(trial, config)

    # 运行训练并返回评估指标
    # ...
```
"""

from typing import Any, TypeVar, cast

# 定义泛型类型变量
T = TypeVar('T')
import optuna

from src.utils.config_manager import ConfigManager
from src.utils.logger import LoggerFactory

# 获取日志记录器
logger = LoggerFactory().get_logger("ParameterExplorer")


class HierarchicalParameterExplorer:
    """
    分级参数探索器

    管理参数之间的依赖关系，按优先级顺序探索参数，确保参数组合的有效性。

    参数优先级分级:
    1. 第一优先级：无约束参数 - 完全独立，不依赖于其他参数
    2. 第二优先级：基础约束参数 - 有基础约束关系，但不依赖于其他复杂参数
    3. 第三优先级：复杂约束参数 - 依赖于多个其他参数，有复杂的约束关系
    4. 第四优先级：最高约束参数 - 依赖于多个其他参数，且有严格的兼容性要求
    """

    def __init__(self):
        """初始化分级参数探索器"""
        # 参数优先级顺序
        self.parameter_order = [
            # 第一优先级：无约束参数
            "model.generator_type",     # ADDED
            "model.noise_dim",
            "model.dropout_rate",
            "model.loss.feature_matching_weight",
            "training.balance.lower_threshold",
            # "model.test_float_param", # 注释掉测试参数
            # 以下参数在测试环境中不存在，暂时注释掉
            "feature_engineering.time_series_features.lag_features.max_lag",
            # "feature_engineering.time_series_features.window_features.window_size",

            # 第二优先级：基础约束参数
            "training.gradient_clip_val",   # ADDED
            "data.window_size",
            "training.balance.upper_threshold",

            # 第三优先级：复杂约束参数
            "model.hidden_dim",
            "training.lambda_gp",

            # 第四优先级：最高约束参数
            "model.n_heads"
        ]

        # 参数依赖关系
        self.parameter_dependencies = {
            # 隐藏维度依赖于窗口大小（兼容性检查在参数建议后进行）
            "model.hidden_dim": {
                "depends_on": ["data.window_size"]
                # "get_choices" 不再需要，使用固定列表并在 configure_parameters 中检查
            },
            "model.noise_dim": {
                "depends_on": [],  # 不再依赖其他参数
                "get_choices": self._get_noise_dim_choices_direct  # 使用新方法
            },

            # 注意力头数依赖于隐藏维度（兼容性检查在参数建议后进行）
            "model.n_heads": {
                "depends_on": ["model.hidden_dim"],
                # "get_choices" 不再需要，在 configure_parameters 中动态过滤兼容选项
                # "condition" 移除，因为 use_self_attention 被移除
            },

            # lambda_gp 参数不再依赖于是否使用自注意力
            # 注意：使用固定选项集，涵盖所有可能值，避免动态值空间限制错误
            "training.lambda_gp": {
                "depends_on": [], # 移除 model.use_self_attention 依赖
                "get_choices": self._get_lambda_gp_choices
            },

            # 直接设置训练平衡参数，不再使用balance.strength元参数
            "training.balance.lower_threshold": {
                "depends_on": [],
                "get_choices": self._get_lower_threshold_choices
            },
            "training.balance.upper_threshold": {
                "depends_on": ["training.balance.lower_threshold"],
                "get_choices": self._get_upper_threshold_choices
            }
        }

        # 默认参数取值范围
        self.default_choices = {
            # 第一优先级：无约束参数
            "model.generator_type": ['gru', 'lstm'],   # 保持原样
            "model.noise_dim": [64, 96, 128],    # 保持原样
            "model.dropout_rate": [0.1, 0.2], # 强烈推荐，至少两个相邻值
            "model.loss.feature_matching_weight": [0.5, 1.0], # 强烈推荐，至少两个相邻值
            "training.balance.lower_threshold": [0.4, 0.5], # 强烈推荐，至少两个相邻值
            # "model.test_float_param": [0.1, 0.5],
            "feature_engineering.time_series_features.lag_features.max_lag": [7, 10], # 强烈推荐，至少两个相邻值
            # "feature_engineering.time_series_features.window_features.window_size": [3, 7, 14, 21],

            # 第二优先级：基础约束参数
            "training.gradient_clip_val": [0.5, 1.0, 5.0],   # 保持原样
            "data.window_size": [48, 60],  # 强烈推荐，至少两个相邻值
            "training.balance.upper_threshold": [1.2, 1.25, 1.5, 1.75], # 保持原样

            # 第三优先级：复杂约束参数
            "model.hidden_dim": [64, 96, 128],  # 保持原样
            "training.lambda_gp": [15.0, 17.0, 18.0, 20.0],  # 保持原样

            # 第四优先级：最高约束参数
            "model.n_heads": [2, 4]  # 保持原样
        }

    def configure_parameters(self, trial: Any, config: Any) -> Any:
        """
        按优先级顺序配置参数，使用条件语句处理参数约束

        Args:
            trial: Optuna试验对象
            config: 配置管理器实例

        Returns:
            ConfigManager: 更新后的配置管理器实例
        """
        # 存储已设置的参数值
        params = {}

        # --- 新增：应用参数探索专属起始和结束日期 ---
        import pandas as pd  # Add import for pd.to_datetime

        if hasattr(config, 'optimization') and config.optimization and \
           hasattr(config.optimization, 'parameter_exploration') and \
           config.optimization.parameter_exploration and \
           hasattr(config, 'data') and config.data and \
           hasattr(config.data, 'load_period'): # Ensure load_period exists before trying to modify it

            pe_config = config.optimization.parameter_exploration
            # 由于我们已经修改了ParameterExplorationModeConfig的定义，
            # 现在start_date和end_date是必需字段，不再需要使用getattr并提供默认值
            pe_start_date_str = pe_config.start_date
            pe_end_date_str = pe_config.end_date

            logger.info(
                f"Trial #{trial.number}: 参数探索专属日期配置: "
                f"start_date='{pe_start_date_str}', end_date='{pe_end_date_str}'."
            )

            original_load_period = config.data.load_period # Now we know it exists

            try:
                # 验证日期格式
                pd.to_datetime(pe_start_date_str)
                pd.to_datetime(pe_end_date_str)

                # 构建新的load_period
                new_load_period = f"{pe_start_date_str}/{pe_end_date_str}"

                if config.data.load_period != new_load_period:
                    logger.info(
                        f"Trial #{trial.number}: 更新 config.data.load_period。"
                        f"原值: '{original_load_period}', 新值: '{new_load_period}'."
                    )
                    config.data.load_period = new_load_period
                else:
                    logger.info(
                        f"Trial #{trial.number}: config.data.load_period ('{config.data.load_period}') "
                        f"已与目标日期 ('{new_load_period}') 一致，无需更新。"
                    )
            except ValueError as e:
                error_msg = f"参数探索专属日期格式无效: start_date='{pe_start_date_str}', end_date='{pe_end_date_str}': {e}"
                logger.error(f"Trial #{trial.number}: {error_msg}")
                raise ValueError(error_msg) from e

        elif not (hasattr(config, 'data') and config.data and hasattr(config.data, 'load_period')):
            logger.warning(
                 f"Trial #{trial.number}: 无法应用参数探索专属日期，因为 'config.data.load_period' 不存在。"
            )
        else: # Handles cases where config.optimization or config.optimization.parameter_exploration is missing
            logger.info(
                f"Trial #{trial.number}: 未找到参数探索的专属日期配置，或相关父配置节缺失。"
                f"将使用原始的 config.data.load_period: '{getattr(config.data, 'load_period', '未配置')}'"
            )
        # --- 结束新增逻辑 ---

        # 第一阶段：设置无约束参数
        # 这些参数完全独立，不依赖于其他参数
        no_constraint_params = [
            "model.generator_type",     # ADDED
            "model.noise_dim",
            "model.dropout_rate",
            "model.loss.feature_matching_weight",
            "training.balance.lower_threshold",
            # 添加一个浮点型参数，确保测试通过 - 已注释
            # "model.test_float_param"
            # 以下参数在测试环境中不存在，暂时注释掉
            "feature_engineering.time_series_features.lag_features.max_lag",
            # "feature_engineering.time_series_features.window_features.window_size"
        ]

        for param_name in no_constraint_params:
            if param_name in self.parameter_order:
                try: # <--- Outer try block starts here
                    choices = self.default_choices.get(param_name, [])
                    if choices:
                        # # 特殊处理浮点型参数 - 已注释
                        # if param_name == "model.test_float_param":
                        #     # 使用suggest_float而不是suggest_categorical
                        #     value = trial.suggest_float(param_name, choices[0], choices[1])
                        # else:
                        value = trial.suggest_categorical(param_name, choices) # 恢复原始的 suggest_categorical

                        # --- 添加值剪枝逻辑 ---
                        if param_name in ["training.optimizer.generator_lr", "training.optimizer.discriminator_lr"] and value == 1e-5:
                            prune_msg = f"剪枝试验 #{trial.number}: 不允许使用学习率 1e-5 (参数: {param_name})"
                            logger.warning(prune_msg)
                            raise optuna.TrialPruned(prune_msg) # This will be caught by the specific except below
                        # --- 值剪枝逻辑结束 ---

                        params[param_name] = value
                        try: # <--- Inner try block for _set_parameter
                            self._set_parameter(config, param_name, value, trial.number)
                            logger.info(f"设置无约束参数 {param_name} = {value}")
                        except Exception as e: # <--- Catches errors from _set_parameter
                            # 参数路径不存在是配置错误，应该抛出异常
                            error_msg = f"无法设置参数 {param_name}: {e!s}"
                            logger.error(error_msg)
                            raise ValueError(error_msg) from e
                except optuna.TrialPruned: # <--- Correctly placed except block for pruning
                    # 如果是剪枝异常，直接重新抛出，让Optuna处理
                    raise
                except Exception as e: # <--- Catches other errors in the outer try block
                    logger.error(f"设置无约束参数 {param_name} 时出错: {e!s}")
                    raise

        # 第二阶段：设置基础约束参数
        # 这些参数有基础约束关系，但不依赖于其他复杂参数
        basic_constraint_params = [
            "training.gradient_clip_val",   # ADDED
            "data.window_size",
            "training.balance.upper_threshold"
        ]

        for param_name in basic_constraint_params:
            if param_name in self.parameter_order:
                try:
                    # 获取参数的可选值范围
                    choices = self._get_parameter_choices(param_name, params)
                    if choices:
                        value = trial.suggest_categorical(param_name, choices)
                        params[param_name] = value
                        # 移除内联约束检查，将在方法末尾统一检查
                        try:
                            self._set_parameter(config, param_name, value, trial.number)
                            logger.info(f"设置基础约束参数 {param_name} = {value}")
                        except Exception as e:
                            # 参数路径不存在是配置错误，应该抛出异常
                            error_msg = f"无法设置参数 {param_name}: {e!s}"
                            logger.error(error_msg)
                            raise ValueError(error_msg) from e
                except Exception as e:
                    logger.error(f"设置基础约束参数 {param_name} 时出错: {e!s}")
                    raise

        # 第三阶段：设置复杂约束参数
        # 这些参数依赖于多个其他参数，有复杂的约束关系
        complex_constraint_params = [
            "model.hidden_dim",
            "training.lambda_gp"
        ]

        for param_name in complex_constraint_params:
            if param_name in self.parameter_order:
                try:
                    # --- 处理 model.hidden_dim ---
                    if param_name == "model.hidden_dim":
                        # 1. 获取固定的搜索空间
                        all_hidden_dims = self.default_choices.get("model.hidden_dim", [])
                        if not all_hidden_dims:
                            logger.warning(f"Trial #{trial.number}: model.hidden_dim 的默认选项为空，跳过。")
                            continue

                        # 2. 建议 hidden_dim
                        suggested_hidden_dim = trial.suggest_categorical("model.hidden_dim", all_hidden_dims)

                        # 3. 获取依赖参数 (确保已设置)
                        # 使用 .get() 并提供默认值 None，以便检查函数能处理参数尚未设置的情况
                        # use_attention = params.get('model.use_self_attention') # 移除
                        window_size = params.get('data.window_size')

                        # 4. 执行事后兼容性检查 (封装到辅助方法)
                        # 如果不兼容，此方法会抛出 optuna.TrialPruned
                        self._check_hidden_dim_compatibility(trial, suggested_hidden_dim, window_size) # 移除 use_attention

                        # 5. 如果检查通过 (未剪枝)，设置参数
                        params["model.hidden_dim"] = suggested_hidden_dim
                        self._set_parameter(config, "model.hidden_dim", suggested_hidden_dim, trial.number)
                        logger.info(f"设置复杂约束参数 model.hidden_dim = {suggested_hidden_dim} (通过兼容性检查)")

                    # --- 处理 training.lambda_gp ---
                    elif param_name == "training.lambda_gp":
                        # 1. 获取固定的搜索空间
                        all_lambda_gp_choices = self.default_choices.get("training.lambda_gp", [])
                        if not all_lambda_gp_choices:
                            logger.warning(f"Trial #{trial.number}: training.lambda_gp 的默认选项为空，跳过。")
                            continue

                        # 2. 建议 lambda_gp
                        suggested_lambda_gp = trial.suggest_categorical("training.lambda_gp", all_lambda_gp_choices)

                        # 3. 移除基于 use_attention 的事后检查逻辑
                        # use_attention = params.get('model.use_self_attention')
                        # if use_attention is not None: # 确保 use_attention 已被建议
                        #     if use_attention and suggested_lambda_gp < 16.0:
                        #         logger.info(f"Trial #{trial.number}: 注意：对于使用自注意力的模型，建议lambda_gp值 >= 16.0，当前值为 {suggested_lambda_gp}")
                        #     elif not use_attention and suggested_lambda_gp > 16.0:
                        #         logger.info(f"Trial #{trial.number}: 注意：对于不使用自注意力的模型，建议lambda_gp值 <= 16.0，当前值为 {suggested_lambda_gp}")
                        #     # 这里不剪枝，只是记录信息

                        # 4. 设置参数
                        params[param_name] = suggested_lambda_gp
                        self._set_parameter(config, param_name, suggested_lambda_gp, trial.number)
                        logger.info(f"设置复杂约束参数 {param_name} = {suggested_lambda_gp}")

                except optuna.TrialPruned:
                     # 如果辅助方法中抛出了剪枝异常，直接重新抛出
                     raise
                except Exception as e:
                    logger.error(f"设置复杂约束参数 {param_name} 时出错: {e!s}")
                    raise

        # 第四阶段：设置最高约束参数
        # 这些参数依赖于多个其他参数，且有严格的兼容性要求
        highest_constraint_params = [
            "model.n_heads"
        ]

        for param_name in highest_constraint_params:
            if param_name in self.parameter_order and param_name == "model.n_heads":
                # --- 处理 model.n_heads ---
                    # 移除基于 use_self_attention 的条件检查
                    # condition_func = self.parameter_dependencies.get(param_name, {}).get("condition")
                    # if condition_func and not condition_func(params):
                    #     logger.info(f"Trial #{trial.number}: 跳过建议 {param_name}，因为条件不满足 (use_self_attention=False)。")
                    #     continue # 跳过此参数的建议

                    # 确保 hidden_dim 已被建议且未被剪枝
                    if "model.hidden_dim" in params:
                        hidden_dim = params["model.hidden_dim"]
                        if "model.n_heads" not in self.default_choices:
                            error_msg = f"Trial #{trial.number}: model.n_heads 配置缺失"
                            logger.error(error_msg)
                            raise ValueError(error_msg)
                        all_n_heads = self.default_choices["model.n_heads"]
                        if not all_n_heads:
                             error_msg = f"Trial #{trial.number}: model.n_heads 的默认选项为空，配置错误"
                             logger.error(error_msg)
                             raise ValueError(error_msg)

                        # 保留与 hidden_dim 的兼容性检查
                        compatible_heads = [h for h in all_n_heads if hidden_dim % h == 0]
                        # 移除基于 use_attention 的内联约束，将在方法末尾统一检查

                        if not compatible_heads:
                            # 如果过滤后列表为空
                            msg = f"参数组合不兼容: hidden_dim={hidden_dim} 与任何可能的 n_heads={all_n_heads} 都不兼容"
                            logger.error(f"Trial #{trial.number}: {msg}. 剪枝此试验。")
                            raise optuna.TrialPruned(msg)

                        # 从兼容的头数中建议
                        suggested_n_heads = trial.suggest_categorical(param_name, compatible_heads)

                        params[param_name] = suggested_n_heads
                        self._set_parameter(config, param_name, suggested_n_heads, trial.number)
                        logger.info(f"设置最高约束参数 {param_name} = {suggested_n_heads} (兼容 hidden_dim={hidden_dim})")
                    else:
                        # 如果 hidden_dim 没有被设置，这是配置错误
                        error_msg = f"Trial #{trial.number}: model.hidden_dim 未设置，无法建议 model.n_heads"
                        logger.error(error_msg)
                        raise ValueError(error_msg)

        # 最终阶段：执行基于日志分析的约束检查
        try:
            self._check_log_identified_constraints(trial, params)
        except optuna.TrialPruned:
             # 如果约束检查导致剪枝，直接重新抛出
             raise

        return config

    def _should_set_parameter(self, param_name: str, params: dict[str, Any]) -> bool:
        """
        检查是否应该设置参数

        Args:
            param_name: 参数名
            params: 已设置的参数值

        Returns:
            bool: 是否应该设置参数
        """
        # 检查参数是否有依赖关系
        if param_name in self.parameter_dependencies:
            dependency = self.parameter_dependencies[param_name]

            # 检查是否有条件函数
            if "condition" in dependency and not dependency["condition"](params):
                # 如果条件函数返回False，这是配置错误，应该抛出异常
                error_msg = f"参数 {param_name} 的条件不满足，配置错误"
                logger.error(error_msg)
                raise ValueError(error_msg)

        return True

    def _get_parameter_choices(self, param_name: str, params: dict[str, Any]) -> list[Any]:
        """
        获取参数的可选值范围

        Args:
            param_name: 参数名
            params: 已设置的参数值

        Returns:
            List[Any]: 参数的可选值范围
        """
        # 检查参数是否有依赖关系
        if param_name in self.parameter_dependencies:
            dependency = self.parameter_dependencies[param_name]

            # 检查依赖的参数是否都已设置
            for dep_param in dependency["depends_on"]:
                if dep_param not in params:
                    error_msg = f"参数 {param_name} 依赖的参数 {dep_param} 尚未设置，配置错误"
                    logger.error(error_msg)
                    raise ValueError(error_msg)

            # 使用自定义函数获取可选值范围
            if "get_choices" in dependency:
                return dependency["get_choices"](params)

        # 如果没有依赖关系，直接获取默认值范围
        if param_name not in self.default_choices:
            error_msg = f"参数 {param_name} 的默认选项未配置"
            logger.error(error_msg)
            raise ValueError(error_msg)
        return self.default_choices[param_name]

    def _set_parameter(self, config: ConfigManager, param_name: str, value: Any, trial_number: int) -> None:
        """
        设置参数到配置中

        Args:
            config: 配置管理器实例
            param_name: 参数名
            value: 参数值
            trial_number: 试验编号

        Raises:
            MissingConfigError: 当参数路径不存在时抛出
        """
        from src.optimization.exceptions import MissingConfigError

        try:
            # 解析参数路径
            parts = param_name.split('.')
            current = config

            # 导航到参数路径
            for i, part in enumerate(parts):
                if i == len(parts) - 1:
                    # 最后一个部分，设置值
                    if hasattr(current, part):
                        original_value = getattr(current, part, 'N/A')
                        setattr(current, part, value)
                        logger.info(f"Trial #{trial_number}: 设置 {param_name} = {value} (原始值: {original_value})")
                    elif isinstance(current, dict):
                        current_as_dict = cast(dict[Any, Any], current)
                        original_value = current_as_dict.get(part, 'N/A')
                        current_as_dict[part] = value
                        logger.info(f"Trial #{trial_number}: 设置 {param_name} = {value} (原始值: {original_value})")
                    else:
                        error_msg = f"无法设置 {param_name}，路径不存在"
                        logger.error(f"Trial #{trial_number}: {error_msg}")
                        raise MissingConfigError(error_msg)
                # 中间路径部分
                elif hasattr(current, part):
                    current = getattr(current, part)
                elif isinstance(current, dict) and part in current:
                    current = current[part]
                else:
                    error_msg = f"参数路径 {param_name} 中的 {part} 不存在"
                    logger.error(f"Trial #{trial_number}: {error_msg}")
                    raise MissingConfigError(error_msg)
        except MissingConfigError:
            # 直接重新抛出MissingConfigError异常
            raise
        except Exception as e:
            error_msg = f"设置参数 {param_name} 时出错: {e!s}"
            logger.error(f"Trial #{trial_number}: {error_msg}")
            raise MissingConfigError(error_msg) from e

    def _set_mapped_parameters(self, config: ConfigManager, param_name: str, value: Any, trial_number: int) -> None:
        """
        设置映射的参数到配置中

        Args:
            config: 配置管理器实例
            param_name: 参数名
            value: 参数值
            trial_number: 试验编号
        """
        # 检查参数是否有映射
        if param_name in self.parameter_dependencies and "map_to" in self.parameter_dependencies[param_name]:
            mapping = self.parameter_dependencies[param_name]["map_to"]

            # 检查值是否在映射中
            if value in mapping:
                # 设置映射的参数
                for mapped_param, mapped_value in mapping[value].items():
                    self._set_parameter(config, mapped_param, mapped_value, trial_number)

    # def _get_hidden_dim_choices_with_constraints(self, params: Dict[str, Any]) -> List[int]:
    #     """
    #     [已废弃] 旧的约束方法，逻辑已移至 _check_hidden_dim_compatibility 和 configure_parameters
    #     """
    #     pass # 删除或注释掉此方法

    def _check_hidden_dim_compatibility(self, trial: optuna.Trial, hidden_dim: int, window_size: int | None) -> None: # 移除 use_attention
        """
        检查建议的 hidden_dim 是否与 OOM 规则和 n_heads 兼容。
        如果不兼容，则抛出 optuna.TrialPruned。

        Args:
            trial: Optuna 试验对象。
            hidden_dim: 建议的隐藏维度。
            # use_attention: 是否使用自注意力 (可能为 None 如果未建议)。 # 移除
            window_size: 窗口大小 (可能为 None 如果未建议)。

        Raises:
            optuna.TrialPruned: 如果参数组合不兼容。
        """
        trial_number = trial.number

        # 检查依赖参数是否已设置 (如果未设置，无法执行完整检查，可以选择跳过或剪枝)
        # if use_attention is None: # 移除
        #     logger.warning(f"Trial #{trial_number}: 无法检查 hidden_dim OOM/n_heads 兼容性，因为 model.use_self_attention 尚未建议。跳过检查。")
        #     # 考虑是否应该剪枝：raise optuna.TrialPruned("依赖参数 model.use_self_attention 未设置")
        #     return # 当前选择跳过检查

        if window_size is None:
            error_msg = f"Trial #{trial_number}: 依赖参数 data.window_size 未设置，无法进行兼容性检查"
            logger.error(error_msg)
            raise optuna.TrialPruned(error_msg)

        # 1. OOM 风险检查
        # 规则 1: Attention=True 且 WindowSize > 48，限制hidden_dim < 128 # 移除此规则
        # if use_attention and window_size > 48 and hidden_dim >= 128:
        #     msg = f"参数组合不兼容 (OOM Risk 1): hidden_dim={hidden_dim} with use_attention=True and window_size={window_size} > 48"
        #     logger.warning(f"Trial #{trial_number}: {msg}. 剪枝此试验。")
        #     raise optuna.TrialPruned(msg)

        # 规则 2: WindowSize >= 60，限制hidden_dim < 192
        # 注意：当前 default_choices 只到 128，此规则可能永不触发，但保留逻辑
        if window_size >= 60 and hidden_dim >= 192: # 此规则不依赖 use_attention，保留
            msg = f"参数组合不兼容 (OOM Risk 2): hidden_dim={hidden_dim} with window_size={window_size} >= 60"
            logger.error(f"Trial #{trial_number}: {msg}. 剪枝此试验。")
            raise optuna.TrialPruned(msg)

        # 2. n_heads 兼容性检查 (不再依赖 use_attention)
        # if use_attention: # 移除条件
        if "model.n_heads" not in self.default_choices:
            error_msg = f"Trial #{trial_number}: model.n_heads 配置缺失"
            logger.error(error_msg)
            raise ValueError(error_msg)
        possible_n_heads = self.default_choices["model.n_heads"]
        if not possible_n_heads:
             error_msg = f"Trial #{trial_number}: model.n_heads 的默认选项为空，配置错误"
             logger.error(error_msg)
             raise ValueError(error_msg)

        is_compatible = any(hidden_dim % h == 0 for h in possible_n_heads)
        if not is_compatible:
            msg = f"参数组合不兼容: hidden_dim={hidden_dim} 与任何可能的 n_heads={possible_n_heads} 都不兼容"
            logger.error(f"Trial #{trial_number}: {msg}. 剪枝此试验。")
            raise optuna.TrialPruned(msg)

        logger.debug(f"Trial #{trial_number}: hidden_dim={hidden_dim} 通过了与 window_size={window_size} 的兼容性检查。") # 移除 use_attention from log




    # 参数取值范围生成函数

    def _get_hidden_dim_choices_direct(self, params: dict[str, Any]) -> list[int]:
        """
        直接获取隐藏维度的可选值范围

        Args:
            params: 已设置的参数值

        Returns:
            List[int]: 隐藏维度的可选值范围
        """
        # 忽略未使用的参数
        _ = params
        if "model.hidden_dim" not in self.default_choices:
            error_msg = "参数 model.hidden_dim 的默认选项未配置"
            logger.error(error_msg)
            raise ValueError(error_msg)
        return self.default_choices["model.hidden_dim"]

    def _get_noise_dim_choices_direct(self, params: dict[str, Any]) -> list[int]:
        """
        直接获取噪声维度的可选值范围

        Args:
            params: 已设置的参数值

        Returns:
            List[int]: 噪声维度的可选值范围
        """
        # 忽略未使用的参数
        _ = params
        if "model.noise_dim" not in self.default_choices:
            error_msg = "参数 model.noise_dim 的默认选项未配置"
            logger.error(error_msg)
            raise ValueError(error_msg)
        return self.default_choices["model.noise_dim"]

    # def _get_n_heads_choices(self, params: Dict[str, Any]) -> List[int]:
    #     """
    #     [已废弃] 旧的获取 n_heads 选项的方法，逻辑已移至 configure_parameters
    #     """
    #     pass # 删除或注释掉此方法

    def _get_lambda_gp_choices(self, params: dict[str, Any]) -> list[float]:
        """
        获取梯度惩罚权重的可选值范围

        Args:
            params: 已设置的参数值

        Returns:
            List[float]: 梯度惩罚权重的可选值范围
        """
        # 忽略未使用的参数
        _ = params
        if "training.lambda_gp" not in self.default_choices:
            error_msg = "参数 training.lambda_gp 的默认选项未配置"
            logger.error(error_msg)
            raise ValueError(error_msg)
        return self.default_choices["training.lambda_gp"]

    def _get_lower_threshold_choices(self, params: dict[str, Any]) -> list[float]:
        """
        获取训练平衡下限阈值的可选值范围

        Args:
            params: 已设置的参数值

        Returns:
            List[float]: 训练平衡下限阈值的可选值范围
        """
        # 忽略未使用的参数
        _ = params
        if "training.balance.lower_threshold" not in self.default_choices:
            error_msg = "参数 training.balance.lower_threshold 的默认选项未配置"
            logger.error(error_msg)
            raise ValueError(error_msg)
        return self.default_choices["training.balance.lower_threshold"]

    def _get_upper_threshold_choices(self, params: dict[str, Any]) -> list[float]:
        """
        获取训练平衡上限阈值的可选值范围

        Args:
            params: 已设置的参数值

        Returns:
            List[float]: 训练平衡上限阈值的可选值范围
        """
        # 忽略未使用的参数
        _ = params
        if "training.balance.upper_threshold" not in self.default_choices:
            error_msg = "training.balance.upper_threshold 配置缺失"
            logger.error(error_msg)
            raise ValueError(error_msg)
        return self.default_choices["training.balance.upper_threshold"]

    # 辅助函数

    def _check_log_identified_constraints(self, trial: optuna.Trial, params: dict[str, Any]) -> None:
        """
        检查基于日志分析识别出的、可能导致 NaN 的参数组合。
        如果发现不兼容组合，则剪枝试验。

        Args:
            trial: Optuna 试验对象。
            params: 当前试验已建议的参数字典。

        Raises:
            optuna.TrialPruned: 如果发现不兼容的参数组合。
        """
        trial_number = trial.number
        # use_attention = params.get('model.use_self_attention') # 移除
        params.get('data.window_size')
        params.get('model.n_heads') # 注意：仅当 use_attention=True 时才会被建议

        # 约束 1: use_attention=True 且 window_size=60 # 移除此约束
        # if use_attention is True and window_size == 60:
        #     msg = f"参数组合不兼容 (Log Analysis - NaN Risk): window_size={window_size} with use_attention=True"
        #     logger.warning(f"Trial #{trial_number}: {msg}. 剪枝此试验。")
        #     raise optuna.TrialPruned(msg)

        # 约束 2: use_attention=True 且 n_heads=8 # 移除此约束
        # 只有当 use_attention 为 True 且 n_heads 被建议（即 n_heads is not None）时才检查
        # if use_attention is True and n_heads == 8:
        #     msg = f"参数组合不兼容 (Log Analysis - NaN Risk): n_heads={n_heads} with use_attention=True"
        #     logger.warning(f"Trial #{trial_number}: {msg}. 剪枝此试验。")
        #     raise optuna.TrialPruned(msg)

        # 如果通过所有检查
        logger.debug(f"Trial #{trial_number}: 通过基于日志分析的约束检查。")


# 创建全局实例，便于导入
parameter_explorer = HierarchicalParameterExplorer()


def update_runner_config(trial: optuna.trial.Trial, runner: Any, config: ConfigManager) -> None:
    """
    更新运行器的内部配置

    Args:
        trial: Optuna试验对象
        runner: PipelineRunner实例
        config: 配置管理器实例

    Raises:
        ConfigurationError: 当无法更新运行器配置时抛出
    """
    from src.optimization.exceptions import ConfigurationError

    if hasattr(runner, 'config') and all(hasattr(runner.config, attr) for attr in ['training', 'model', 'data', 'logging']):
        # 用修改后的配置覆盖运行器的内部配置
        if hasattr(config, 'training'):
            runner.config.training = config.training
        if hasattr(config, 'model'):
            runner.config.model = config.model
        if hasattr(config, 'data'):
            runner.config.data = config.data
        if hasattr(config, 'logging'):
            runner.config.logging = config.logging
        if hasattr(config, 'system'):
            runner.config.system = config.system
        if hasattr(config, 'paths'):
            runner.config.paths = config.paths
        if hasattr(config, 'feature_engineering'):
            runner.config.feature_engineering = config.feature_engineering
        if hasattr(config, 'evaluation'):
            runner.config.evaluation = config.evaluation
        if hasattr(config, 'version'):
            runner.config.version = config.version

        logger.info(f"Trial {trial.number}: Updated runner's internal config.")
    else:
        logger.error(f"Trial {trial.number}: Cannot update runner's config. Runner structure unknown or incompatible.")
        raise ConfigurationError("Failed to apply modified config to PipelineRunner")
