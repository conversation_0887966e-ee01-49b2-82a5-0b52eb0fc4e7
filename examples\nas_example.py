"""神经架构搜索示例脚本

使用示例：
python examples/nas_example.py --strategy evolutionary --max_iterations 30 --time_budget 8

功能演示：
1. 自动搜索最优GAN架构
2. 与现有训练流程集成
3. 性能对比和结果分析
"""

import argparse
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
from torch.utils.data import DataLoader

from src.data.data_pipeline import DataPipeline
from src.optimization.nas.integration import GANTrainerWithNAS
from src.utils.config_manager import ConfigManager
from src.utils.logger import get_logger


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="神经架构搜索示例")

    # NAS配置参数
    parser.add_argument('--strategy', type=str, default='evolutionary',
                       choices=['evolutionary', 'darts'],
                       help='搜索策略 (默认: evolutionary)')

    parser.add_argument('--max_iterations', type=int, default=30,
                       help='最大搜索迭代次数 (默认: 30)')

    parser.add_argument('--time_budget', type=float, default=8.0,
                       help='搜索时间预算 (小时, 默认: 8.0)')

    parser.add_argument('--population_size', type=int, default=15,
                       help='进化算法种群大小 (默认: 15)')

    parser.add_argument('--enable_nas', action='store_true', default=True,
                       help='启用NAS功能')

    parser.add_argument('--baseline_epochs', type=int, default=5,
                       help='基线模型训练轮数 (默认: 5)')

    parser.add_argument('--final_epochs', type=int, default=20,
                       help='最终模型训练轮数 (默认: 20)')

    # 数据和配置参数
    parser.add_argument('--config_file', type=str, default='config.yaml',
                       help='配置文件路径')

    parser.add_argument('--output_dir', type=str, default='outputs/nas_example',
                       help='输出目录')

    parser.add_argument('--log_level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')

    return parser.parse_args()


def setup_logging(log_level: str):
    """设置日志"""
    import logging
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def validate_nas_config(config: ConfigManager) -> None:
    """验证NAS配置的完整性"""
    if config.nas is None:
        raise ValueError("配置文件中必须包含NAS配置部分")

    logger = get_logger("NASValidation")
    logger.info("NAS配置验证通过")
    logger.info(f"搜索策略: {config.nas.manager.search_strategy}")
    logger.info(f"最大迭代: {config.nas.manager.max_iterations}")
    logger.info(f"时间预算: {config.nas.manager.time_budget_hours}小时")


def prepare_data(config: ConfigManager) -> tuple[DataLoader, DataLoader]:
    """准备训练和验证数据"""
    logger = get_logger("DataPreparation")
    logger.info("开始准备数据")

    # 创建数据流水线
    data_pipeline = DataPipeline(config.data.data_path, config)

    # 加载和处理数据
    processed_data = data_pipeline.run_pipeline()

    # 从处理后的数据创建数据集
    from torch.utils.data import Dataset

    class DictDataset(Dataset):
        """返回字典格式数据的数据集，支持 target_standardizer"""
        def __init__(self, features, targets, target_standardizer=None):
            self.features = features
            self.targets = targets
            self._target_standardizer = target_standardizer

        def __len__(self):
            return len(self.features)

        def __getitem__(self, idx):
            return {
                'features': self.features[idx],
                'target': self.targets[idx]
            }

        def get_target_standardizer(self):
            """获取目标标准化器"""
            return self._target_standardizer

        @property
        def target_standardizer(self):
            """目标标准化器属性"""
            return self._target_standardizer

    # 分割数据为训练集和验证集
    features = processed_data['features']
    targets = processed_data['targets']

    # 获取目标标准化器
    target_standardizer = data_pipeline.target_standardizer
    logger.info(f"从数据流水线获取 target_standardizer: "
               f"{'已获取' if target_standardizer is not None else '未获取'}")

    # 简单的70/30分割
    split_idx = int(0.7 * len(features))

    train_features = features[:split_idx]
    train_targets = targets[:split_idx]
    val_features = features[split_idx:]
    val_targets = targets[split_idx:]

    # 创建数据集，传递 target_standardizer
    train_dataset = DictDataset(train_features, train_targets, target_standardizer)
    val_dataset = DictDataset(val_features, val_targets, target_standardizer)

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        num_workers=0,  # Windows兼容性
        pin_memory=torch.cuda.is_available()
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        num_workers=0,
        pin_memory=torch.cuda.is_available()
    )

    logger.info(f"数据准备完成 - 训练集: {len(train_loader)} batches, "
               f"验证集: {len(val_loader)} batches")

    return train_loader, val_loader


def run_nas_experiment(args):
    """运行NAS实验"""
    logger = get_logger("NASExperiment")
    logger.info("开始NAS实验")

    # 加载配置
    config = ConfigManager.from_yaml(args.config_file)

    # 验证NAS配置
    validate_nas_config(config)

    # 准备数据
    train_loader, val_loader = prepare_data(config)

    # 创建NAS训练器（配置已包含在config中）
    trainer = GANTrainerWithNAS(
        config=config,
        train_loader=train_loader,
        val_loader=val_loader,
        enable_nas=args.enable_nas,
        baseline_epochs=args.baseline_epochs
    )

    # target_standardizer 现在会自动从数据加载器中提取
    # 如果自动提取失败，会在日志中显示警告
    logger.info(f"GANTrainerWithNAS 已初始化，target_standardizer 状态: "
               f"{'已设置' if trainer.target_standardizer is not None else '未设置'}")

    # 执行训练
    logger.info("开始NAS增强训练")
    results = trainer.train_with_nas(epochs=args.final_epochs)

    # 保存结果
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    trainer.save_nas_results(str(output_dir))

    # 分析结果
    analyze_results(results, logger)

    logger.info("NAS实验完成")
    return results


def analyze_results(results: dict, logger):
    """分析实验结果"""
    logger.info("=== NAS实验结果分析 ===")

    if results.get('nas_enabled', False):
        # NAS启用的情况
        performance = results.get('performance_summary', {})
        baseline_mae = performance.get('baseline_mae', 'N/A')
        final_mae = performance.get('final_mae', 'N/A')
        improvement = performance.get('improvement_achieved', 0)
        search_success = performance.get('search_success', False)

        logger.info(f"基线模型MAE: {baseline_mae}")
        logger.info(f"优化后MAE: {final_mae}")
        logger.info(f"性能改进: {improvement:.2f}%")
        logger.info(f"架构搜索成功: {search_success}")

        # 搜索统计
        search_results = results.get('search_results', {})
        total_iterations = search_results.get('total_iterations', 0)
        search_time = search_results.get('search_time_hours', 0)

        logger.info(f"搜索迭代次数: {total_iterations}")
        logger.info(f"搜索耗时: {search_time:.2f}小时")

        # 架构信息
        if 'best_architecture' in results:
            arch = results['best_architecture']
            gen_config = arch.get('generator', {})
            disc_config = arch.get('discriminator', {})

            logger.info("=== 最佳架构配置 ===")
            logger.info(f"生成器层数: {gen_config.get('num_layers', 'N/A')}")
            logger.info(f"生成器隐藏维度: {gen_config.get('hidden_dim', 'N/A')}")
            logger.info(f"注意力头数: {gen_config.get('num_attention_heads', 'N/A')}")
            logger.info(f"判别器层数: {disc_config.get('num_layers', 'N/A')}")
            logger.info(f"判别器隐藏维度: {disc_config.get('hidden_dim', 'N/A')}")
    else:
        # 标准训练的情况
        logger.info("使用标准训练 (NAS未启用)")
        best_mae = results.get('best_val_mae', 'N/A')
        logger.info(f"最佳验证MAE: {best_mae}")

    total_time = results.get('total_time_hours', 0)
    logger.info(f"总训练时间: {total_time:.2f}小时")


def main():
    """主函数"""
    args = parse_arguments()

    # 设置日志
    setup_logging(args.log_level)

    logger = get_logger("Main")
    logger.info("启动神经架构搜索示例")
    logger.info(f"参数配置: {vars(args)}")

    try:
        # 运行实验
        results = run_nas_experiment(args)

        logger.info("实验成功完成")
        return 0

    except KeyboardInterrupt:
        logger.info("用户中断实验")
        return 1

    except Exception as e:
        logger.error(f"实验失败: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
