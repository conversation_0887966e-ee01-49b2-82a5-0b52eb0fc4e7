"""监控配置模块

定义监控相关的配置类，包括模型统计监控和错误监控的配置。
"""
from dataclasses import dataclass
from typing import Optional


@dataclass
class MonitoringConfig:
    """监控配置类"""

    # ModelStatsMonitor 配置
    enable_model_stats: bool = False
    stats_log_frequency: int = 10
    stats_detailed_frequency: int = 100
    stats_log_path: Optional[str] = None
    track_weights: bool = True
    track_gradients: bool = True
    track_activations: bool = False
    nan_detection: bool = True

    # ErrorMonitor 配置
    enable_error_monitor: bool = False
    error_log_path: Optional[str] = None
    enable_nan_detection_in_error_monitor: bool = True
    enable_traceback: bool = True
    max_errors: int = 5
    error_cooldown: int = 60

    def to_dict(self) -> dict:
        """将配置转换为字典格式

        Returns:
            dict: 配置字典
        """
        from dataclasses import asdict
        return asdict(self)
