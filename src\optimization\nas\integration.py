"""NAS集成模块 - 将神经架构搜索集成到GANTrainer训练流程

模块路径: src/optimization/nas/integration.py

功能说明：
1. GANTrainerWithNAS: 集成NAS功能的训练器
2. 自动架构搜索和模型优化
3. 与现有训练流程无缝集成
4. 支持渐进式搜索策略
"""

from __future__ import annotations

import time
from pathlib import Path
from typing import Any

import torch
from torch.utils.data import DataLoader

from src.models.gan.gan_model import GANModel
from src.models.gan.trainer import GANTrainer
from src.utils.config_manager import ConfigManager
from src.utils.logger import get_logger

from .manager import NASConfig, NASManager, SearchResult
from .search_space import ArchitectureConfig


class GANTrainerWithNAS(GANTrainer):
    """集成NAS功能的GAN训练器"""

    def __init__(self,
                 config: ConfigManager,
                 train_loader: DataLoader,
                 val_loader: DataLoader,
                 nas_config: NASConfig | None = None,
                 enable_nas: bool = True,
                 baseline_epochs: int = 5):
        """
        Args:
            config: 基础配置管理器
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            nas_config: NAS搜索配置
            enable_nas: 是否启用NAS功能
            baseline_epochs: 基线模型训练轮数
        """
        # 先初始化 logger，然后提取 target_standardizer
        self.logger = get_logger("GANTrainerWithNAS")

        # 自动从数据加载器提取 target_standardizer
        target_standardizer = self._extract_target_standardizer(train_loader)

        # 初始化基础训练器，传递 target_standardizer
        super().__init__(config, None, train_loader, val_loader, None, target_standardizer)
        self.enable_nas = enable_nas
        self.baseline_epochs = baseline_epochs

        # 设置默认NAS配置
        self.nas_config = nas_config or self._create_default_nas_config()

        # NAS相关属性
        self.nas_manager: NASManager | None = None
        self.baseline_mae: float | None = None
        self.optimized_model: GANModel | None = None
        self.search_result: SearchResult | None = None

        self.logger.info(f"NAS集成训练器初始化完成 - NAS启用: {enable_nas}")

    def _extract_target_standardizer(self, train_loader):
        """从数据加载器中提取 target_standardizer

        Args:
            train_loader: 训练数据加载器

        Returns:
            target_standardizer: 目标标准化器实例，如果无法获取则返回None
        """
        target_standardizer = None

        try:
            # 方法1: 从 TimeSeriesDataLoader 获取
            if hasattr(train_loader, 'get_target_standardizer'):
                target_standardizer = train_loader.get_target_standardizer()
                if target_standardizer is not None:
                    self.logger.info("成功从 TimeSeriesDataLoader.get_target_standardizer() 获取 target_standardizer")
                    return target_standardizer

            # 方法2: 从底层数据集获取
            if hasattr(train_loader, '_dataset') and train_loader._dataset is not None:
                dataset = train_loader._dataset
                if hasattr(dataset, 'get_target_standardizer'):
                    target_standardizer = dataset.get_target_standardizer()
                    if target_standardizer is not None:
                        self.logger.info("成功从数据集的 get_target_standardizer() 获取 target_standardizer")
                        return target_standardizer

                # 方法3: 从数据集的 target_standardizer 属性获取
                if hasattr(dataset, 'target_standardizer'):
                    target_standardizer = dataset.target_standardizer
                    if target_standardizer is not None:
                        self.logger.info("成功从数据集的 target_standardizer 属性获取 target_standardizer")
                        return target_standardizer

            # 方法4: 从 PyTorch DataLoader 的数据集获取
            if hasattr(train_loader, 'dataset') and train_loader.dataset is not None:
                dataset = train_loader.dataset
                if hasattr(dataset, 'get_target_standardizer'):
                    target_standardizer = dataset.get_target_standardizer()
                    if target_standardizer is not None:
                        self.logger.info("成功从 PyTorch DataLoader 数据集获取 target_standardizer")
                        return target_standardizer

                if hasattr(dataset, 'target_standardizer'):
                    target_standardizer = dataset.target_standardizer
                    if target_standardizer is not None:
                        self.logger.info("成功从 PyTorch DataLoader 数据集属性获取 target_standardizer")
                        return target_standardizer

            # 如果所有方法都失败，记录警告
            self.logger.warning(
                "无法从数据加载器中自动提取 target_standardizer。"
                "这可能导致 GANEvaluator 无法正确进行逆标准化。"
                "请考虑手动设置 trainer.target_standardizer。"
            )
            return None

        except Exception as e:
            self.logger.error(f"提取 target_standardizer 时发生错误: {e}")
            return None

    def train_with_nas(self, epochs: int | None = None) -> dict[str, Any]:
        """执行完整的训练流程 (包含NAS)"""
        if not self.enable_nas:
            # 如果未启用NAS，使用标准训练
            super().train(mode=True)
            return {"nas_enabled": False, "message": "NAS未启用，使用标准训练"}

        self.logger.info("开始NAS增强的训练流程")
        start_time = time.time()

        try:
            # 阶段1: 基线模型训练
            baseline_results = self._train_baseline_model()

            # 阶段2: 神经架构搜索
            search_results = self._run_architecture_search()

            # 阶段3: 优化模型训练
            final_results = self._train_optimized_model(epochs)

            # 整合结果
            total_time = time.time() - start_time
            integrated_results = self._integrate_results(
                baseline_results, search_results, final_results, total_time
            )

            self.logger.info(f"NAS增强训练完成 - 总耗时: {total_time/3600:.2f}小时")
            return integrated_results

        except Exception as e:
            self.logger.error(f"NAS增强训练失败: {e}", exc_info=True)
            # 回退到标准训练
            return self._fallback_training(epochs)

    def _create_default_nas_config(self) -> NASConfig:
        """创建默认NAS配置"""
        return NASConfig(
            search_strategy="evolutionary",
            max_iterations=30,  # 减少迭代次数以满足时间约束
            time_budget_hours=8.0,  # 不超过现有训练时间的3倍
            max_epochs_per_eval=5,  # 减少每次评估的轮数
            early_stop_patience=2,
            population_size=15,  # 减少种群大小
            mutation_rate=0.3,
            crossover_rate=0.7,
            experiment_name="gan_timeseries_nas"
        )

    def _train_baseline_model(self) -> dict[str, Any]:
        """训练基线模型"""
        self.logger.info(f"开始基线模型训练 - {self.baseline_epochs}轮")

        # 使用当前配置创建基线模型
        if self.model is None:
            feature_dim = self._get_feature_dim_from_data()
            self.model = GANModel(
                config=self.config_manager,
                feature_dim=feature_dim,
                window_size=self.config_manager.data.window_size
            )

        # 训练基线模型
        baseline_results = super().run_training_loop(
            batch_size=self.config_manager.training.batch_size,
            num_epochs=self.baseline_epochs
        )

        # 评估基线性能
        self.baseline_mae = self._evaluate_baseline_mae()

        self.logger.info(f"基线模型训练完成 - MAE: {self.baseline_mae:.4f}")
        return baseline_results

    def _evaluate_baseline_mae(self) -> float:
        """评估基线模型的MAE (使用反标准化数据)"""
        try:
            self.model.eval()
            total_mae = 0.0
            num_batches = 0

            with torch.no_grad():
                if self.val_loader is None:
                    raise ValueError("验证数据加载器未设置")
                for batch in self.val_loader:
                    features = batch['features'].to(self.device)
                    targets = batch['target'].to(self.device)

                    predictions = self.model(features)

                    # 计算标准化空间的MAE (用于评分)
                    mae = torch.mean(torch.abs(predictions - targets)).item()
                    self.logger.debug(f"基线MAE计算 - 标准化空间: {mae:.4f}")

                    # 同时计算反标准化MAE (用于日志记录和对比)
                    if hasattr(self, 'target_standardizer') and self.target_standardizer is not None:
                        try:
                            # 反标准化预测值
                            batch_size, seq_len, _ = predictions.shape
                            inv_predictions = self.target_standardizer.inverse_transform(
                                predictions.view(-1, 1).to(self.device),
                                is_normalized=True
                            ).view(batch_size, seq_len, -1)

                            # 反标准化目标值
                            inv_targets = self.target_standardizer.inverse_transform(
                                targets.view(-1, 1).to(self.device),
                                is_normalized=True
                            ).view(batch_size, seq_len, -1)

                            # 在反标准化空间计算MAE (仅用于日志)
                            inv_mae = torch.mean(torch.abs(inv_predictions - inv_targets)).item()
                            self.logger.debug(f"基线MAE计算 - 反标准化: {inv_mae:.4f} (仅供参考)")
                        except Exception as e:
                            self.logger.warning(f"反标准化MAE计算失败: {e}")
                    else:
                        self.logger.debug("target_standardizer不可用，跳过反标准化MAE计算")

                    total_mae += mae
                    num_batches += 1

            avg_mae = total_mae / max(num_batches, 1)
            self.logger.info(f"基线MAE评估完成 - 平均MAE: {avg_mae:.4f} (共{num_batches}个批次)")
            return avg_mae

        except Exception as e:
            self.logger.error(f"评估基线MAE失败: {e}")
            raise RuntimeError(f"评估基线MAE失败: {e}") from e

    def _run_architecture_search(self) -> dict[str, Any]:
        """运行架构搜索"""
        self.logger.info("开始神经架构搜索")

        # 创建NAS管理器
        if self.train_loader is None or self.val_loader is None:
            raise ValueError("训练和验证数据加载器必须在NAS搜索前设置")

        self.nas_manager = NASManager(
            base_config=self.config_manager,
            train_loader=self.train_loader,
            val_loader=self.val_loader,
            baseline_mae=self.baseline_mae,
            target_standardizer=self.target_standardizer
        )

        # 执行搜索
        self.search_result = self.nas_manager.run_search()

        search_summary = {
            'search_success': self.search_result.success,
            'best_mae': self.search_result.get_best_mae(),
            'total_iterations': self.search_result.total_iterations,
            'search_time_hours': self.search_result.total_time_hours,
            'improvement_percent': self.search_result.get_improvement_over_baseline(
                self.baseline_mae) if self.baseline_mae else 0
        }

        self.logger.info(f"架构搜索完成 - 最佳MAE: {search_summary['best_mae']:.4f}, "
                        f"改进: {search_summary['improvement_percent']:.2f}%")

        return search_summary

    def _train_optimized_model(self, epochs: int | None) -> dict[str, Any]:
        """训练优化后的模型"""
        if not self.search_result or not self.search_result.success:
            self.logger.warning("架构搜索失败，使用基线模型继续训练")
            return super().run_training_loop(
                batch_size=self.config_manager.training.batch_size,
                num_epochs=epochs
            )

        self.logger.info("开始训练优化后的模型")

        # 创建优化模型
        if not self.search_result.best_architecture:
            raise ValueError("搜索结果中没有最佳架构")

        if self.nas_manager is not None:
            self.optimized_model = self.nas_manager.create_optimized_model(
                self.search_result.best_architecture
            )
        else:
            raise ValueError("NAS管理器未初始化")

        if self.optimized_model is None:
            self.logger.warning("创建优化模型失败，使用基线模型")
            return super().run_training_loop(
                batch_size=self.config_manager.training.batch_size,
                num_epochs=epochs
            )

        # 替换当前模型
        old_model = self.model
        self.model = self.optimized_model

        # 重新初始化优化器
        self._reinitialize_optimizers()

        # 训练优化模型
        try:
            optimized_results = super().run_training_loop(
                batch_size=self.config_manager.training.batch_size,
                num_epochs=epochs
            )

            # 评估优化后的性能
            optimized_mae = self._evaluate_baseline_mae()

            # 创建结果字典
            result_dict: dict[str, Any] = dict(optimized_results)
            result_dict['optimized_mae'] = optimized_mae
            if self.search_result and self.search_result.best_architecture:
                result_dict['architecture_config'] = self.search_result.best_architecture.to_dict()

            self.logger.info(f"优化模型训练完成 - MAE: {optimized_mae:.4f}")
            return result_dict

        except Exception as e:
            self.logger.error(f"优化模型训练失败: {e}")
            # 恢复基线模型
            self.model = old_model
            self._reinitialize_optimizers()
            return super().run_training_loop(
                batch_size=self.config_manager.training.batch_size,
                num_epochs=epochs
            )

    def _reinitialize_optimizers(self):
        """重新初始化优化器"""
        try:
            # 重新创建优化器管理器
            from src.utils.optimizer_manager import OptimizerManager
            self.optimizer_manager = OptimizerManager(self.config_manager)

            # 创建优化器
            self.g_optimizer = self.optimizer_manager.create_optimizer(
                self.model.generator, model_type='generator'
            )
            self.d_optimizer = self.optimizer_manager.create_optimizer(
                self.model.discriminator, model_type='discriminator'
            )

            # 设置模型优化器
            self.model.set_optimizers(self.g_optimizer, self.d_optimizer)

            self.logger.info("优化器重新初始化完成")

        except Exception as e:
            self.logger.error(f"重新初始化优化器失败: {e}")

    def _integrate_results(self,
                          baseline_results: dict[str, Any],
                          search_results: dict[str, Any],
                          final_results: dict[str, Any],
                          total_time: float) -> dict[str, Any]:
        """整合所有阶段的结果"""
        integrated = {
            'nas_enabled': True,
            'total_time_hours': total_time / 3600,
            'baseline_results': baseline_results,
            'search_results': search_results,
            'final_results': final_results,
            'performance_summary': {
                'baseline_mae': self.baseline_mae,
                'final_mae': final_results.get('optimized_mae', final_results.get('best_val_mae')),
                'improvement_achieved': search_results.get('improvement_percent', 0),
                'search_success': search_results.get('search_success', False)
            }
        }

        # 添加最佳架构信息
        if self.search_result and self.search_result.success and self.search_result.best_architecture:
            integrated['best_architecture'] = self.search_result.best_architecture.to_dict()
            integrated['architecture_metrics'] = self.search_result.best_metrics

        return integrated

    def _fallback_training(self, epochs: int | None) -> dict[str, Any]:
        """回退到标准训练"""
        self.logger.info("执行回退训练策略")

        fallback_results = super().run_training_loop(
            batch_size=self.config_manager.training.batch_size,
            num_epochs=epochs
        )

        # 创建结果字典
        result_dict: dict[str, Any] = dict(fallback_results)
        result_dict['nas_enabled'] = False
        result_dict['fallback_reason'] = "NAS训练失败"

        return result_dict

    def _get_feature_dim_from_data(self) -> int:
        """从数据中获取特征维度"""
        try:
            if self.train_loader is None:
                return 19
            batch = next(iter(self.train_loader))
            features = batch['features']
            return features.shape[-1]
        except Exception as e:
            self.logger.error(f"无法从数据获取特征维度: {e}")
            raise RuntimeError(f"无法从数据获取特征维度: {e}") from e

    def save_nas_results(self, save_dir: str):
        """保存NAS结果"""
        if not self.search_result:
            self.logger.warning("没有NAS结果可保存")
            return

        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)

        try:
            # 保存搜索结果摘要
            summary_file = save_path / "nas_summary.json"
            summary_data = {
                'search_success': self.search_result.success,
                'best_mae': self.search_result.get_best_mae(),
                'baseline_mae': self.baseline_mae,
                'improvement_percent': self.search_result.get_improvement_over_baseline(self.baseline_mae or 1.0),
                'total_iterations': self.search_result.total_iterations,
                'search_time_hours': self.search_result.total_time_hours,
                'nas_config': self.nas_config.__dict__
            }

            import json
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, indent=2, ensure_ascii=False)

            # 保存最佳架构
            if self.search_result.best_architecture:
                arch_file = save_path / "best_architecture.json"
                with open(arch_file, 'w', encoding='utf-8') as f:
                    json.dump(self.search_result.best_architecture.to_dict(), f, indent=2)

            self.logger.info(f"NAS结果已保存到: {save_path}")

        except Exception as e:
            self.logger.error(f"保存NAS结果失败: {e}")

    def load_optimized_architecture(self, arch_file: str) -> bool:
        """加载优化的架构配置"""
        try:
            import json
            with open(arch_file, 'r', encoding='utf-8') as f:
                arch_dict = json.load(f)

            architecture = ArchitectureConfig.from_dict(arch_dict)

            # 创建优化模型
            if self.nas_manager is None:
                if self.train_loader is None or self.val_loader is None:
                    raise ValueError("训练和验证数据加载器必须在加载架构前设置")

                self.nas_manager = NASManager(
                    base_config=self.config_manager,
                    train_loader=self.train_loader,
                    val_loader=self.val_loader,
                    target_standardizer=self.target_standardizer
                )

            self.optimized_model = self.nas_manager.create_optimized_model(architecture)

            if self.optimized_model:
                self.model = self.optimized_model
                self._reinitialize_optimizers()
                self.logger.info(f"成功加载优化架构: {arch_file}")
                return True

        except Exception as e:
            self.logger.error(f"加载优化架构失败: {e}")

        return False
