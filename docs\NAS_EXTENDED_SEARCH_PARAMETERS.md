# NAS扩展搜索参数文档

## 概述

本文档记录了新增的高优先级NAS搜索参数，这些参数对GAN时间序列预测模型的性能有重要影响。

## 新增搜索参数类别

### 1. 训练策略参数 (TrainingStrategyConfig)

#### 优化器配置
- **optimizer_type**: 优化器类型
  - 选项: `adam`, `adamw`, `rmsprop`, `sgd`
  - 默认: `adam`
  - 搜索范围: 4种优化器类型

- **generator_lr**: 生成器学习率
  - 搜索范围: 0.0001 - 0.01
  - 默认: 0.001

- **discriminator_lr**: 判别器学习率
  - 搜索范围: 0.0001 - 0.01
  - 默认: 0.001

- **lr_ratio**: 生成器/判别器学习率比例
  - 搜索范围: 0.5 - 2.0
  - 默认: 1.0

#### 学习率调度
- **lr_scheduler_type**: 学习率调度器类型
  - 选项: `plateau`, `cosine`, `exponential`, `step`, `linear`
  - 默认: `plateau`

- **lr_decay_factor**: 学习率衰减因子
  - 搜索范围: 0.1 - 0.95
  - 默认: 0.9

- **lr_patience**: 学习率调度耐心值
  - 搜索范围: 2 - 10
  - 默认: 3

#### 训练平衡
- **n_critic**: 判别器训练次数
  - 搜索范围: 1 - 5
  - 默认: 1

- **g_steps**: 生成器训练次数
  - 搜索范围: 1 - 3
  - 默认: 1

- **training_balance_strategy**: 训练平衡策略
  - 选项: `fixed`, `adaptive`, `dynamic`
  - 默认: `fixed`

#### 优化器特定参数
- **beta1**: Adam优化器beta1参数
  - 搜索范围: 0.0 - 0.9
  - 默认: 0.5

- **beta2**: Adam优化器beta2参数
  - 搜索范围: 0.9 - 0.999
  - 默认: 0.999

- **momentum**: SGD动量参数
  - 搜索范围: 0.0 - 0.99
  - 默认: 0.9

### 2. 损失函数参数 (LossConfig)

#### 损失权重
- **adversarial_weight**: 对抗损失权重
  - 搜索范围: 0.5 - 2.0
  - 默认: 1.0

- **feature_matching_weight**: 特征匹配损失权重
  - 搜索范围: 0.1 - 5.0
  - 默认: 1.0

- **temporal_consistency_weight**: 时序一致性损失权重
  - 搜索范围: 0.01 - 1.0
  - 默认: 0.1

- **regression_weight**: 回归损失权重
  - 搜索范围: 1.0 - 50.0
  - 默认: 10.0

#### 梯度惩罚
- **lambda_gp**: 梯度惩罚权重
  - 搜索范围: 0.01 - 1.0
  - 默认: 0.1

- **adaptive_lambda_gp**: 是否自适应调整梯度惩罚
  - 选项: True/False
  - 默认: True

- **gp_target_norm**: 目标梯度范数
  - 搜索范围: 0.5 - 2.0
  - 默认: 1.0

#### 损失函数类型
- **discriminator_loss_type**: 判别器损失类型
  - 选项: `wgan_gp`, `lsgan`, `vanilla`, `hinge`
  - 默认: `wgan_gp`

- **generator_loss_type**: 生成器损失类型
  - 选项: `wgan_gp`, `lsgan`, `vanilla`, `hinge`
  - 默认: `wgan_gp`

#### 损失稳定性
- **label_smoothing**: 标签平滑
  - 搜索范围: 0.0 - 0.3
  - 默认: 0.0

- **loss_clipping**: 损失裁剪
  - 搜索范围: 0.0 - 10.0 (0.0表示不裁剪)
  - 默认: 0.0

### 3. 正则化参数 (RegularizationConfig)

#### Dropout配置
- **generator_dropout**: 生成器dropout率
  - 搜索范围: 0.0 - 0.5
  - 默认: 0.1

- **discriminator_dropout**: 判别器dropout率
  - 搜索范围: 0.0 - 0.5
  - 默认: 0.1

- **feature_encoder_dropout**: 特征编码器dropout率
  - 搜索范围: 0.0 - 0.5
  - 默认: 0.1

#### 权重正则化
- **weight_decay**: 权重衰减
  - 搜索范围: 0.0 - 0.01
  - 默认: 0.0

- **spectral_norm**: 是否使用谱归一化
  - 选项: True/False
  - 默认: True

- **gradient_clip_val**: 梯度裁剪值
  - 搜索范围: 0.1 - 5.0
  - 默认: 0.5

- **gradient_clip_type**: 梯度裁剪类型
  - 选项: `norm`, `value`
  - 默认: `norm`

#### 噪声注入
- **noise_std**: 噪声标准差
  - 搜索范围: 0.0 - 0.5
  - 默认: 0.1

- **input_noise_std**: 输入噪声标准差
  - 搜索范围: 0.0 - 0.1
  - 默认: 0.0

#### 归一化类型
- **normalization_type**: 归一化类型
  - 选项: `batch_norm`, `layer_norm`, `instance_norm`, `none`
  - 默认: `layer_norm`

#### 正则化策略
- **dropout_schedule**: Dropout调度策略
  - 选项: `fixed`, `decay`, `adaptive`
  - 默认: `fixed`

- **l1_regularization**: L1正则化
  - 搜索范围: 0.0 - 0.01
  - 默认: 0.0

- **l2_regularization**: L2正则化
  - 搜索范围: 0.0 - 0.01
  - 默认: 0.0

## 搜索空间统计

### 总体规模
- **优化器类型**: 4种
- **学习率调度器**: 5种
- **损失函数类型**: 4种
- **归一化类型**: 4种
- **连续参数**: 20+个
- **离散参数**: 15+个
- **总搜索空间**: 数十亿种组合

### 覆盖度测试结果
通过50次随机采样测试：
- ✅ 优化器类型覆盖: 100% (4/4)
- ✅ 损失类型覆盖: 100% (4/4)
- ✅ 归一化类型覆盖: 100% (4/4)
- ✅ 所有参数范围验证通过

## 使用示例

```python
from src.optimization.nas.search_space import SearchSpaceManager

# 创建搜索空间管理器
manager = SearchSpaceManager(memory_limit_gb=3.0)

# 采样完整架构配置
config = manager.sample_architecture()

# 访问新增的搜索参数
print(f"优化器: {config.training_strategy.optimizer_type.value}")
print(f"生成器学习率: {config.training_strategy.generator_lr}")
print(f"损失权重: {config.loss_config.adversarial_weight}")
print(f"正则化: {config.regularization.normalization_type.value}")

# 序列化配置
config_dict = config.to_dict()

# 反序列化配置
restored_config = config.__class__.from_dict(config_dict)
```

## 预期效果

### 训练策略优化
- **学习率自适应**: 通过搜索最优学习率和调度策略，提升收敛速度
- **优化器选择**: 不同优化器适合不同的数据特征和模型架构
- **训练平衡**: 优化生成器和判别器的训练比例，提升训练稳定性

### 损失函数优化
- **权重平衡**: 自动找到各损失分量的最优权重组合
- **损失类型**: 针对时间序列预测任务选择最适合的损失函数
- **梯度惩罚**: 优化WGAN-GP的梯度惩罚参数，提升训练稳定性

### 正则化优化
- **过拟合防止**: 通过搜索最优dropout和权重衰减参数防止过拟合
- **归一化选择**: 为不同网络层选择最适合的归一化方法
- **噪声注入**: 优化噪声注入策略，提升模型鲁棒性

## 集成状态

✅ **已完成**:
- 搜索空间定义和实现
- 随机采样和验证逻辑
- 配置序列化和反序列化
- 完整的测试覆盖

🔄 **下一步**:
- 集成到NAS评估器中
- 在实际训练中应用搜索结果
- 性能基准测试和对比分析
