#!/usr/bin/env python3
"""
时间序列预测系统便捷运行脚本

这个脚本简化了main.py的命令行参数，避免了参数冗余问题。

用法示例:
    python run.py train --config config.yaml
    python run.py evaluate --config config.yaml --model outputs/models/GAN_epoch_10.pt
    python run.py predict --config config.yaml --model outputs/models/GAN_epoch_10.pt --input data/test.csv --output outputs/predictions
    python run.py process --config config.yaml --output_dir processed_data --save_processed
"""

import argparse
import subprocess
import sys
from pathlib import Path


def run_main_with_mode(mode: str, **kwargs):
    """运行main.py，自动处理参数冗余问题"""
    
    # 基础命令
    cmd = [sys.executable, "main.py", "--mode", mode]
    
    # 添加配置文件参数
    if "config" in kwargs and kwargs["config"]:
        cmd.extend(["--config", kwargs["config"]])
    else:
        raise ValueError("必须提供--config参数")
    
    # 根据模式添加特定参数
    if mode == "train":
        # 训练模式参数
        if kwargs.get("resume"):
            cmd.append("--resume")
        if kwargs.get("checkpoint"):
            cmd.extend(["--checkpoint", kwargs["checkpoint"]])
        
        # 添加虚拟参数以满足argparse要求
        cmd.extend([
            "--output_dir", "dummy",
            "--model", "dummy", 
            "--input", "dummy",
            "--output", "dummy"
        ])
        
    elif mode == "evaluate":
        # 评估模式参数
        if not kwargs.get("model"):
            raise ValueError("评估模式必须提供--model参数")
        cmd.extend(["--model", kwargs["model"]])
        
        # 添加虚拟参数
        cmd.extend([
            "--output_dir", "dummy",
            "--input", "dummy",
            "--output", "dummy"
        ])
        
    elif mode == "predict":
        # 预测模式参数
        if not kwargs.get("model"):
            raise ValueError("预测模式必须提供--model参数")
        if not kwargs.get("input"):
            raise ValueError("预测模式必须提供--input参数")
        if not kwargs.get("output"):
            raise ValueError("预测模式必须提供--output参数")
            
        cmd.extend([
            "--model", kwargs["model"],
            "--input", kwargs["input"],
            "--output", kwargs["output"]
        ])
        
        # 添加虚拟参数
        cmd.extend(["--output_dir", "dummy"])
        
    elif mode == "process":
        # 数据处理模式参数
        if not kwargs.get("output_dir"):
            raise ValueError("数据处理模式必须提供--output_dir参数")
        cmd.extend(["--output_dir", kwargs["output_dir"]])
        
        if kwargs.get("save_processed"):
            cmd.append("--save_processed")
        
        # 添加虚拟参数
        cmd.extend([
            "--model", "dummy",
            "--input", "dummy", 
            "--output", "dummy"
        ])
    
    else:
        raise ValueError(f"不支持的运行模式: {mode}")
    
    # 执行命令
    print(f"执行命令: {' '.join(cmd)}")
    return subprocess.run(cmd)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="时间序列预测系统便捷运行脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run.py train --config config.yaml
  python run.py train --config config.yaml --resume --checkpoint outputs/checkpoints/GAN_epoch_5.pt
  python run.py evaluate --config config.yaml --model outputs/models/GAN_epoch_10.pt
  python run.py predict --config config.yaml --model outputs/models/GAN_epoch_10.pt --input data/test.csv --output outputs/predictions
  python run.py process --config config.yaml --output_dir processed_data --save_processed
        """
    )
    
    # 运行模式（位置参数）
    parser.add_argument("mode", choices=["train", "evaluate", "predict", "process"],
                       help="运行模式")
    
    # 通用参数
    parser.add_argument("--config", type=str, required=True,
                       help="配置文件路径")
    
    # 训练模式参数
    parser.add_argument("--resume", action="store_true",
                       help="从检查点恢复训练")
    parser.add_argument("--checkpoint", type=str,
                       help="检查点路径")
    
    # 评估和预测模式参数
    parser.add_argument("--model", type=str,
                       help="模型路径")
    
    # 预测模式参数
    parser.add_argument("--input", type=str,
                       help="输入数据路径")
    parser.add_argument("--output", type=str,
                       help="输出结果路径")
    
    # 数据处理模式参数
    parser.add_argument("--output_dir", type=str,
                       help="处理后数据的保存目录")
    parser.add_argument("--save_processed", action="store_true",
                       help="是否保存处理后的数据")
    
    args = parser.parse_args()
    
    # 检查配置文件是否存在
    if not Path(args.config).exists():
        print(f"错误: 配置文件不存在: {args.config}")
        return 1
    
    try:
        # 运行对应模式
        result = run_main_with_mode(
            mode=args.mode,
            config=args.config,
            resume=args.resume,
            checkpoint=args.checkpoint,
            model=args.model,
            input=args.input,
            output=args.output,
            output_dir=args.output_dir,
            save_processed=args.save_processed
        )
        return result.returncode
        
    except ValueError as e:
        print(f"参数错误: {e}")
        return 1
    except Exception as e:
        print(f"执行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
