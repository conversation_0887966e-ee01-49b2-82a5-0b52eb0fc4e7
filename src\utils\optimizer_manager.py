"""优化器管理模块 - 仅提供优化器创建功能

本模块实现了优化器的管理功能，包括：
1. 优化器创建
2. 梯度裁剪 (保留，因为与优化器步骤相关)
3. 获取学习率 (保留，用于日志记录)
"""

# 导入类型注解所需的类型

import torch
from torch import nn, optim

from src.utils.config_manager import ConfigManager
from src.utils.logger import get_logger


class OptimizerManager:
    """优化器管理器 - 仅负责优化器的创建和基本管理"""

    def __init__(self, config: ConfigManager):

        # 类型注解
        self.optimizer_type: str | None = None
        self.generator_lr: float | None = None
        self.discriminator_lr: float | None = None
        self.weight_decay: float | None = None
        self.beta1: float | None = None
        self.beta2: float | None = None
        self.momentum: float | None = None
        self.eps: float | None = None
        """初始化优化器管理器

        Args:
            config: 配置管理器
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config = config

        # 初始化优化器参数 - 全部必须从配置加载
        self.optimizer_type = None  # 优化器类型
        self.generator_lr = None    # 生成器学习率
        self.discriminator_lr = None # 判别器学习率
        self.weight_decay = None    # 权重衰减
        self.beta1 = None          # beta1
        self.beta2 = None          # beta2
        self.momentum = None       # 动量
        self.eps = None           # epsilon

        # 配置优化器参数
        self._configure_optimizer()

        self.logger.info("优化器管理器初始化完成 (仅优化器创建)")

    def _configure_optimizer(self):
        """配置优化器参数"""
        if not hasattr(self.config, 'training') or not hasattr(self.config.training, 'optimizer'):
            raise ValueError("配置中缺少必需的 training.optimizer 节")

        optimizer_config = self.config.training.optimizer
        is_opt_dict = isinstance(optimizer_config, dict)

        # 严格模式：直接读取必需字段，不使用默认值
        required_fields = [
            'type', 'generator_lr', 'discriminator_lr', 'weight_decay',
            'beta1', 'beta2', 'momentum', 'eps'
        ]

        try:
            if is_opt_dict:
                for field in required_fields:
                    if field not in optimizer_config:
                        raise ValueError(f"优化器配置缺少必需字段: {field}")
                self.optimizer_type = optimizer_config['type']
                self.generator_lr = float(optimizer_config['generator_lr'])
                self.discriminator_lr = float(optimizer_config['discriminator_lr'])
                self.weight_decay = float(optimizer_config['weight_decay'])
                self.beta1 = float(optimizer_config['beta1'])
                self.beta2 = float(optimizer_config['beta2'])
                self.momentum = float(optimizer_config['momentum'])
                self.eps = float(optimizer_config['eps'])
            else:
                for field in required_fields:
                    if not hasattr(optimizer_config, field):
                        raise ValueError(f"优化器配置缺少必需字段: {field}")
                self.optimizer_type = optimizer_config.type
                self.generator_lr = float(optimizer_config.generator_lr)
                self.discriminator_lr = float(optimizer_config.discriminator_lr)
                self.weight_decay = float(optimizer_config.weight_decay)
                self.beta1 = float(optimizer_config.beta1)
                self.beta2 = float(optimizer_config.beta2)
                self.momentum = float(optimizer_config.momentum)
                self.eps = float(optimizer_config.eps)

            # 验证参数有效性
            if self.weight_decay < 0:
                raise ValueError(f"weight_decay必须大于等于0，当前值: {self.weight_decay}")
            if not 0 <= self.beta1 < 1:
                raise ValueError(f"beta1必须在[0,1)范围内，当前值: {self.beta1}")
            if not 0 <= self.beta2 < 1:
                raise ValueError(f"beta2必须在[0,1)范围内，当前值: {self.beta2}")
            if not 0 <= self.momentum < 1:
                raise ValueError(f"momentum必须在[0,1)范围内，当前值: {self.momentum}")
            if self.eps <= 0:
                raise ValueError(f"eps必须大于0，当前值: {self.eps}")

            # 日志记录
            self.logger.debug(
                f"优化器配置已加载:\n"
                f"- 优化器类型: {self.optimizer_type}\n"
                f"- 生成器学习率: {self.generator_lr}\n"
                f"- 判别器学习率: {self.discriminator_lr}\n"
                f"- 权重衰减: {self.weight_decay}\n"
                f"- Beta1: {self.beta1}, Beta2: {self.beta2}\n"
                f"- 动量: {self.momentum}, Epsilon: {self.eps}"
            )

        except ValueError as ve:
            self.logger.error(f"加载优化器配置时发生值错误: {ve!s}")
            raise
        except Exception as e:
            self.logger.error(f"加载优化器配置时发生意外错误: {e!s}")
            raise ValueError(f"优化器配置加载失败: {e!s}")

    def create_optimizer(
        self,
        model: nn.Module,
        model_type: str,  # 必需参数，用于区分模型类型 ('generator', 'discriminator')
        optimizer_type: str | None = None,
        weight_decay: float | None = None,
        beta1: float | None = None,
        beta2: float | None = None,
        momentum: float | None = None,
        eps: float | None = None
    ) -> optim.Optimizer:
        """创建优化器

        Args:
            model: 模型
            model_type: 模型类型 ('generator', 'discriminator')，用于选择特定学习率（必需）
            optimizer_type: 优化器类型，可选值：adam, sgd, rmsprop
            weight_decay: 权重衰减
            beta1: beta1
            beta2: beta2
            momentum: 动量
            eps: epsilon

        Returns:
            optim.Optimizer: 优化器

        Raises:
            ValueError: 如果任何必需参数缺失或无效
        """
        # 验证model_type参数
        if model_type not in ['generator', 'discriminator']:
            raise ValueError(f"model_type必须是'generator'或'discriminator'，但获取到的是: {model_type}")

        # 确定优化器类型和参数 (除学习率外)
        optimizer_type = optimizer_type or self.optimizer_type
        if optimizer_type is None:
            raise ValueError("optimizer_type不能为None，必须提供有效的优化器类型")

        # 使用配置的值，不允许使用None
        if weight_decay is None:
            weight_decay = self.weight_decay
        if beta1 is None:
            beta1 = self.beta1
        if beta2 is None:
            beta2 = self.beta2
        if momentum is None:
            momentum = self.momentum
        if eps is None:
            eps = self.eps

        # 转换为浮点数并验证
        try:
            # 确保值不为None并转换
            assert weight_decay is not None, "weight_decay不能为None"
            assert beta1 is not None, "beta1不能为None"
            assert beta2 is not None, "beta2不能为None"
            assert momentum is not None, "momentum不能为None"
            assert eps is not None, "eps不能为None"

            # 使用str()转换确保类型安全
            weight_decay = float(str(weight_decay))
            beta1 = float(str(beta1))
            beta2 = float(str(beta2))
            momentum = float(str(momentum))
            eps = float(str(eps))
        except (TypeError, ValueError) as e:
            raise ValueError(f"无法将优化器参数转换为浮点数: {e!s}") from e

        # --- 严格根据 model_type 确定学习率 ---
        final_lr = None
        if model_type == 'generator':
            if self.generator_lr is None:
                 raise ValueError("生成器学习率 (generator_lr) 未在配置中设置或加载失败。")
            final_lr = self.generator_lr
            self.logger.debug(f"使用生成器学习率: {final_lr}")
        elif model_type == 'discriminator':
            if self.discriminator_lr is None:
                 raise ValueError("判别器学习率 (discriminator_lr) 未在配置中设置或加载失败。")
            final_lr = self.discriminator_lr
            self.logger.debug(f"使用判别器学习率: {final_lr}")
        else:
            raise ValueError(f"创建优化器时必须提供有效的 model_type ('generator' 或 'discriminator')，收到: {model_type}")

        # 创建优化器
        if optimizer_type == "adam":
            optimizer = optim.Adam(
                model.parameters(),
                lr=final_lr,
                weight_decay=weight_decay,
                betas=(beta1, beta2),
                eps=eps
            )
        elif optimizer_type == "adamw":
            optimizer = optim.AdamW(
                model.parameters(),
                lr=final_lr,
                weight_decay=weight_decay,
                betas=(beta1, beta2),
                eps=eps
            )
        elif optimizer_type == "sgd":
            optimizer = optim.SGD(
                model.parameters(),
                lr=final_lr,
                weight_decay=weight_decay,
                momentum=momentum
            )
        elif optimizer_type == "rmsprop":
            optimizer = optim.RMSprop(
                model.parameters(),
                lr=final_lr,
                weight_decay=weight_decay,
                momentum=momentum,
                eps=eps
            )
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_type}")

        self.logger.info(
            f"优化器已创建:\n"
            f"- 类型: {optimizer_type}\n"
            f"- 学习率: {final_lr}\n"
            f"- 权重衰减: {weight_decay}"
        )

        return optimizer

    # --- 移除调度器相关方法 ---
    # def create_scheduler(...)
    # def update_learning_rate(...)
    # def register_optimizer(...) # 这个方法似乎与调度器更新相关，也移除

    def clip_gradients(
        self,
        model: nn.Module,
        max_norm: float = 1.0
    ):
        """裁剪梯度

        Args:
            model: 模型
            max_norm: 最大范数
        """
        # 裁剪梯度
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm)

    def get_learning_rate(self, optimizer: optim.Optimizer) -> float:
        """获取当前学习率

        Args:
            optimizer: 优化器

        Returns:
            float: 当前学习率
        """
        # 检查优化器是否有参数组
        if not optimizer.param_groups:
            self.logger.warning(f"优化器 {type(optimizer).__name__} 没有参数组，无法获取学习率。返回 0.0")
            return 0.0
        # 检查参数组是否为空
        if not optimizer.param_groups[0]:
             self.logger.warning(f"优化器 {type(optimizer).__name__} 的第一个参数组为空，无法获取学习率。返回 0.0")
             return 0.0
        # 检查 'lr' 键是否存在
        if 'lr' not in optimizer.param_groups[0]:
             self.logger.warning(f"优化器 {type(optimizer).__name__} 的第一个参数组中缺少 'lr' 键。返回 0.0")
             return 0.0

        return optimizer.param_groups[0]['lr']
