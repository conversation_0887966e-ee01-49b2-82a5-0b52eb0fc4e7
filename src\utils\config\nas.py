"""NAS配置模块 - 神经架构搜索相关配置类

此模块定义了NAS相关的所有配置类，包括搜索空间配置和管理器配置。
所有配置参数都必须通过配置文件设定，不提供默认值回退。
"""

from dataclasses import dataclass, field
from typing import Any


@dataclass
class SearchSpaceConfig:
    """搜索空间配置 - 所有参数必须在配置文件中明确设定"""

    # 生成器搜索空间 - 必须配置
    generator_num_layers_range: tuple[int, int]
    generator_hidden_dim_range: tuple[int, int]
    generator_num_heads_range: tuple[int, int]
    generator_dropout_range: tuple[float, float]
    generator_activation_types: list[str]

    # 判别器搜索空间 - 必须配置
    discriminator_num_layers_range: tuple[int, int]
    discriminator_hidden_dim_range: tuple[int, int]

    # 特征编码器搜索空间 - 必须配置
    feature_encoder_num_scales_range: tuple[int, int]
    feature_encoder_kernel_size_options: list[int]
    feature_encoder_temporal_window_options: list[int]
    feature_encoder_amplification_factor_range: tuple[float, float]
    feature_encoder_fusion_types: list[str]

    # 训练策略搜索空间 - 必须配置
    training_lr_range: tuple[float, float]
    training_optimizer_types: list[str]
    training_lr_scheduler_types: list[str]
    training_n_critic_range: tuple[int, int]
    training_g_steps_range: tuple[int, int]

    # 损失函数搜索空间 - 必须配置
    loss_adversarial_weight_range: tuple[float, float]
    loss_feature_matching_weight_range: tuple[float, float]
    loss_temporal_consistency_weight_range: tuple[float, float]
    loss_gradient_penalty_lambda_range: tuple[float, float]
    loss_gradient_penalty_target_range: tuple[float, float]
    loss_types: list[str]
    loss_label_smoothing_range: tuple[float, float]
    loss_loss_clipping_range: tuple[float, float]

    # 正则化搜索空间 - 必须配置
    regularization_dropout_range: tuple[float, float]
    regularization_weight_decay_range: tuple[float, float]
    regularization_gradient_clipping_range: tuple[float, float]
    regularization_normalization_types: list[str]
    regularization_noise_std_range: tuple[float, float]
    regularization_input_noise_range: tuple[float, float]


@dataclass
class NASManagerConfig:
    """NAS管理器配置 - 所有参数必须在配置文件中明确设定"""

    # 搜索策略配置 - 必须配置
    search_strategy: str
    max_iterations: int
    time_budget_hours: float

    # 评估配置 - 必须配置
    max_epochs_per_eval: int
    early_stop_patience: int
    memory_limit_mb: float

    # 进化算法配置 - 必须配置
    population_size: int
    mutation_rate: float
    crossover_rate: float

    # DARTS配置 - 必须配置
    darts_learning_rate: float
    darts_momentum: float

    # 资源约束 - 必须配置
    gpu_memory_limit_gb: float
    enable_distributed: bool
    num_workers: int

    # 日志和保存 - 必须配置
    log_dir: str
    save_dir: str
    experiment_name: str
    save_intermediate_results: bool


@dataclass
class NASConfig:
    """完整的NAS配置 - 包含搜索空间和管理器配置"""

    search_space: SearchSpaceConfig
    manager: NASManagerConfig
