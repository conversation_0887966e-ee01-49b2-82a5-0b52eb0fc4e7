"""优化数据过滤模块 - 提供参数探索模块使用的数据过滤功能"""

from __future__ import annotations

import pandas as pd

from src.optimization.exceptions import ConfigurationError
from src.utils.config import ConfigManager
from src.utils.logger import get_logger


def filter_data_for_optimization(
    data: pd.DataFrame,
    config: ConfigManager
) -> pd.DataFrame:
    """
    根据优化配置中的 optimization_start_date 参数过滤数据

    Args:
        data: 原始数据
        config: 配置管理器

    Returns:
        pd.DataFrame: 过滤后的数据

    Raises:
        ConfigurationError: 如果配置缺失或无效
    """
    logger = get_logger(__name__)

    # 检查配置
    if not hasattr(config, 'optimization') or config.optimization is None:
        raise ConfigurationError("配置中缺少 'optimization' 部分")

    # 获取优化起始日期
    if not hasattr(config.optimization, 'optimization_start_date'):
        raise ConfigurationError("配置中缺少 optimization.optimization_start_date 字段")

    opt_start_date_str = config.optimization.optimization_start_date
    if not opt_start_date_str:
        raise ConfigurationError("配置 optimization.optimization_start_date 未设置或为空，参数探索需要此配置。")

    # 检查数据中是否有日期列
    date_col = 'date'
    if date_col not in data.columns:
        raise ConfigurationError(f"数据中缺少必需的日期列 '{date_col}'")

    # 确保日期列是 datetime 类型
    data[date_col] = pd.to_datetime(data[date_col])
    original_min_date = data[date_col].min()
    original_max_date = data[date_col].max()
    
    # 安全地记录日期范围
    min_date_str = original_min_date.date() if hasattr(original_min_date, 'date') and callable(original_min_date.date) else str(original_min_date)
    max_date_str = original_max_date.date() if hasattr(original_max_date, 'date') and callable(original_max_date.date) else str(original_max_date)
    logger.info(f"原始数据时间范围: {min_date_str} 到 {max_date_str}")

    try:
        # 解析优化起始日期
        logger.info(f"检测到配置 optimization.optimization_start_date: '{opt_start_date_str}'")
        start_date = pd.to_datetime(opt_start_date_str)
        # 使用数据中的最大日期作为结束日期
        end_date = original_max_date if isinstance(original_max_date, pd.Timestamp) else pd.to_datetime(original_max_date)

        # 验证解析后的日期
        if pd.isna(start_date):
            raise ValueError("解析后的 start_date 为 NaT")
        if pd.isna(end_date):
            raise ValueError("原始数据的最大日期无效")
            
        # 确保 start_date 和 end_date 是可比较的 datetime 类型
        if not isinstance(start_date, pd.Timestamp) or not isinstance(end_date, pd.Timestamp):
            raise ValueError("日期格式无效，无法比较")
            
        if start_date > end_date:
            start_date_str = start_date.date() if hasattr(start_date, 'date') and callable(start_date.date) else str(start_date)
            end_date_str = end_date.date() if hasattr(end_date, 'date') and callable(end_date.date) else str(end_date)
            raise ValueError(f"起始日期 ({start_date_str}) 不能晚于结束日期 ({end_date_str})")

        # 应用过滤
        num_before = len(data)
        date_mask = (data[date_col] >= start_date) & (data[date_col] <= end_date)
        
        # 确保返回的是 DataFrame 类型
        if hasattr(data, 'loc'):
            filtered_data = data.loc[date_mask].copy()
        else:
            filtered_data = data[date_mask].copy()
            if not isinstance(filtered_data, pd.DataFrame):
                filtered_data = pd.DataFrame(filtered_data)
                
        num_after = len(filtered_data)

        if num_after == 0:
            start_str = start_date.date() if hasattr(start_date, 'date') and callable(start_date.date) else str(start_date)
            end_str = end_date.date() if hasattr(end_date, 'date') and callable(end_date.date) else str(end_date)
            raise ValueError(f"使用 optimization_start_date 过滤后没有剩余数据。范围: [{start_str}] - [{end_str}]")

        # 安全地记录日期范围
        start_str = start_date.date() if hasattr(start_date, 'date') and callable(start_date.date) else str(start_date)
        end_str = end_date.date() if hasattr(end_date, 'date') and callable(end_date.date) else str(end_date)
        logger.info(f"已应用 optimization_start_date 过滤: [{start_str}] - [{end_str}]")
        logger.info(f"过滤前样本数: {num_before}, 过滤后样本数: {num_after}")

        # 确保返回的是 DataFrame 类型
        if not isinstance(filtered_data, pd.DataFrame):
            return pd.DataFrame(filtered_data)
        return filtered_data

    except Exception as e:
        # 捕获所有在日期处理和过滤中发生的错误
        error_msg = f"处理 optimization.optimization_start_date ('{opt_start_date_str}') 或进行日期过滤时出错: {e}"
        logger.error(error_msg, exc_info=True)
        raise ConfigurationError(error_msg) from e
