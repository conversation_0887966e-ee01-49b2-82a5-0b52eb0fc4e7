#!/usr/bin/env python3
"""
配置修复测试模块

测试所有配置修复的功能，包括：
1. 激活函数工厂
2. 生成器配置对象化
3. 判别器配置对象化
4. 配置加载器修复
5. 权重初始化修复
"""

import sys
import os
import torch
import traceback
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_activation_factory():
    """测试激活函数工厂"""
    print("=" * 60)
    print("测试激活函数工厂")
    print("=" * 60)
    
    try:
        from src.models.gan.components.activation_factory import test_all_activations, test_swish_specifically
        
        # 测试所有激活函数
        all_passed = test_all_activations()
        
        # 专门测试Swish
        swish_passed = test_swish_specifically()
        
        return all_passed and swish_passed
        
    except Exception as e:
        print(f"❌ 激活函数工厂测试失败: {e}")
        traceback.print_exc()
        return False


def test_config_loading():
    """测试配置加载"""
    print("=" * 60)
    print("测试配置加载")
    print("=" * 60)
    
    try:
        from src.utils.config.loader import ConfigLoader
        
        # 测试配置加载
        loader = ConfigLoader()
        config = loader.load_from_yaml('config.yaml')
        
        print("✅ 配置加载成功")
        
        # 验证生成器配置
        generator_config = config.model.generator
        print(f"生成器配置类型: {type(generator_config)}")
        print(f"生成器激活函数: {generator_config.activation_type}")
        print(f"生成器层归一化: {generator_config.use_layer_norm}")
        print(f"生成器残差连接: {generator_config.use_residual}")
        print(f"噪声注入层: {generator_config.noise_injection_layers}")
        
        # 验证判别器配置
        discriminator_config = config.model.discriminator
        print(f"判别器配置类型: {type(discriminator_config)}")
        print(f"判别器层数: {discriminator_config.num_layers}")
        print(f"判别器隐藏维度: {discriminator_config.hidden_dims}")
        print(f"注意力类型: {discriminator_config.attention_types}")
        print(f"分支配置: {discriminator_config.branch_config}")
        
        # 验证配置值是否符合预期
        assert generator_config.activation_type == "swish", f"生成器激活函数应为swish，实际为{generator_config.activation_type}"
        assert generator_config.use_layer_norm == False, f"生成器层归一化应为False，实际为{generator_config.use_layer_norm}"
        assert generator_config.use_residual == True, f"生成器残差连接应为True，实际为{generator_config.use_residual}"
        assert generator_config.noise_injection_layers == [4], f"噪声注入层应为[4]，实际为{generator_config.noise_injection_layers}"
        
        assert discriminator_config.num_layers == 6, f"判别器层数应为6，实际为{discriminator_config.num_layers}"
        assert discriminator_config.hidden_dims == [128, 128, 96, 64, 48, 32], f"判别器隐藏维度不匹配"
        assert discriminator_config.attention_types == ["adaptive_dilation", "causal"], f"注意力类型不匹配"
        assert discriminator_config.branch_config["trend_branch"] == True, f"趋势分支应为True"
        
        print("✅ 配置验证全部通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        traceback.print_exc()
        return False


def test_weight_initialization():
    """测试权重初始化修复"""
    print("=" * 60)
    print("测试权重初始化修复")
    print("=" * 60)
    
    try:
        from src.models.gan.components.activation_factory import create_activation
        from src.models.base.base_module import BaseModule
        
        # 创建一个包含Swish的简单模块
        class TestModule(BaseModule):
            def __init__(self):
                super().__init__("TestModule")
                self.swish = create_activation("swish")
                self.linear = torch.nn.Linear(10, 5)
        
        # 测试初始化
        module = TestModule()
        print("✅ 包含Swish的模块初始化成功")
        
        # 测试前向传播
        x = torch.randn(2, 10)
        y = module.linear(module.swish(x))
        print(f"✅ 前向传播成功: {x.shape} -> {y.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 权重初始化测试失败: {e}")
        traceback.print_exc()
        return False


def test_generator_config_integration():
    """测试生成器配置集成"""
    print("=" * 60)
    print("测试生成器配置集成")
    print("=" * 60)
    
    try:
        from src.utils.config.loader import ConfigLoader
        from src.models.gan.components.activation_factory import create_activation
        
        # 加载配置
        loader = ConfigLoader()
        config = loader.load_from_yaml('config.yaml')
        
        # 获取生成器配置
        gen_config = config.model.generator
        
        # 测试激活函数创建
        activation = create_activation(gen_config.activation_type)
        print(f"✅ 根据配置创建激活函数: {type(activation).__name__}")
        
        # 测试配置属性访问
        print(f"层数: {gen_config.num_layers}")
        print(f"隐藏维度: {gen_config.hidden_dim}")
        print(f"学习率: {gen_config.learning_rate}")
        print(f"激活函数: {gen_config.activation_type}")
        print(f"层归一化: {gen_config.use_layer_norm}")
        print(f"残差连接: {gen_config.use_residual}")
        print(f"噪声注入层: {gen_config.noise_injection_layers}")
        
        print("✅ 生成器配置集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 生成器配置集成测试失败: {e}")
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始配置修复测试")
    print("=" * 80)
    
    tests = [
        ("激活函数工厂", test_activation_factory),
        ("配置加载", test_config_loading),
        ("权重初始化修复", test_weight_initialization),
        ("生成器配置集成", test_generator_config_integration),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            traceback.print_exc()
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 测试结果汇总")
    print("=" * 80)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if all_passed:
        print("🎉 所有配置修复测试通过！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return all_passed


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
