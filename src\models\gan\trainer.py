"""训练器模块 - 提供GAN网络的训练流程与优化框架

项目结构模块索引：
1. 基础设施模块:
   - src/utils/config_manager.py: 配置管理，训练参数控制
   - src/utils/logger.py: 日志系统，训练过程记录
   - src/utils/cuda_manager.py: GPU管理和性能监控
   - src/utils/path_utils.py: 路径工具，检查点文件管理
   - src/utils/resource_manager.py: 资源管理，训练资源分配
   - src/utils/exception_handler.py: 异常处理，训练错误恢复

2. 共用模块:
   - src/models/gan/gan_model.py: GAN模型，训练目标模型
   - src/models/gan/loss_calculator.py: 损失计算，训练优化指标
   - src/models/base/model_state.py: 模型状态，训练状态管理
   - src/models/base/model_saver.py: 模型保存，检查点管理

3. 配置文件:
   - config.yaml:
     ├── training:
     │   ├── optimizer:
     │   │   ├── type: 优化器类型
     │   │   ├── learning_rate: 学习率设置
     │   │   ├── weight_decay: 权重衰减
     │   │   └── momentum: 动量参数
     │   ├── scheduler:
     │   │   ├── type: 调度器类型
     │   │   ├── patience: 耐心值设置
     │   │   ├── factor: 衰减因子
     │   │   └── cooldown: 冷却期设置
     │   └── process:
     │       ├── max_epochs: 最大轮次
     │       ├── early_stopping: 早停设置
     │       └── gradient_clipping: 梯度裁剪值
     └── checkpointing:
         ├── frequency: 保存频率设置
         ├── keep_best_n: 保留最佳模型数
         └── metrics: 评估指标设置

4. 父类模块:
   - src/models/base/base_model.py: BaseModel，模型基类
   - src/models/base/base_module.py: BaseModule，基础功能模块
   - src/models/base/signal_processor.py: SignalProcessor，信号处理基类
   - src/models/base/model_state.py: ModelState，状态管理基类

5. 同阶段GAN模块:
   - gan_model.py: GAN整体模型
   - generator.py: 生成器实现
   - discriminator.py: 判别器实现
   - loss_calculator.py: 损失计算模块
   - gan_evaluator.py: 评估器实现

核心功能：
1. 训练循环管理
   - 训练轮次控制
   - 批次迭代处理
   - 验证过程集成
   - 训练终止判断

2. 优化器策略
   - 生成器优化器配置
   - 判别器优化器配置
   - 梯度累积实现
   - 梯度裁剪控制

3. 学习率调度
   - 动态学习率调整
   - 性能监控触发
   - 预热策略实现
   - 衰减模式控制

4. 检查点管理
   - 定期模型保存
   - 最佳模型追踪
   - 训练恢复支持
   - 状态一致性保障

5. 性能监控与分析
   - 训练速度测量
   - 资源使用追踪
   - 损失趋势分析
   - 过拟合检测实现
"""

import math
import time
from collections import defaultdict
from collections.abc import Callable
from datetime import datetime
from pathlib import Path
from typing import (
    Protocol,
    TypeVar,
    cast,
    runtime_checkable,
)

import torch

# from pathlib import Path # Duplicate import
from torch import nn
from torch.utils.data import DataLoader, Dataset

# 已在上面导入 DataLoader 和 Dataset
# 导入 Standardizer 以便进行类型提示
from src.data.standardization import Standardizer
from src.models.base.base_model import BaseModel
from src.models.base.model_saver import ModelSaver

# Removed monitor_manager import as its functionality is now in cuda_manager
from src.models.base.model_state import ModelState, ModelStateManager
from src.models.gan.gan_evaluator import GANEvaluator
from src.utils.adaptive_lambda_gp import AdaptiveLambdaGP
from src.utils.config.base import BaseConfig  # Added import for BaseConfig

# 导入批次大小优化器
# 导入学习率调度器以进行类型检查
# from torch.optim.lr_scheduler import CyclicLR # 不再需要
from src.utils.config_manager import ConfigManager
from src.utils.cuda import cuda_manager
from src.utils.error_monitor import configure_error_monitor

# 导入学习率平衡器
from src.utils.enhanced_gan_lr_balancer import EnhancedGanLrBalancer

# 导入模型统计监控和错误监控工具
from src.utils.model_stats_monitor import ModelStatsMonitor
from src.utils.optimizer_manager import OptimizerManager

# Placeholder for log_performance if it's not defined elsewhere
# def log_performance(metrics: Dict[str, float]):
#     # Implement logging logic here, e.g., using system_monitor or logger
#     # print(f"Performance Log: {metrics}")
#     pass

@runtime_checkable
class TrainableModel(Protocol):
    # 更新协议以包含 n_critic 和 data_stream 参数
    def train_step(self, features: torch.Tensor, targets: torch.Tensor, data_stream: torch.cuda.Stream, n_critic: int, current_lambda_gp: float | None) -> dict[str, float]: ...

T = TypeVar('T')
@runtime_checkable
class SizedDataset(Protocol):
    def __len__(self) -> int: ...
    def __getitem__(self, index: int) -> dict[str, torch.Tensor]: ...

class GANTrainer(BaseModel):
    """GAN训练管理器

    专注于训练流程管理，利用GANModel提供的完整功能。
    重用了以下现有功能：
    1. ModelStateManager - 状态管理和早停
    2. ModelSaver - 检查点管理
    3. GANEvaluator - 模型评估
    """

    def __init__(
        self,
        config: ConfigManager,
        model: nn.Module | None = None,
        train_loader: DataLoader | None = None,
        val_loader: DataLoader | None = None,
        test_loader: DataLoader | None = None,
        target_standardizer: Standardizer | None = None
    ):
        """初始化GAN训练器

        Args:
            config: 配置对象
            model: GAN模型实例
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            test_loader: 测试数据加载器
            target_standardizer: 目标标准化器
        """
        # 调用父类初始化，确保混合精度配置被正确初始化
        super().__init__(config, name="GANTrainer")

        try:
            # 1. 配置验证
            if not hasattr(self.config_manager, 'training'):
                raise ValueError("配置缺少 training 部分")
            training_config = self.config_manager.training

            # 2. 基础配置
            self.device = cuda_manager.device

            # 3. 记录初始配置
            version: str
            if hasattr(self.config_manager, 'version'):
                version = str(self.config_manager.version) if not isinstance(self.config_manager.version, dict) else 'dict_version'
            else:
                version = 'unknown'
            self._logger.info(f"GANTrainer初始化:\n"
                          f"- 配置版本: {version}\n"
                          f"- 设备: {self.device}\n"
                          f"- 混合精度: {'启用' if self.use_amp else '禁用'}")

            # 4. 基本组件设置
            self.model = model
            self.train_loader = train_loader
            self.val_loader = val_loader
            self.test_loader = test_loader
            self.target_standardizer = target_standardizer # 直接使用传入的 target_standardizer
            self.pruning_callback = None # 初始化剪枝回调

            # 初始化训练平衡参数
            self.current_n_critic = 1  # 默认值
            self.current_g_steps = 1   # 默认值
            if hasattr(training_config, 'balance'):
                balance_config = training_config.balance
                if hasattr(balance_config, 'min_n_critic'): # 使用 min_n_critic 作为初始值
                    self.current_n_critic = balance_config.min_n_critic
                    self._logger.info(f"从配置 training.balance.min_n_critic 加载初始 n_critic: {self.current_n_critic}")
                else:
                    self._logger.info(f"配置 training.balance 中未找到 min_n_critic，n_critic 使用默认值: {self.current_n_critic}")

                if hasattr(balance_config, 'min_g_steps'): # 使用 min_g_steps 作为初始值
                    self.current_g_steps = balance_config.min_g_steps
                    self._logger.info(f"从配置 training.balance.min_g_steps 加载初始 g_steps: {self.current_g_steps}")
                else:
                    self._logger.info(f"配置 training.balance 中未找到 min_g_steps，g_steps 使用默认值: {self.current_g_steps}")
            else:
                self._logger.info(f"配置中未找到 training.balance，n_critic 和 g_steps 使用默认值: {self.current_n_critic}, {self.current_g_steps}")

            # 初始化精准调整的历史趋势跟踪
            self.loss_ratio_history = []  # 存储最近几轮的损失比率
            self.mae_history = []  # 存储最近几轮的MAE
            self.history_window = 3  # 历史窗口大小
            self._logger.info(f"精准调整机制已初始化，历史窗口大小: {self.history_window}")

            # 输出训练平衡配置信息
            if hasattr(training_config, 'balance'):
                balance_config = training_config.balance
                self._logger.info(
                    f"📊 训练平衡配置:\n"
                    f"   初始判别器训练次数: {self.current_n_critic}\n"
                    f"   初始生成器训练次数: {self.current_g_steps}\n"
                    f"   判别器过弱阈值: {getattr(balance_config, 'lower_threshold', '未配置')}\n"
                    f"   判别器过强阈值: {getattr(balance_config, 'upper_threshold', '未配置')}\n"
                    f"   判别器训练次数范围: {getattr(balance_config, 'min_n_critic', '未配置')} - {getattr(balance_config, 'max_n_critic', '未配置')}\n"
                    f"   生成器训练次数范围: {getattr(balance_config, 'min_g_steps', '未配置')} - {getattr(balance_config, 'max_g_steps', '未配置')}\n"
                    f"   动态调整: 启用 (修复后的敏感阈值)"
                )
            else:
                self._logger.info(
                    f"📊 训练平衡配置:\n"
                    f"   初始判别器训练次数: {self.current_n_critic} (默认值)\n"
                    f"   初始生成器训练次数: {self.current_g_steps} (默认值)\n"
                    f"   动态调整: 禁用 (未找到balance配置)"
                )

            # 5. 初始化优化器管理器
            self.optimizer_manager = OptimizerManager(self.config_manager)

            # 6. 初始化自适应梯度惩罚权重管理器
            # 从配置中读取lambda_gp值，不提供默认值回退
            if not hasattr(training_config, 'lambda_gp'):
                raise ValueError("配置中缺少必需字段 'lambda_gp'")

            self.current_lambda_gp = training_config.lambda_gp
            self._logger.info(f"从配置加载 lambda_gp: {self.current_lambda_gp}")

            if hasattr(training_config, 'use_adaptive_lambda_gp') and training_config.use_adaptive_lambda_gp:
                if hasattr(training_config, 'adaptive_lambda_gp'):
                    # 获取模型配置
                    use_self_attention = False
                    window_size = 48
                    if hasattr(self.config_manager, 'model'):
                        model_config = self.config_manager.model
                        # 日志追踪model_config
                        self._logger.debug(f"[DEBUG] model_config类型: {type(model_config)}")
                        self._logger.debug(f"[DEBUG] model_config属性: {dir(model_config)}")
                        if hasattr(model_config, '__dict__'):
                            self._logger.debug(f"[DEBUG] model_config内容: {model_config.__dict__}")
                        # 从sequence_strategy获取window_size
                        sequence_strategy_config = getattr(model_config, 'sequence_strategy', None)
                        if sequence_strategy_config is not None:
                            self._logger.debug(f"[DEBUG] sequence_strategy_config类型: {type(sequence_strategy_config)}")
                            self._logger.debug(f"[DEBUG] sequence_strategy_config属性: {dir(sequence_strategy_config)}")
                            if hasattr(sequence_strategy_config, '__dict__'):
                                self._logger.debug(f"[DEBUG] sequence_strategy_config内容: {sequence_strategy_config.__dict__}")
                            if hasattr(sequence_strategy_config, 'window_size'):
                                window_size = sequence_strategy_config.window_size

                        # 从discriminator获取use_self_attention
                        discriminator_specific_config = getattr(model_config, 'discriminator', None)
                        if discriminator_specific_config is not None:
                            self._logger.debug(f"[DEBUG] discriminator_specific_config类型: {type(discriminator_specific_config)}")
                            self._logger.debug(f"[DEBUG] discriminator_specific_config内容: {discriminator_specific_config}")
                            if isinstance(discriminator_specific_config, dict) and 'enable_temporal_attention' in discriminator_specific_config:
                                use_self_attention = discriminator_specific_config['enable_temporal_attention']
                            elif hasattr(discriminator_specific_config, 'enable_temporal_attention'): # If it's an object
                                # Use getattr for safer access, even after hasattr, to satisfy Pylance
                                use_self_attention = getattr(discriminator_specific_config, 'enable_temporal_attention', False)


                    self.adaptive_lambda_gp_manager = AdaptiveLambdaGP(
                        config=training_config.adaptive_lambda_gp,
                        use_self_attention=use_self_attention,
                        window_size=window_size,
                        logger=self._logger
                    )
                    self.current_lambda_gp = cast(float, self.adaptive_lambda_gp_manager.get_lambda_gp())
                    self._logger.info(f"AdaptiveLambdaGP管理器已初始化。初始 lambda_gp: {self.current_lambda_gp:.4f}")
                else:
                    self._logger.info("自适应梯度惩罚权重机制已配置但未启用（或配置数据非字典）。")
            else:
                self._logger.info(f"未使用自适应梯度惩罚权重机制。固定 lambda_gp: {self.current_lambda_gp:.4f}")

            # 7. 初始化监控工具
            self._init_monitoring_tools_impl() # Renamed to avoid potential conflicts if BaseModel had it

            # 7.1 初始化状态管理器、模型保存器和评估器
            try:
                self.state_manager = ModelStateManager(config=self.config_manager)
                self._logger.info("ModelStateManager 初始化成功。")

                # 确保 save_dir 是 Path 对象
                save_dir_path = Path(self.config_manager.paths.model_dir)
                self.model_saver = ModelSaver(
                    model_name=self.name, # self.name is "GANTrainer"
                    save_dir=save_dir_path,
                    config=self.config_manager, # Pass the full ConfigManager
                    state_manager=self.state_manager # Pass the initialized state_manager
                )
                self._logger.info("ModelSaver 初始化成功。")

                if self.target_standardizer is None:
                    self._logger.warning("target_standardizer 未提供给 GANTrainer，GANEvaluator 可能无法正确进行逆标准化。")

                self.evaluator = GANEvaluator(
                    config=self.config_manager, # Pass the full ConfigManager
                    standardizer=self.target_standardizer # Pass the existing standardizer
                )
                self._logger.info("GANEvaluator 初始化成功。")

            except Exception as e:
                self._logger.error(f"初始化核心训练组件 (StateManager, ModelSaver, Evaluator) 失败: {e!s}", exc_info=True)
                raise RuntimeError(f"初始化核心训练组件失败: {e!s}") from e

            # 8. 记录完整初始化信息
            train_len = len(train_loader) if train_loader else 0
            val_len = len(val_loader) if val_loader else 0
            self._logger.info(
                f"GAN训练器初始化完成:\n"
                f"- 设备: {self.device}\n"
                f"- 混合精度: {'启用' if self.use_amp else '禁用'}\n"
                f"- 训练数据量: {train_len}\n"
                f"- 验证数据量: {val_len}\n"
                f"- 模型统计监控: {'启用' if self.stats_monitor else '禁用'}\n"
                f"- 错误监控: {'启用' if self.error_monitor else '禁用'}"
            )

        except Exception as e:
            self._logger.error(f"GAN训练器初始化失败: {e!s}")
            raise RuntimeError(f"GAN训练器初始化失败: {e!s}") from e

    def _init_monitoring_tools_impl(self):
        """初始化模型统计和错误监控工具"""
        self.stats_monitor = None
        self.error_monitor = None
        try:
            # 尝试从配置中获取监控设置
            monitoring_config = {} # Default to empty dict
            try:
                # 直接尝试访问 self.config_manager.monitoring 属性
                monitoring_config_section_obj = self.config_manager.monitoring
                if monitoring_config_section_obj is not None:
                    if hasattr(monitoring_config_section_obj, 'to_dict') and callable(getattr(monitoring_config_section_obj, 'to_dict')):
                        # 如果有to_dict方法，使用它
                        monitoring_config = monitoring_config_section_obj.to_dict()
                    elif hasattr(monitoring_config_section_obj, '__dict__'):
                        # 如果是数据类实例，使用dataclasses.asdict
                        from dataclasses import asdict, is_dataclass
                        if is_dataclass(monitoring_config_section_obj):
                            monitoring_config = asdict(monitoring_config_section_obj)
                        else:
                            # 如果是普通对象，使用__dict__
                            monitoring_config = monitoring_config_section_obj.__dict__
                    elif isinstance(monitoring_config_section_obj, dict):
                        monitoring_config = monitoring_config_section_obj
                    else:
                        self._logger.warning(
                            f"'monitoring' 配置节类型为 {type(monitoring_config_section_obj).__name__}，无法转换为字典。将使用默认监控设置。"
                        )
                else:
                    self._logger.warning("'monitoring' 配置节为None，将使用默认监控设置。")
            except AttributeError: # 'monitoring' 属性不存在于 ConfigManager 实例
                self._logger.warning("配置文件中未找到 'monitoring' 顶级配置节，或 ConfigManager 未设置 'monitoring' 属性。将使用监控工具的默认设置。")
            except Exception as e: # 捕获其他潜在错误，例如 to_dict() 失败
                self._logger.warning(f"获取 'monitoring' 配置时发生错误: {e!s}。将使用默认监控设置。")

            enable_stats_monitor = monitoring_config.get('enable_model_stats', False)
            enable_error_monitor = monitoring_config.get('enable_error_monitor', False)

            if enable_stats_monitor:
                # 从 monitoring_config 或默认值获取 ModelStatsMonitor 参数
                # 使用正确的字段名称映射
                stats_log_freq = monitoring_config.get('stats_log_frequency', 10)
                stats_detailed_freq = monitoring_config.get('stats_detailed_frequency', 100)
                # 构造日志路径，如果 monitoring_config 中有 stats_log_path
                stats_log_path_str = monitoring_config.get('stats_log_path', None)
                if stats_log_path_str is None and self.config_manager.paths and hasattr(self.config_manager.paths, 'logs_dir'):
                    stats_log_path_str = str(Path(self.config_manager.paths.logs_dir) / "model_stats.log")

                self.stats_monitor = ModelStatsMonitor(
                    log_frequency=stats_log_freq,
                    detailed_log_frequency=stats_detailed_freq,
                    log_path=stats_log_path_str,
                    track_weights=monitoring_config.get('track_weights', True),
                    track_gradients=monitoring_config.get('track_gradients', True),
                    track_activations=monitoring_config.get('track_activations', False),
                    nan_detection=monitoring_config.get('nan_detection', True)
                )
                self._logger.info("ModelStatsMonitor 初始化成功。")
            else:
                self._logger.warning("ModelStatsMonitor 未启用。")

            if enable_error_monitor:
                 # 从 monitoring_config 或默认值获取 ErrorMonitor 参数
                error_log_path_str = monitoring_config.get('error_log_path', None)
                if error_log_path_str is None and self.config_manager.paths and hasattr(self.config_manager.paths, 'logs_dir'):
                     error_log_path_str = str(Path(self.config_manager.paths.logs_dir) / "error_monitor.log")

                self.error_monitor = configure_error_monitor(
                    log_path=error_log_path_str,
                    enable_nan_detection=monitoring_config.get('enable_nan_detection_in_error_monitor', True), # Use a distinct key
                    enable_traceback=monitoring_config.get('enable_traceback', True),
                    max_errors=monitoring_config.get('max_errors', 5),
                    error_cooldown=monitoring_config.get('error_cooldown', 60)
                )
                self._logger.info("ErrorMonitor 初始化成功。")
            else:
                self._logger.warning("ErrorMonitor 未启用。")

        except AttributeError as e:
            self._logger.warning(f"初始化监控工具时配置缺失: {e!s}。监控功能可能受限。")
        except Exception as e:
            self._logger.error(f"初始化监控工具失败: {e!s}", exc_info=True)
            # 根据策略，可以选择不在这里抛出异常，允许训练在没有监控的情况下继续
            # 或者抛出异常以强制修复监控配置
            # raise RuntimeError(f"初始化监控工具失败: {e!s}") from e

    def _train_step(
        self,
        batch: dict[str, torch.Tensor],
        batch_idx: int = 0,  # 默认值，避免未使用警告
        total_batches: int = 1,  # 默认值，避免未使用警告
        step_time: float | None = None  # 新增：上一步的执行时间，用于批次大小优化
    ) -> dict[str, float]:
        """执行单个训练步骤

        Args:
            batch: 包含特征和目标的批次数据
            batch_idx: 当前批次索引
            total_batches: 总批次数
            step_time: 上一步的执行时间，用于批次大小优化

        使用GANModel的train_step方法执行完整的训练流程。

        Args:
            batch: 包含特征和目标的批次数据
            batch_idx: 当前批次索引
            total_batches: 总批次数

        Returns:
            Dict[str, float]: 包含生成器和判别器损失的指标字典
        """
        # 使用参数以避免未使用警告
        if batch_idx > 0 and total_batches > 1:
            self._logger.debug(f"处理批次 {batch_idx}/{total_batches}")

        # 如果提供了上一步执行时间，记录它
        if step_time is not None:
            self._logger.debug(f"上一步执行时间: {step_time:.4f}秒")

            # 如果有批次大小优化器，可以使用step_time进行优化
            if hasattr(self, 'batch_size_optimizer') and self.batch_size_optimizer:
                self._logger.debug(f"可以使用step_time={step_time:.4f}秒进行批次大小优化")
        try:
            # 准备数据 - 使用异步传输到设备，并指定数据流
            # 检查数据流状态
            if self.train_data_stream is None:
                raise ValueError("训练数据流未初始化，无法执行异步训练步骤")

            features = cuda_manager.move_to_device(batch['features'], non_blocking=True, stream=self.train_data_stream)
            targets = cuda_manager.move_to_device(batch['target'], non_blocking=True, stream=self.train_data_stream)
            self._logger.debug(f"异步传输数据到设备，使用流: {self.train_data_stream}")


            # 不再自动修正维度，直接要求输入符合要求
            if features.dim() != 3:
                raise ValueError(f"输入特征维度必须为3D，实际维度: {features.dim()}，形状: {features.shape}")

            # 使用GANModel的train_step方法
            # 确保model不为None且实现了train_step方法
            if self.model is None:
                error_msg = "模型为None，无法执行train_step"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            if not hasattr(self.model, 'train_step') or not callable(self.model.train_step):
                error_msg = "模型必须实现train_step方法"
                self._logger.error(error_msg)
                raise AttributeError(error_msg)

            # 调用train_step并验证返回值
            model = cast(TrainableModel, self.model)
            try:
                # 初始化指标字典和列表
                g_step_metrics_list = [] # 初始化列表以收集生成器指标
                d_metrics = {} # 初始化字典以存储判别器指标

                # 多次生成器训练步骤
                for g_step in range(self.current_g_steps):
                    is_d_step = (g_step == 0) # 定义is_d_step
                    # 检查数据流状态
                    if self.train_data_stream is None:
                        raise ValueError("训练数据流未初始化，无法执行异步训练步骤")

                    # 更新自适应梯度惩罚权重（如果启用）
                    # 注意：我们需要在判别器执行反向传播后更新lambda_gp，所以这段代码应该在train_step之后执行
                    # 但是为了保持代码结构，我们先记录是否需要更新lambda_gp，然后在train_step之后执行更新
                    should_update_lambda_gp = False
                    if hasattr(self, 'adaptive_lambda_gp_manager') and self.adaptive_lambda_gp_manager is not None and g_step == 0 and is_d_step:  # 只在判别器训练步骤更新
                            # 确保模型是GANModel类型
                            from src.models.gan.gan_model import GANModel
                            if isinstance(model, GANModel) and hasattr(model, 'discriminator') and model.discriminator is not None:
                                should_update_lambda_gp = True
                                self._logger.info("将在train_step之后更新自适应lambda_gp")
                            else:
                                self._logger.warning("模型不是GANModel类型或没有discriminator属性，无法更新自适应lambda_gp")

                    step_result = model.train_step(
                        features=features,
                        targets=targets,
                        data_stream=self.train_data_stream, # 传递数据流
                        n_critic=self.current_n_critic if g_step == 0 else 0,  # 只在第一次迭代执行判别器训练
                        current_lambda_gp=self.current_lambda_gp # 使用可能已更新的lambda_gp值
                    )

                    # 在train_step之后更新lambda_gp（如果需要）
                    if should_update_lambda_gp and self.adaptive_lambda_gp_manager is not None:
                        # 重新导入GANModel以避免IDE警告
                        from src.models.gan.gan_model import GANModel
                        if isinstance(model, GANModel) and hasattr(model, 'discriminator') and model.discriminator is not None:
                            try:
                                # 使用模型的判别器更新lambda_gp
                                if hasattr(self.adaptive_lambda_gp_manager, 'update') and callable(self.adaptive_lambda_gp_manager.update):
                                    self.current_lambda_gp = self.adaptive_lambda_gp_manager.update(model.discriminator)
                                    self._logger.info(f"自适应lambda_gp更新: {self.current_lambda_gp:.4f}")
                                else:
                                    self._logger.warning("adaptive_lambda_gp_manager没有update方法")
                            except Exception as e:
                                self._logger.warning(f"更新自适应lambda_gp时出错: {e!s}")
                        else:
                            self._logger.warning("模型不是GANModel类型或没有discriminator属性，无法更新自适应lambda_gp")
                    if not isinstance(step_result, dict):
                        error_msg = "train_step必须返回字典类型"
                        self._logger.error(error_msg)
                        raise TypeError(error_msg)

                    # 记录当前步骤的指标
                    if is_d_step:
                        d_metrics = step_result # Store discriminator metrics (includes G metrics too)
                        # Also add G metrics from D step to the list for averaging G metrics later
                        g_metrics_from_d_step = {k: v for k, v in step_result.items() if k.startswith('g_')}
                        if g_metrics_from_d_step:
                             g_step_metrics_list.append(g_metrics_from_d_step)
                    else:
                        # Store only generator metrics from subsequent steps
                        g_metrics_from_g_step = {k: v for k, v in step_result.items() if k.startswith('g_')}
                        if g_metrics_from_g_step:
                             g_step_metrics_list.append(g_metrics_from_g_step)


                # 计算平均生成器指标 (if any generator steps were run)
                final_metrics = {} # Renamed from metrics to avoid confusion before assignment
                if g_step_metrics_list:
                    avg_g_metrics = {}
                    # Get all unique generator keys across all steps
                    all_g_keys = {key for step in g_step_metrics_list for key in step}
                    for k in all_g_keys:
                         # Safely sum values, skipping steps where the key might be missing (though unlikely for G keys)
                         # Ensure values are numeric before summing
                         valid_values = [step.get(k) for step in g_step_metrics_list if isinstance(step.get(k), int | float | torch.Tensor) and not torch.isnan(torch.tensor(float(step.get(k))))]
                         if valid_values:
                             # Convert tensors to float before summing
                             numeric_values = [v.item() if isinstance(v, torch.Tensor) else float(v) for v in valid_values]
                             avg_g_metrics[k] = sum(numeric_values) / len(numeric_values)
                         else:
                             avg_g_metrics[k] = float('nan') # Or handle as appropriate
                    final_metrics.update(avg_g_metrics)

                # 添加判别器指标 (if discriminator was run)
                if d_metrics:
                    # Add only the D-specific metrics from the d_metrics dict
                    d_specific_metrics = {k: v for k, v in d_metrics.items() if k.startswith('d_')}
                    final_metrics.update(d_specific_metrics)
                    # Ensure the main g_loss and d_loss are present if available in d_metrics
                    # Check if g_loss exists in d_metrics and is not already in final_metrics from avg_g_metrics
                    if 'g_loss' in d_metrics and 'g_loss' not in final_metrics:
                         final_metrics['g_loss'] = d_metrics['g_loss']
                    # Check if d_loss exists in d_metrics (it should if d_metrics is not empty and D ran)
                    if 'd_loss' in d_metrics and 'd_loss' not in final_metrics:
                         final_metrics['d_loss'] = d_metrics['d_loss']

            except Exception as inner_e:
                error_msg = f"内部训练步骤失败 (模型调用或指标计算): {inner_e!s}"
                self._logger.error(error_msg, exc_info=True)
                # Propagate the error to the outer handler or raise a specific exception
                raise RuntimeError(error_msg) from inner_e

            # 仅记录关键训练指标
            # Assuming log_performance is handled by system_monitor or similar
            # system_monitor.log_performance({
            #     'batch_g_loss': metrics.get('g_loss', float('nan')), # Use .get for safety
            #     'batch_d_loss': metrics.get('d_loss', float('nan'))
            # })

            return final_metrics # Return the correctly aggregated metrics

        except Exception as e:
            # Fixed missing str(e) in the original error message log
            error_msg = f"GAN训练步骤失败: {e!s}"
            self._logger.error(error_msg, exc_info=True) # Add traceback
            self.state_manager.log_component_state('trainer', False, error_msg)
            raise RuntimeError(error_msg) from e

    def init_mixed_precision(self, config):
        """初始化混合精度训练设置

        从配置中读取混合精度设置并初始化GradScaler。
        如果配置中未指定，则使用默认值。

        Args:
            config: 混合精度配置对象
        """
        try:
            # 不使用回退值，必须提供明确的配置
            if config is None:
                raise ValueError("必须提供明确的混合精度配置(MixedPrecisionConfig)，不接受None值")

            # 传入的 config 参数本身就应该是 mixed_precision_config 对象
            mixed_precision_config = config

            if not hasattr(mixed_precision_config, 'enabled'):
                raise ValueError("mixed_precision配置必须包含enabled参数")

            self.amp_enabled = mixed_precision_config.enabled

            # 要求配置中必须明确指定dtype
            if not hasattr(mixed_precision_config, 'dtype'):
                raise ValueError("mixed_precision配置必须包含dtype参数")

            self.amp_dtype = torch.float16 if mixed_precision_config.dtype == 'float16' else torch.bfloat16

            # 初始化GradScaler - 使用正确的导入路径
            from torch.amp.grad_scaler import GradScaler
            self.grad_scaler = GradScaler(
                init_scale=getattr(mixed_precision_config, 'init_scale', 2.0**16),
                growth_factor=getattr(mixed_precision_config, 'growth_factor', 2.0),
                backoff_factor=getattr(mixed_precision_config, 'backoff_factor', 0.5),
                growth_interval=getattr(mixed_precision_config, 'growth_interval', 2000),
                enabled=self.amp_enabled
            )

            self._logger.info(
                f"混合精度训练已{'启用' if self.amp_enabled else '禁用'} "
                f"(dtype={'float16' if self.amp_dtype == torch.float16 else 'bfloat16'})"
            )

        except Exception as e:
            self._logger.error(f"初始化混合精度训练失败: {e!s}")
            raise

    def _train_epoch(self, train_loader: DataLoader, current_epoch: int | None = None) -> dict[str, float]:
        """训练一个轮次

        Args:
            train_loader: 训练数据加载器
            current_epoch: 当前轮次编号 (可选)

        Returns:
            Dict[str, float]: 训练指标
        """
        # --- 确保 current_epoch 是整数 ---
        if current_epoch is None:
            # 尝试从状态管理器获取，如果失败则使用默认值 0
            try:
                current_epoch = getattr(self.state_manager.training_state, 'epoch', 0)
                self._logger.warning(f"未传入current_epoch参数，从状态管理器获取: {current_epoch}")
            except AttributeError:
                 self._logger.warning("状态管理器或其状态不可用，current_epoch 使用默认值 0")
                 current_epoch = 0
        # 断言 current_epoch 现在是整数
        assert isinstance(current_epoch, int), f"_train_epoch: current_epoch 应该是整数，但得到 {type(current_epoch)}"
        # --- 结束确保 ---


        # 记录当前epoch
        self._logger.info(f"开始训练轮次 {current_epoch}")

        try:
            # 确保模型不为None
            if self.model is None:
                raise ValueError("模型未初始化，无法进行训练")
            self.model.train()
            metrics_sum = defaultdict(float)
            num_batches = len(train_loader) # Use the passed train_loader
            last_step_time = None

            # 记录当前批次大小，用于OOM恢复
            # current_batch_size = train_loader.batch_size  # 暂时注释掉未使用的变量

            # 初始化计数器和数据迭代器
            batch_idx = 0
            processed_batches = 0
            data_iterator = iter(train_loader)

            # 批次处理循环
            while batch_idx < num_batches:
                try:
                    # 获取批次数据
                    batch = next(data_iterator)

                    # 记录步骤开始时间
                    step_start_time = time.time()

                    # 重置批次内峰值内存统计
                    if cuda_manager is not None:
                        try:
                            cuda_manager.reset_batch_peak_memory()
                            self._logger.debug(f"已重置批次{batch_idx}的峰值内存统计")
                        except Exception as e:
                            self._logger.warning(f"重置批次峰值内存统计失败: {e!s}")

                    # 递增全局步数计数器 - 仅用于记录批次数，与Warmup无关
                    self._global_step += 1

                    # 训练步骤前检查模型参数
                    if hasattr(self, 'stats_monitor') and self.stats_monitor and batch_idx == 0:
                        # 注册模型以进行统计监控
                            self.stats_monitor.register_model(self.model)

                    # 初始化batch_metrics为空字典，确保在异常情况下也有定义
                    batch_metrics = {'g_loss': float('nan'), 'd_loss': float('nan')}

                    # 训练步骤（添加批次信息）
                    batch_metrics = self._train_step(
                        batch=batch,
                        batch_idx=batch_idx,
                        total_batches=num_batches,
                        step_time=last_step_time
                    )

                    # 训练步骤后记录模型统计信息
                    if hasattr(self, 'stats_monitor') and self.stats_monitor:
                        is_valid = self.stats_monitor.log_step(
                            model=self.model,
                            losses=batch_metrics,
                            step_type=f"训练轮次{current_epoch}-批次{batch_idx}"
                        )
                        # 如果检测到NaN/Inf，提前停止训练并调用剪枝回调
                        if not is_valid:
                            self._logger.error(f"轮次{current_epoch}批次{batch_idx}检测到NaN/Inf，提前停止训练")
                            # 使用错误监控记录详细信息，error_monitor必须存在
                            if not hasattr(self, 'error_monitor') or self.error_monitor is None:
                                error_msg = "error_monitor 不存在，无法记录NaN详细信息"
                                self._logger.error(error_msg)
                                raise ValueError(error_msg)

                            self.error_monitor.early_stop_on_nan(
                                loss=torch.tensor(batch_metrics.get('g_loss', float('nan'))),
                                step=batch_idx,
                                epoch=current_epoch
                            )
                            # 调用剪枝回调（如果存在）
                            if self.pruning_callback and callable(self.pruning_callback):
                                self._logger.info("检测到NaN/Inf，调用剪枝回调...")
                                self.pruning_callback() # 这将引发 TrialPruned 异常
                            # 设置batch_metrics为NaN，确保后续处理不会出错
                            batch_metrics = {'g_loss': float('nan'), 'd_loss': float('nan')}
                            break # 仍然跳出当前轮次的批处理循环

                    # 计算步骤执行时间
                    last_step_time = time.time() - step_start_time

                    # 获取批次内峰值内存信息
                    batch_peak_memory_info = None
                    if cuda_manager is not None:
                        try:
                            batch_peak_memory_info = cuda_manager.get_batch_peak_memory_info()
                            if batch_peak_memory_info:
                                self._logger.debug(
                                    f"批次{batch_idx}峰值内存利用率: "
                                    f"{batch_peak_memory_info.utilization*100:.1f}% "
                                    f"({batch_peak_memory_info.used_gb:.2f}GB)"
                                )
                        except Exception as e:
                            self._logger.warning(f"获取批次峰值内存信息失败: {e!s}")

                    # 累积指标
                    for k, v in batch_metrics.items():
                        # 确保值是数字且不是NaN
                        if isinstance(v, int | float) and not math.isnan(float(v)):
                            metrics_sum[k] += v
                        elif isinstance(v, torch.Tensor) and not torch.isnan(v).any():
                            metrics_sum[k] += v.item()  # 转换张量为浮点数

                    # 如果启用了动态批次大小，更新批次大小优化器
                    if hasattr(self, 'batch_size_optimizer') and self.batch_size_optimizer:
                        try:
                            # 使用当前批次的损失和执行时间更新批次大小
                            g_loss = batch_metrics.get('g_loss', None)
                            d_loss = batch_metrics.get('d_loss', None)

                            # 计算平均损失
                            avg_loss = None
                            if g_loss is not None and d_loss is not None and not math.isnan(float(g_loss)) and not math.isnan(float(d_loss)):
                                avg_loss = (g_loss + d_loss) / 2
                            elif g_loss is not None and not math.isnan(float(g_loss)):
                                avg_loss = g_loss
                            elif d_loss is not None and not math.isnan(float(d_loss)):
                                avg_loss = d_loss

                            # 更新批次大小优化器，传递批次内峰值内存信息
                            if avg_loss is not None:
                                # 使用批次内峰值内存信息更新批次大小优化器
                                # 批次内峰值内存信息已经在内部被记录，不需要额外传递
                                self.batch_size_optimizer.step(loss=avg_loss, step_time=last_step_time)
                        except Exception as e:
                            self._logger.error(f"更新批次大小优化器失败: {e!s}")
                            raise

                    # 记录进度 (和可选的步级学习率日志)
                    log_freq = 1  # 每个批次都记录损失
                    if (batch_idx + 1) % log_freq == 0:
                        self._logger.info(
                            f"训练进度 - 轮次 {current_epoch}: {batch_idx+1}/{num_batches} "
                            f"[{(batch_idx+1)/num_batches*100:.1f}%] "
                            f"G损失: {batch_metrics.get('g_loss', 0.0):.4f}, D损失: {batch_metrics.get('d_loss', 0.0):.4f}"
                        )
                        # 每 log_freq 步记录一次学习率
                        current_g_lr = self.optimizer_manager.get_learning_rate(self.g_optimizer)
                        current_d_lr = self.optimizer_manager.get_learning_rate(self.d_optimizer)
                        self._logger.info(f"   Step {batch_idx+1} LR: G={current_g_lr:.6f}, D={current_d_lr:.6f}")

                    # 更新批次索引和已处理批次计数
                    batch_idx += 1
                    processed_batches += 1

                except Exception as e:
                    # 使用错误监控记录异常，但不尝试自动恢复
                    if hasattr(self, 'error_monitor') and self.error_monitor:
                        # 只记录异常，不尝试继续执行
                        self.error_monitor.handle_exception(
                            e,
                            context=f"训练轮次{current_epoch}批次{batch_idx}"
                        )

                    # 直接抛出异常，不掩盖问题
                    raise

                    # 移除OOM恢复机制的无效代码

            # 计算平均指标
            if processed_batches == 0:
                raise ValueError("没有成功处理任何批次，无法计算指标")

            metrics = {k: v / processed_batches for k, v in metrics_sum.items()}

            # 记录轮次指标
            log_message = f"\n训练轮次 {current_epoch} 完成:\n"
            log_message += f"- 平均生成器损失: {metrics.get('g_loss', float('nan')):.4f}\n"
            log_message += f"- 平均判别器损失: {metrics.get('d_loss', float('nan')):.4f}\n"

            # 添加梯度范数记录
            if 'g_grad_norm' in metrics:
                log_message += f"- 生成器梯度范数: {metrics.get('g_grad_norm', float('nan')):.4f}\n"
            if 'd_grad_norm' in metrics:
                log_message += f"- 判别器梯度范数: {metrics.get('d_grad_norm', float('nan')):.4f}\n"

            # 添加损失函数各独立分量记录
            # 生成器损失分量
            log_message += "\n生成器损失分量:\n"
            for key, value in metrics.items():
                if key.startswith('g_') and key not in {'g_loss', 'g_grad_norm'}:
                    log_message += f"- {key}: {value:.4f}\n"

            # 判别器损失分量
            log_message += "\n判别器损失分量:\n"
            for key, value in metrics.items():
                if key.startswith('d_') and key not in {'d_loss', 'd_grad_norm'}:
                    log_message += f"- {key}: {value:.4f}\n"

            self._logger.info(log_message)

            return metrics

        except Exception as e:
            self._logger.error(f"训练轮次失败 - 轮次{current_epoch}: {e!s}", exc_info=True) # Add traceback
            raise RuntimeError(f"训练轮次失败: {e!s}") from e

    def evaluate(self, test_loader):
        """评估模型

        Args:
            test_loader: 测试数据加载器

        Returns:
            Dict[str, float]: 评估指标
        """
        return self._validate_epoch(test_loader)

    def _validate_epoch(self, val_loader: DataLoader) -> dict[str, float]:
        """执行验证流程

        Args:
            val_loader: 验证数据加载器

        Returns:
            Dict[str, float]: 验证指标
        """
        try:
            # 确保模型不为None
            if self.model is None:
                raise ValueError("模型未初始化，无法进行验证")
            self.model.eval()
            metrics_sum = defaultdict(float)
            num_batches = len(val_loader)

            # 检查评估器状态 - 简化逻辑，不再尝试自动修复
            if not hasattr(self, 'evaluator') or self.evaluator is None:
                self._logger.error("评估器未初始化，无法进行验证")
                raise ValueError("评估器未初始化，请在验证前确保评估器已正确初始化")

            # 确保target_standardizer已设置
            if self.target_standardizer is None:
                raise ValueError("target_standardizer未设置，无法进行验证")

            with torch.no_grad():
                for batch_idx, batch in enumerate(val_loader):
                    # 验证批次 - 使用异步传输
                    # 检查数据流状态
                    if self.val_data_stream is None:
                        raise ValueError("验证数据流未初始化，无法执行异步验证步骤")

                    features = cuda_manager.move_to_device(batch['features'], non_blocking=True, stream=self.val_data_stream)
                    targets = cuda_manager.move_to_device(batch['target'], non_blocking=True, stream=self.val_data_stream)
                    # 注意: 验证步骤通常不需要与训练并行，但异步传输本身仍有好处

                    # 使用评估器计算指标
                    try:
                        # 确保模型不为None
                        if self.model is None:
                            raise ValueError("模型未初始化，无法进行评估")
                        # 确保评估器已初始化
                        if self.evaluator is None:
                             raise RuntimeError("评估器未成功初始化")

                        # 初始化metrics为空字典，确保在异常情况下也有定义
                        metrics = {}

                        # 使用模型统计监控记录验证过程中的模型状态
                        if hasattr(self, 'stats_monitor') and self.stats_monitor and batch_idx == 0:
                            # 只在第一个批次注册模型
                            self.stats_monitor.register_model(self.model)
                            self._logger.info("已注册模型用于验证过程中的统计监控")

                        # 评估模型 - 确保使用反标准化后的MAE
                        metrics = self.evaluator.evaluate(
                            self.model,
                            {
                                'features': features,
                                'target': targets
                            }
                        )

                        # 记录反标准化后的MAE用于一致性检查
                        if 'val_mae' in metrics:
                            self._logger.debug(f"验证批次{batch_idx} - 反标准化MAE: {metrics['val_mae']:.4f}")

                        # 使用模型统计监控记录验证指标
                        if hasattr(self, 'stats_monitor') and self.stats_monitor:
                            is_valid = self.stats_monitor.log_step(
                                model=self.model,
                                losses=metrics,
                                step_type=f"验证批次{batch_idx}"
                            )
                            # 如果检测到NaN/Inf，记录详细信息
                            if not is_valid:
                                self._logger.error(f"验证批次{batch_idx}检测到NaN/Inf")
                                # 使用错误监控记录详细信息
                                if hasattr(self, 'error_monitor') and self.error_monitor:
                                    self.error_monitor.early_stop_on_nan(
                                        loss=torch.tensor(metrics.get('loss', float('nan'))),
                                        step=batch_idx,
                                        epoch=-1  # 验证没有轮次概念
                                    )

                        # 检查指标中是否有NaN
                        if any(torch.isnan(torch.tensor(float(v))) for v in metrics.values() if isinstance(v, int | float)):
                            self._logger.warning(f"评估指标在批次 {batch_idx} 中包含NaN值: {metrics}")
                            # 直接抛出异常，不掩盖问题
                            raise ValueError(f"评估指标在批次 {batch_idx} 中包含NaN值: {metrics}")

                        # 更新评估器状态
                        self.state_manager.log_component_state(
                            'evaluator',
                            True,
                            f"评估成功 - batch {batch_idx+1}/{num_batches}"
                        )
                    except Exception:
                        # 直接抛出异常，不掩盖问题
                        raise

                    # 记录验证批次性能指标
                    if batch_idx % 10 == 0:
                        # 创建性能日志
                        # 使用 'loss' 作为基础损失键，fit 方法会添加 'val_' 前缀
                        performance_log = {
                            'val_batch_loss': metrics.get('loss', float('nan')), # Use .get for safety with 'loss'
                        }

                        # 添加其他指标
                        for k, v in metrics.items():
                            if k != 'val_loss':
                                performance_log[f'val_{k}'] = v

                        # 记录性能
                        # system_monitor.log_performance(performance_log) # Commented out

                    # 累积指标
                    for k, v in metrics.items():
                         # Ensure value is a number and not NaN before adding
                        if isinstance(v, int | float) and not torch.isnan(torch.tensor(float(v))):
                             metrics_sum[k] += v
                        elif isinstance(v, torch.Tensor) and not torch.isnan(v):
                             metrics_sum[k] += v.item() # Convert tensor to float


                    # 记录进度
                    if (batch_idx + 1) % 1 == 0:  # 每个批次都记录
                        self._logger.info(
                            f"验证进度: {batch_idx+1}/{num_batches} "
                            f"[{(batch_idx+1)/num_batches*100:.1f}%] "
                            # 修正：打印实际存在的指标，使用正确的键 'val_mae'
                            f"验证 MAE: {metrics.get('val_mae', 0.0):.4f}"
                        )
            # 计算平均指标
            if num_batches == 0:
                raise ValueError("验证数据集为空，无法计算指标")

            metrics = {k: v / num_batches for k, v in metrics_sum.items()}

            # 直接返回带有 val_ 前缀的指标
            return metrics

        except Exception as e:
            self._logger.error(f"验证失败: {e!s}", exc_info=True) # Add traceback
            raise RuntimeError(f"验证失败: {e!s}") from e

    # 移除 train_model 方法
    # def train_model(self, train_loader, val_loader): ...

    def train(self, mode: bool = True) -> 'BaseModel':
        """切换训练/评估模式 (覆盖基类方法)

        Args:
            mode: 是否设置为训练模式 (True) 或评估模式 (False)

        Returns:
            BaseModel: 当前模型实例
        """
        if self.model is not None:
            self.model.train(mode)
        return self

    # 重命名 fit 为 run_training_loop 并调整参数顺序
    def run_training_loop(
        self,
        batch_size: int, # 设为必需参数，放在前面
        train_dataset: SizedDataset | Dataset | None = None,
        val_dataset: SizedDataset | Dataset | None = None,
        num_workers: int = 0,
        num_epochs: int | None = None, # Allow num_epochs from config
        dynamic_batch_size: bool = False, # 新增：是否启用动态批次大小
        pruning_callback: Callable | None = None # 新增：剪枝回调函数 # Error 6: Callable was not defined
    ) -> dict[str, list[float]]:
        """执行训练循环

        Args:
            batch_size: 批量大小 (必需)
            train_dataset: 训练数据集（可选，优先使用已设置的loader）
            val_dataset: 验证数据集（可选，优先使用已设置的loader）
            num_workers: 数据加载线程数 (如果提供了数据集)
            num_epochs: 训练轮数 (优先使用此参数，否则从配置读取)
            dynamic_batch_size: 是否启用动态批次大小调整 (默认: False)

        Returns:
            Dict[str, List[float]]: 包含训练历史记录的字典，键为指标名称，值为每个轮次的指标列表
        """
        # 保存剪枝回调
        self.pruning_callback = pruning_callback
        if self.pruning_callback:
            self._logger.info("已设置剪枝回调函数")

        # 保存训练数据集引用，用于OOM恢复
        if train_dataset is not None:
            self.train_dataset = train_dataset
        try:
            # 1. 训练前准备
            self.state_manager.transition_to(ModelState.TRAINING)
            epoch_metrics = defaultdict(list)

            # 初始化批次大小优化器（如果启用）
            self.batch_size_optimizer = None
            self._logger.info(f"动态批次大小设置: {dynamic_batch_size}")

            if dynamic_batch_size:
                try:
                    self._logger.info("开始初始化批次大小优化器...")
                    # Import the factory and the get_optimizer function
                    from src.utils.config.batch_size_optimizer import (
                        create_batch_size_optimizer_config,
                        get_batch_size_optimizer,
                    )

                    # Get the fully populated BatchSizeOptimizerConfig instance from the main config
                    optimizer_config_from_yaml = create_batch_size_optimizer_config(self.config_manager)

                    # Create a dictionary with all required parameters for BatchSizeOptimizerConfig
                    # Override initial_batch_size with the one passed to run_training_loop
                    # and ensure 'enabled' is True if dynamic_batch_size is True.
                    final_optimizer_config_params = {
                        "enabled": True, # If dynamic_batch_size is true, this should be true
                        "initial_batch_size": batch_size, # Use the batch_size passed to run_training_loop
                        "min_batch_size": optimizer_config_from_yaml.min_batch_size,
                        "max_batch_size": optimizer_config_from_yaml.max_batch_size,
                        "memory_utilization_target": optimizer_config_from_yaml.memory_utilization_target,
                        "strategy": optimizer_config_from_yaml.strategy,
                        "adjustment_interval": optimizer_config_from_yaml.adjustment_interval,
                        "growth_factor": optimizer_config_from_yaml.growth_factor,
                        "shrink_factor": optimizer_config_from_yaml.shrink_factor,
                        "stability_threshold": optimizer_config_from_yaml.stability_threshold,
                        "warmup_steps": optimizer_config_from_yaml.warmup_steps,
                        "oom_recovery": optimizer_config_from_yaml.oom_recovery,
                        "noise_dim": optimizer_config_from_yaml.noise_dim,
                        "dimensions": optimizer_config_from_yaml.dimensions
                    }

                    # Import the config class itself for instantiation
                    from src.utils.config.batch_size_optimizer import (
                        BatchSizeOptimizerConfig,
                    )
                    final_optimizer_config = BatchSizeOptimizerConfig(**final_optimizer_config_params)

                    self._logger.info("正在创建批次大小优化器实例...")
                    self.batch_size_optimizer = get_batch_size_optimizer(final_optimizer_config)

                    self._logger.info(
                        f"批次大小优化器已初始化成功:\n"
                        f"- 初始批次大小: {final_optimizer_config.initial_batch_size}\n"
                        f"- 最小批次大小: {final_optimizer_config.min_batch_size}\n"
                        f"- 最大批次大小: {final_optimizer_config.max_batch_size}\n"
                        f"- 目标内存利用率: {final_optimizer_config.memory_utilization_target * 100:.0f}%\n"
                        f"- 调整策略: {final_optimizer_config.strategy}"
                    )
                    current_bs = self.batch_size_optimizer.get_batch_size()
                    self._logger.info(f"当前批次大小: {current_bs}")

                except Exception as e:
                    self._logger.error(f"初始化批次大小优化器失败: {e!s}", exc_info=True)
                    # 直接抛出异常，不掩盖问题
                    raise

            # 定义数据集适配器类
            class DatasetAdapter(Dataset):
                def __init__(self, dataset):
                    self.dataset = dataset
                def __len__(self):
                    return len(self.dataset)
                def __getitem__(self, idx):
                    return self.dataset[idx]

            # 确定数据加载器
            if train_dataset is not None:
                # 确保数据集实现了Dataset协议
                if not isinstance(train_dataset, Dataset):
                    self._logger.warning(f"train_dataset类型为{type(train_dataset).__name__}，可能不兼容DataLoader")
                    # 尝试将SizedDataset适配为Dataset
                    if hasattr(train_dataset, '__len__') and hasattr(train_dataset, '__getitem__'):
                        train_dataset = DatasetAdapter(train_dataset)
                        self._logger.info("已将train_dataset适配为Dataset")
                    else:
                        raise TypeError(f"train_dataset类型{type(train_dataset).__name__}不支持DataLoader")

                # 使用当前批次大小创建数据加载器
                current_batch_size = self.batch_size_optimizer.get_batch_size() if self.batch_size_optimizer else batch_size
                self.train_loader = DataLoader(
                    train_dataset, batch_size=current_batch_size, shuffle=True, num_workers=num_workers
                )

            if val_dataset is not None:
                # 确保数据集实现了Dataset协议
                if not isinstance(val_dataset, Dataset):
                    self._logger.warning(f"val_dataset类型为{type(val_dataset).__name__}，可能不兼容DataLoader")
                    # 尝试将SizedDataset适配为Dataset
                    if hasattr(val_dataset, '__len__') and hasattr(val_dataset, '__getitem__'):
                        val_dataset = DatasetAdapter(val_dataset)
                        self._logger.info("已将val_dataset适配为Dataset")
                    else:
                        raise TypeError(f"val_dataset类型{type(val_dataset).__name__}不支持DataLoader")

                # 验证数据加载器使用固定批次大小
                self.val_loader = DataLoader(
                    val_dataset, batch_size=batch_size, shuffle=False, num_workers=num_workers
                )

            # 检查数据加载器是否已设置
            if self.train_loader is None or self.val_loader is None:
                 raise ValueError("训练和验证数据加载器必须设置或通过数据集提供")

            # 在确定 DataLoader 后初始化数据流
            # 创建新的CUDA流用于数据传输
            self.train_data_stream = cuda_manager.create_stream("train_data_transfer")
            self.val_data_stream = cuda_manager.create_stream("val_data_transfer")

            self._logger.info(f"已创建训练数据流: {self.train_data_stream}")
            self._logger.info(f"已创建验证数据流: {self.val_data_stream}")


            # 确定训练轮数
            if num_epochs is None:
                num_epochs = self.config_manager.training.num_epochs

            # 2. 初始化优化器并设置到模型
            # 确保模型不为None且具有必要的属性
            if self.model is None:
                raise ValueError("模型未初始化，无法设置优化器")

            if not hasattr(self.model, 'generator') or self.model.generator is None:
                raise ValueError("模型缺少generator属性或generator为None")

            if not hasattr(self.model, 'discriminator') or self.model.discriminator is None:
                raise ValueError("模型缺少discriminator属性或discriminator为None")

            # 为生成器创建优化器，明确指定 model_type
            self.g_optimizer = self.optimizer_manager.create_optimizer(
                cast(nn.Module, self.model.generator),
                model_type='generator'
            )
            # 为判别器创建优化器，明确指定 model_type
            self.d_optimizer = self.optimizer_manager.create_optimizer(
                cast(nn.Module, self.model.discriminator),
                model_type='discriminator'
            )


            # 确保模型有set_optimizers方法
            if not hasattr(self.model, 'set_optimizers') or not callable(self.model.set_optimizers):
                raise ValueError("模型缺少set_optimizers方法")

            self.model.set_optimizers(self.g_optimizer, self.d_optimizer)
            self._logger.info(
                f"优化器初始化完成:\n"
                f"- 生成器优化器: {type(self.g_optimizer).__name__}\n"
                f"- 判别器优化器: {type(self.d_optimizer).__name__}"
            )

            # 3.1 初始化学习率调整机制 (调度器或平衡器)
            self.lr_balancer = None
            self.g_scheduler = None
            self.d_scheduler = None

            # 首先尝试直接从training属性中获取lr_balancer配置
            lr_balancer_config = None
            if hasattr(self.config_manager, 'training') and hasattr(self.config_manager.training, 'lr_balancer'):
                lr_balancer_config = self.config_manager.training.lr_balancer
                self._logger.debug(f"从training属性中获取到lr_balancer配置: {lr_balancer_config}")
            else:
                # 如果直接访问失败，尝试使用get方法
                lr_balancer_config = self.config_manager.get('training.lr_balancer')
                self._logger.debug(f"使用get方法获取到lr_balancer配置: {lr_balancer_config}")

            # 检查lr_balancer配置是否存在且已启用
            is_balancer_enabled = False
            if lr_balancer_config is not None:
                # 'enabled' is mandatory in LRBalancerConfig now
                if isinstance(lr_balancer_config, dict):
                    is_balancer_enabled = lr_balancer_config['enabled'] # Direct access
                else:
                    is_balancer_enabled = lr_balancer_config.enabled # Direct access

            # 根据配置决定使用哪种学习率调整机制
            if is_balancer_enabled:
                # 只支持增强版多指标学习率平衡器
                self._logger.info("启用增强版多指标学习率平衡器，将使用 EnhancedGanLrBalancer。")
                try:
                    # 获取增强版配置参数
                    if isinstance(lr_balancer_config, dict):
                        target_ratio = float(lr_balancer_config['target_ratio'])
                        sensitivity = float(lr_balancer_config['sensitivity'])
                        min_lr = float(lr_balancer_config['min_lr'])
                        max_lr = float(lr_balancer_config['max_lr'])
                        epsilon = float(lr_balancer_config['epsilon'])
                        performance_weight = float(lr_balancer_config.get('performance_weight', 0.3))
                        stability_weight = float(lr_balancer_config.get('stability_weight', 0.2))
                        loss_ratio_weight = float(lr_balancer_config.get('loss_ratio_weight', 0.5))
                        history_window = int(lr_balancer_config.get('history_window', 5))
                        # 新增：调整模式参数
                        adjustment_mode = lr_balancer_config.get('adjustment_mode', 'adaptive')
                        mode_switch_threshold = float(lr_balancer_config.get('mode_switch_threshold', 0.3))
                        improvement_threshold = float(lr_balancer_config.get('improvement_threshold', 0.05))
                        stability_threshold = float(lr_balancer_config.get('stability_threshold', 0.1))
                    else:
                        target_ratio = float(lr_balancer_config.target_ratio)
                        sensitivity = float(lr_balancer_config.sensitivity)
                        min_lr = float(lr_balancer_config.min_lr)
                        max_lr = float(lr_balancer_config.max_lr)
                        epsilon = float(lr_balancer_config.epsilon)
                        performance_weight = float(getattr(lr_balancer_config, 'performance_weight', 0.3))
                        stability_weight = float(getattr(lr_balancer_config, 'stability_weight', 0.2))
                        loss_ratio_weight = float(getattr(lr_balancer_config, 'loss_ratio_weight', 0.5))
                        history_window = int(getattr(lr_balancer_config, 'history_window', 5))
                        improvement_threshold = float(getattr(lr_balancer_config, 'improvement_threshold', 0.05))
                        stability_threshold = float(getattr(lr_balancer_config, 'stability_threshold', 0.1))
                        # 新增：调整模式参数
                        adjustment_mode = getattr(lr_balancer_config, 'adjustment_mode', 'adaptive')
                        mode_switch_threshold = float(getattr(lr_balancer_config, 'mode_switch_threshold', 0.3))

                    # 记录增强版配置参数
                    self._logger.info(
                        f"EnhancedGanLrBalancer 配置参数:\n"
                        f"- 目标比率: {target_ratio}\n"
                        f"- 敏感度: {sensitivity}\n"
                        f"- 学习率范围: [{min_lr:.6f}, {max_lr:.6f}]\n"
                        f"- 权重分配: 损失比率={loss_ratio_weight:.2f}, 性能={performance_weight:.2f}, 稳定性={stability_weight:.2f}\n"
                        f"- 历史窗口: {history_window}, 改善阈值: {improvement_threshold:.3f}, 稳定性阈值: {stability_threshold:.3f}\n"
                        f"- 调整模式: {adjustment_mode}, 模式切换阈值: {mode_switch_threshold:.2f}"
                    )

                    # 创建增强版学习率平衡器
                    self.lr_balancer = EnhancedGanLrBalancer(
                        optimizer_g=self.g_optimizer,
                        optimizer_d=self.d_optimizer,
                        target_ratio=target_ratio,
                        sensitivity=sensitivity,
                        min_lr=min_lr,
                        max_lr=max_lr,
                        epsilon=epsilon,
                        performance_weight=performance_weight,
                        stability_weight=stability_weight,
                        loss_ratio_weight=loss_ratio_weight,
                        history_window=history_window,
                        improvement_threshold=improvement_threshold,
                        stability_threshold=stability_threshold,
                        adjustment_mode=adjustment_mode,
                        mode_switch_threshold=mode_switch_threshold,
                        logger_name=self.__class__.__name__ + ".EnhancedLrBalancer"
                    )
                    self._logger.info("EnhancedGanLrBalancer 初始化成功。")
                except Exception as e:
                    self._logger.error(f"初始化 EnhancedGanLrBalancer 失败: {e}", exc_info=True)
                    raise RuntimeError(f"初始化 EnhancedGanLrBalancer 失败: {e}") from e

                # 禁用原有的调度器
                self.g_scheduler = None
                self.d_scheduler = None
                self._logger.info("已禁用原有的 CyclicLR 调度器。")
            else:
                # 在没有启用lr_balancer的情况下，使用固定学习率
                self._logger.info("未启用lr_balancer，将使用固定学习率。")
                self.lr_balancer = None
                self.g_scheduler = None
                self.d_scheduler = None

            # 记录最终使用的学习率调整机制
            if self.lr_balancer:
                 self._logger.info("学习率调整机制: EnhancedGanLrBalancer")
            else:
                 self._logger.info("学习率调整机制: 无 (使用固定学习率)")


            # 3. 训练循环 (序号调整为 3.2)
            # 在训练开始前重置全局步数
            # 注意：设置为0，因为在第一个批次处理前会递增为1
            self._global_step = 0
            self._logger.info("全局步数计数器已重置为0，将在第一个批次处理前递增为1")
            epoch = 0  # 初始化epoch变量
            # 添加断言确保 num_epochs 是整数
            assert isinstance(num_epochs, int), f"num_epochs 必须是整数，但得到的是 {type(num_epochs)}"
            for epoch in range(num_epochs):
                # 记录轮次开始时间
                epoch_start = time.time()

                # 记录当前的全局步数
                self._logger.info(f"轮次 {epoch} 开始 - 当前全局步数: {self._global_step}")

                # 记录当前学习率
                current_g_lr = self.optimizer_manager.get_learning_rate(self.g_optimizer)
                current_d_lr = self.optimizer_manager.get_learning_rate(self.d_optimizer)
                self._logger.info(f"轮次 {epoch} 开始时学习率: G_LR={current_g_lr:.6f}, D_LR={current_d_lr:.6f}")

                # 3.1 训练轮次
                # 如果启用了动态批次大小，可能需要在每个轮次重新创建数据加载器
                if dynamic_batch_size and self.batch_size_optimizer and train_dataset is not None:
                    # 获取当前优化后的批次大小
                    current_batch_size = self.batch_size_optimizer.get_batch_size()

                    # 记录批次大小变化
                    if current_batch_size != self.train_loader.batch_size:
                        self._logger.info(f"批次大小已调整: {self.train_loader.batch_size} -> {current_batch_size}")
                        # 重新创建数据加载器
                        self.train_loader = DataLoader(
                            train_dataset, batch_size=current_batch_size, shuffle=True, num_workers=num_workers
                        )

                # 执行训练轮次 - 传递当前epoch参数
                train_metrics = self._train_epoch(self.train_loader, current_epoch=epoch)

                # 3.2 验证轮次
                val_metrics = self._validate_epoch(self.val_loader)

                # 3.3 更新状态和检查点
                self.state_manager.update_training_state(
                    epoch=epoch,
                    train_metrics=train_metrics,
                    val_metrics=val_metrics
                )

                # 3.4 检查早停 - 提前检查，如果需要停止则不记录当前轮次的指标
                if self.state_manager.should_stop_training():
                    self._logger.info(
                        f"触发早停:\n"
                        f"- 当前轮次: {epoch}\n"
                        f"- 最佳损失: {self.state_manager.training_state.best_loss!s}\n"
                        f"- 无改善轮数: {self.state_manager.training_state.patience_counter!s}"
                    )
                    break

                # 3.5 保存检查点 - 修正epoch编号(从1开始)
                if self.state_manager.should_save_checkpoint(epoch):
                    # 使用正确的方法名 save_model 而不是 save_checkpoint
                    self.model_saver.save_model(
                        model=self.model,
                        optimizer=None,  # 不保存优化器状态
                        epoch=epoch + 1,  # 修正为1-based
                        metrics=val_metrics,
                        is_best=self.state_manager.is_best_model(val_metrics)
                    )
                    # 记录最后保存的检查点信息，用于OOM恢复
                    self.state_manager.training_state.last_checkpoint = self.model_saver.save_dir / f"{self.model_saver.model_name}_epoch_{epoch + 1}.pt"
                    self.state_manager.training_state.last_save_time = datetime.now()
                    self._logger.info(f"检查点已保存: {self.state_manager.training_state.last_checkpoint}")

                # 3.6 记录学习率状态并调整学习率
                # 仅当启用学习率平衡器时调整学习率
                if self.lr_balancer is not None:
                    # 使用增强版学习率平衡器进行调整 (基于训练损失和性能指标)
                    avg_g_loss = train_metrics.get('g_loss', float('nan'))
                    avg_d_loss = train_metrics.get('d_loss', float('nan'))
                    if not torch.isnan(torch.tensor(avg_g_loss)) and not torch.isnan(torch.tensor(avg_d_loss)):
                        # 获取梯度范数（如果可用）
                        gradient_norms = None
                        if hasattr(self, '_last_gradient_norms') and self._last_gradient_norms:
                            gradient_norms = self._last_gradient_norms

                        # 调用增强版平衡器
                        self.lr_balancer.step(
                            g_loss=avg_g_loss,
                            d_loss=avg_d_loss,
                            performance_metrics=val_metrics,  # 传递验证指标
                            gradient_norms=gradient_norms
                        )
                    else:
                         self._logger.warning(f"Epoch {epoch}: 训练损失包含 NaN (g={avg_g_loss}, d={avg_d_loss})，跳过学习率平衡器调整。")

                # 获取更新后的学习率
                g_lr_after = self.optimizer_manager.get_learning_rate(self.g_optimizer)
                d_lr_after = self.optimizer_manager.get_learning_rate(self.d_optimizer)

                # 构造学习率日志后缀
                log_suffix = ""
                if self.lr_balancer is not None:
                    log_suffix = " (由 EnhancedGanLrBalancer 调整)"
                else:
                    log_suffix = " (使用固定学习率)"

                self._logger.info(f"轮次 {epoch} 结束时学习率: G_LR={g_lr_after:.6f}, D_LR={d_lr_after:.6f}{log_suffix}")


                # 3.7 更新训练历史 - 强制包含所有必需指标
                # 处理训练指标并确保前缀
                for k, v in train_metrics.items():
                    name = f'train_{k}' if not k.startswith('train_') else k
                    epoch_metrics[name].append(v)

                # 处理验证指标并确保前缀
                for k, v in val_metrics.items():
                    name = f'val_{k}' if not k.startswith('val_') else k
                    epoch_metrics[name].append(v)

                # 强制包含基本指标
                if 'train_g_loss' not in epoch_metrics:
                    epoch_metrics['train_g_loss'].append(train_metrics.get('g_loss', float('nan')))
                if 'train_d_loss' not in epoch_metrics:
                    epoch_metrics['train_d_loss'].append(train_metrics.get('d_loss', float('nan')))
                if 'val_loss' not in epoch_metrics:
                    epoch_metrics['val_loss'].append(val_metrics.get('loss', float('nan')))

                # 记录轮次结束信息
                epoch_duration = time.time() - epoch_start
                self._logger.info(f"轮次 {epoch} 完成, 耗时: {epoch_duration:.2f}s")
                # Log memory usage if needed: system_monitor.log_memory_usage()

                # 3.8 调整训练平衡，传递验证MAE用于历史趋势分析
                current_mae = val_metrics.get('val_mae', None)
                self._adjust_training_balance(train_metrics, current_mae)

                # 3.9 调整批次大小（如果启用）
                if dynamic_batch_size and self.batch_size_optimizer:
                    try:
                        # 使用训练指标调整批次大小
                        g_loss = train_metrics.get('g_loss', None)
                        d_loss = train_metrics.get('d_loss', None)

                        # 如果有验证指标，也考虑验证指标
                        val_loss = None
                        if 'val_metrics' in locals() and val_metrics is not None:
                            val_loss = val_metrics.get('loss', None)

                        # 计算平均损失作为性能指标
                        performance_metric = None
                        if g_loss is not None and d_loss is not None:
                            performance_metric = (g_loss + d_loss) / 2
                        elif g_loss is not None:
                            performance_metric = g_loss
                        elif d_loss is not None:
                            performance_metric = d_loss
                        elif val_loss is not None:
                            performance_metric = val_loss

                        # 调整批次大小
                        if performance_metric is not None:
                            # 记录调整前的批次大小
                            old_batch_size = self.batch_size_optimizer.get_batch_size()

                            # 执行调整
                            self.batch_size_optimizer.step(loss=performance_metric, force_adjust=True)

                            # 获取调整后的批次大小
                            new_batch_size = self.batch_size_optimizer.get_batch_size()

                            # 记录调整结果
                            if new_batch_size != old_batch_size:
                                self._logger.info(f"轮次 {epoch} 结束后批次大小已调整: {old_batch_size} -> {new_batch_size}")
                            else:
                                self._logger.info(f"轮次 {epoch} 结束后批次大小保持不变: {new_batch_size}")

                            # 记录性能指标
                            self._logger.info(f"性能指标: {performance_metric:.4f} (来源: {'g_loss+d_loss' if g_loss is not None and d_loss is not None else 'g_loss' if g_loss is not None else 'd_loss' if d_loss is not None else 'val_loss'})")
                    except Exception as e:
                        self._logger.warning(f"调整批次大小失败: {e!s}")
                        # 直接抛出异常，不掩盖问题
                        raise

            # 4. 训练完成
            # 记录最终的lambda_gp值（不保存历史图表）
            if hasattr(self, 'adaptive_lambda_gp_manager') and self.adaptive_lambda_gp_manager:
                try:
                    # 记录最终的lambda_gp值
                    final_lambda_gp = self.adaptive_lambda_gp_manager.get_lambda_gp()
                    self._logger.info(f"训练结束，最终lambda_gp值: {final_lambda_gp:.4f}")
                except Exception as e:
                    self._logger.error(f"获取最终lambda_gp值失败: {e!s}")
                    # 直接抛出异常，不掩盖问题
                    raise

            # 修正：使用 str() 避免 MagicMock 格式化错误
            best_loss_val = self.state_manager.training_state.best_loss # 获取值
            completed_epochs = epoch + 1 if epoch is not None else num_epochs  # 安全获取完成的轮次
            self._logger.info(
                f"训练完成: 共{completed_epochs}轮 (计划 {num_epochs} 轮), "
                f"最佳验证损失={best_loss_val!s}"
            )

            # 返回训练历史记录
            # 不再提供默认值，确保所有必要指标都已存在
            return dict(epoch_metrics) # Convert back to regular dict

        except Exception as e:
            self.state_manager.transition_to(ModelState.ERROR)
            error_msg = f"GAN训练失败: {e!s}"
            self._logger.error(error_msg, exc_info=True) # Add traceback
            # 抛出异常而不是返回历史记录
            raise RuntimeError(error_msg) from e
        finally:
            # 清理资源 (e.g., close files, release GPU memory if needed)
            self._logger.info("训练器清理完成")
            pass

    def _adjust_training_balance(self, train_metrics: dict[str, float], current_mae: float | None = None):
        """动态调整生成器和判别器的训练平衡

        Args:
            train_metrics: 训练指标字典
            current_mae: 当前验证MAE值（可选，用于历史趋势分析）
        """
        g_loss = train_metrics.get('g_loss', float('nan'))
        d_loss = train_metrics.get('d_loss', float('nan'))

        # 记录当前训练参数和配置约束
        try:
            balance_config = getattr(self.config_manager.training, 'balance', None)
            if balance_config is None:
                error_msg = "配置缺失必要参数: training.balance"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            # 检查 balance_config 是否包含必要字段
            required_fields = ['lower_threshold', 'upper_threshold', 'min_n_critic', 'max_n_critic', 'min_g_steps', 'max_g_steps']
            missing_fields = []

            for field in required_fields:
                if not hasattr(balance_config, field):
                    missing_fields.append(field)

            if missing_fields:
                error_msg = f"配置缺失必要参数: training.balance.{', training.balance.'.join(missing_fields)}"
                self._logger.error(error_msg)
                raise ValueError(error_msg)

            # 使用配置中的值
            lower_threshold = balance_config.lower_threshold  # 判别器过弱阈值
            upper_threshold = balance_config.upper_threshold  # 判别器过强阈值
            min_n_critic = balance_config.min_n_critic       # 最少判别器训练次数
            max_n_critic = balance_config.max_n_critic      # 最多判别器训练次数
            min_g_steps = balance_config.min_g_steps        # 最少生成器训练次数
            max_g_steps = balance_config.max_g_steps       # 最多生成器训练次数
        except Exception as e:
            error_msg = f"获取训练平衡参数失败: {e!s}"
            self._logger.error(error_msg)
            raise ValueError(error_msg) from e

        original_n_critic = self.current_n_critic
        original_g_steps = getattr(self, 'current_g_steps', 1)  # 默认为1
        self.current_g_steps = original_g_steps  # 确保属性存在

        # 根据比例调整训练次数
        if torch.isnan(torch.tensor(g_loss)) or torch.isnan(torch.tensor(d_loss)) or g_loss == 0:
            self._logger.warning(f"无法调整训练平衡，损失值无效: g_loss={g_loss}, d_loss={d_loss}")
            return

        # 计算损失比率来评估相对强度
        # 注意：在WGAN-GP中，生成器损失为负值是完全正常的现象
        # 使用绝对值计算比率，关注损失的相对大小而不是符号
        abs_g_loss = abs(g_loss)
        abs_d_loss = abs(d_loss)

        # 防止除零错误
        if abs_g_loss < 1e-8:
            abs_g_loss = 1e-8

        # 计算比率 (|D_loss| / |G_loss|)
        ratio = abs_d_loss / abs_g_loss

        # 更新历史趋势数据，包括MAE
        self._update_training_history(ratio, current_mae)

        # 分析训练趋势
        trend_analysis = self._analyze_training_trend()

        # 根据趋势分析调整调整策略
        stability_modifier = 1.0  # 稳定性修正因子
        if trend_analysis['stability'] == 'unstable':
            stability_modifier = 0.5  # 不稳定时减小调整幅度
            self._logger.debug("检测到训练不稳定，减小调整幅度")
        elif trend_analysis['stability'] == 'very_stable':
            stability_modifier = 1.2  # 很稳定时可以稍微增大调整幅度
            self._logger.debug("检测到训练很稳定，可以稍微增大调整幅度")

        # 使用之前已获取的配置值（移除重复的配置获取代码）

        # 根据比例调整训练次数
        # 注意：删除了对负损失的错误特殊处理，因为在WGAN-GP中负损失是正常现象
        if ratio < lower_threshold:  # 判别器太弱
            # 判别器太弱 (D_loss/G_loss < lower_threshold) 或 生成器太强
            # 根据不平衡程度计算调整步长，考虑稳定性修正
            adjustment_step = self._calculate_adjustment_step(ratio, lower_threshold, stability_modifier)

            # 如果步长为0，表示基本平衡，无需调整
            if adjustment_step == 0:
                adjustment = "基本平衡，无需调整"
            else:
                # 计算新值，但暂不设置 (添加性能优化限制)
                # 性能优化: 限制判别器最大训练步数为3，避免过度计算
                performance_max_n_critic = min(max_n_critic, 3)  # 性能优化限制
                new_n_critic = min(performance_max_n_critic, self.current_n_critic + adjustment_step)  # 增加判别器训练
                new_g_steps = max(min_g_steps, self.current_g_steps - adjustment_step)    # 减少生成器训练

                # 确定实际调整方向
                if new_n_critic > self.current_n_critic:
                    critic_direction = "增加"
                elif new_n_critic < self.current_n_critic:
                    critic_direction = "减少"  # 这种情况不太可能发生
                else:
                    critic_direction = "保持"

                if new_g_steps < self.current_g_steps:
                    g_steps_direction = "减少"
                elif new_g_steps > self.current_g_steps:
                    g_steps_direction = "增加"  # 这种情况不太可能发生
                else:
                    g_steps_direction = "保持"

                # 设置新值
                self.current_n_critic = new_n_critic
                self.current_g_steps = new_g_steps

                # 根据实际调整生成描述
                adjustment = f"{critic_direction}判别器训练，{g_steps_direction}生成器训练 (判别器弱/生成器强，调整步长={adjustment_step}，受max_n_critic和min_g_steps约束)"
        elif ratio > upper_threshold:  # 判别器太强 (D_loss/G_loss > upper_threshold) 或 生成器太弱
            # 根据不平衡程度计算调整步长，考虑稳定性修正
            adjustment_step = self._calculate_adjustment_step(ratio, upper_threshold, stability_modifier)

            # 如果步长为0，表示基本平衡，无需调整
            if adjustment_step == 0:
                adjustment = "基本平衡，无需调整"
            else:
                # 计算新值，但暂不设置
                new_n_critic = max(min_n_critic, self.current_n_critic - adjustment_step)  # 减少判别器训练
                new_g_steps = min(max_g_steps, self.current_g_steps + adjustment_step)    # 增加生成器训练

                # 确定实际调整方向
                if new_n_critic < self.current_n_critic:
                    critic_direction = "减少"
                elif new_n_critic > self.current_n_critic:
                    critic_direction = "增加"  # 这种情况可能发生在min_n_critic > current_n_critic时
                else:
                    critic_direction = "保持"

                if new_g_steps > self.current_g_steps:
                    g_steps_direction = "增加"
                elif new_g_steps < self.current_g_steps:
                    g_steps_direction = "减少"  # 这种情况可能发生在max_g_steps < current_g_steps时
                else:
                    g_steps_direction = "保持"

                # 设置新值
                self.current_n_critic = new_n_critic
                self.current_g_steps = new_g_steps

                # 根据实际调整生成描述
                adjustment = f"{critic_direction}判别器训练，{g_steps_direction}生成器训练 (判别器强/生成器弱，调整步长={adjustment_step}，受min_n_critic和max_g_steps约束)"
        else: # lower_threshold <= ratio <= upper_threshold
            adjustment = "保持平衡"

        # 无论是否调整，都输出详细的训练比例日志 (修复：改为INFO级别)
        if original_n_critic != self.current_n_critic or original_g_steps != self.current_g_steps:
            self._logger.info(
                f"🔄 动态调整训练平衡: g_loss={g_loss:.4f}, d_loss={d_loss:.4f}, ratio={ratio:.4f}\n"
                f"   判别器训练次数: {original_n_critic} -> {self.current_n_critic}\n"
                f"   生成器训练次数: {original_g_steps} -> {self.current_g_steps}\n"
                f"   调整原因: {adjustment}\n"
                f"   配置约束: min_n_critic={min_n_critic}, max_n_critic={max_n_critic}, min_g_steps={min_g_steps}, max_g_steps={max_g_steps}"
            )
        else:
            # 修复：改为INFO级别，确保始终输出训练比例状态
            self._logger.info(
                f"⚖️  保持训练平衡: g_loss={g_loss:.4f}, d_loss={d_loss:.4f}, ratio={ratio:.4f}\n"
                f"   判别器训练次数: {self.current_n_critic} (无变化)\n"
                f"   生成器训练次数: {self.current_g_steps} (无变化)\n"
                f"   平衡状态: {adjustment}\n"
                f"   配置约束: min_n_critic={min_n_critic}, max_n_critic={max_n_critic}, min_g_steps={min_g_steps}, max_g_steps={max_g_steps}"
            )

    def _calculate_adjustment_step(self, ratio: torch.Tensor | float, threshold: torch.Tensor | float | None = None, stability_modifier: float = 1.0) -> int:
        """根据损失比率和不平衡程度计算精准调整步长

        Args:
            ratio: 当前损失比率 (|D_loss|/|G_loss|)
            threshold: 阈值参考值
            stability_modifier: 稳定性修正因子 (0.5-1.2)

        Returns:
            int: 精准调整步长 (0-3)
                0: 基本平衡，无需调整
                1: 轻微不平衡，小步调整
                2: 严重不平衡，中步调整
                3: 极度不平衡，大步调整
        """
        if threshold is None:
            # 如果没有提供阈值，使用默认步长
            return 1

        # 计算偏离程度
        if ratio == float('inf'):
            # 无穷大比率，使用最大调整步长
            return 2

        # 根据偏离程度决定调整步长
        ratio_value = ratio.item() if isinstance(ratio, torch.Tensor) else float(ratio)
        threshold_value = threshold.item() if isinstance(threshold, torch.Tensor) else float(threshold)

        # 计算相对偏离程度
        if ratio_value < threshold_value:
            # 判别器过弱的情况
            deviation = threshold_value / max(ratio_value, 1e-8)  # 避免除零
        else:
            # 判别器过强的情况
            deviation = ratio_value / max(threshold_value, 1e-8)  # 避免除零

        # 修复后的敏感调整阈值：降低阈值以提高响应敏感度
        # 应用稳定性修正因子到偏离程度阈值
        threshold_3 = 3.0 / stability_modifier   # 极度不平衡阈值 (从5.0降低到3.0)
        threshold_15 = 1.5 / stability_modifier  # 严重不平衡阈值 (从2.5降低到1.5)
        threshold_12 = 1.2 / stability_modifier  # 轻微不平衡阈值 (从1.5降低到1.2，且改为调整而非跳过)

        if deviation >= threshold_3:
            # 极度不平衡，大步调整
            step = 2
            self._logger.info(f"极度不平衡检测: 偏离程度={deviation:.2f}, 调整阈值={threshold_3:.2f}, 稳定性修正={stability_modifier:.2f}, 使用调整步长={step}")
        elif deviation >= threshold_15:
            # 严重不平衡，中步调整
            step = 1
            self._logger.info(f"严重不平衡检测: 偏离程度={deviation:.2f}, 调整阈值={threshold_15:.2f}, 稳定性修正={stability_modifier:.2f}, 使用调整步长={step}")
        elif deviation >= threshold_12:
            # 轻微不平衡，小步调整 (修复：改为调整而非跳过)
            step = 1
            self._logger.info(f"轻微不平衡检测: 偏离程度={deviation:.2f}, 调整阈值={threshold_12:.2f}, 稳定性修正={stability_modifier:.2f}, 使用调整步长={step}")
        else:
            # 基本平衡，不调整
            step = 0
            self._logger.info(f"基本平衡状态: 偏离程度={deviation:.2f}, 调整阈值={threshold_12:.2f}, 稳定性修正={stability_modifier:.2f}, 无需调整")

        return step

    def _update_training_history(self, loss_ratio: float, mae: float | None = None):
        """更新训练历史趋势数据

        Args:
            loss_ratio: 当前损失比率
            mae: 当前MAE值（可选）
        """
        # 更新损失比率历史
        self.loss_ratio_history.append(loss_ratio)
        if len(self.loss_ratio_history) > self.history_window:
            self.loss_ratio_history.pop(0)

        # 更新MAE历史
        if mae is not None:
            self.mae_history.append(mae)
            if len(self.mae_history) > self.history_window:
                self.mae_history.pop(0)

        self._logger.debug(f"历史趋势更新: 损失比率={loss_ratio:.4f}, MAE={mae}, 历史窗口={len(self.loss_ratio_history)}")

    def _analyze_training_trend(self) -> dict[str, str]:
        """分析训练趋势

        Returns:
            dict: 包含趋势分析结果的字典
        """
        trend_analysis = {
            'loss_ratio_trend': 'stable',
            'mae_trend': 'stable',
            'stability': 'stable'
        }

        # 分析损失比率趋势
        if len(self.loss_ratio_history) >= 2:
            recent_ratios = self.loss_ratio_history[-2:]
            if recent_ratios[-1] > recent_ratios[-2] * 1.2:
                trend_analysis['loss_ratio_trend'] = 'worsening'
            elif recent_ratios[-1] < recent_ratios[-2] * 0.8:
                trend_analysis['loss_ratio_trend'] = 'improving'

        # 分析MAE趋势
        if len(self.mae_history) >= 2:
            recent_maes = self.mae_history[-2:]
            if recent_maes[-1] > recent_maes[-2] * 1.1:
                trend_analysis['mae_trend'] = 'worsening'
            elif recent_maes[-1] < recent_maes[-2] * 0.9:
                trend_analysis['mae_trend'] = 'improving'

        # 分析整体稳定性
        if len(self.loss_ratio_history) >= 3:
            ratios = self.loss_ratio_history[-3:]
            ratio_std = torch.std(torch.tensor(ratios)).item()
            if ratio_std > 0.5:  # 标准差过大表示不稳定
                trend_analysis['stability'] = 'unstable'
            elif ratio_std < 0.1:  # 标准差很小表示很稳定
                trend_analysis['stability'] = 'very_stable'

        return trend_analysis
