"""搜索空间定义模块 - 定义GAN各组件的架构搜索空间

模块路径: src/optimization/nas/search_space.py

功能说明：
1. 生成器搜索空间：层数、隐藏维度、注意力头数、激活函数
2. 判别器搜索空间：分支结构、注意力机制、层数配置
3. 特征编码器搜索空间：卷积核大小、时序窗口、放大因子
4. 搜索空间管理：约束检查、采样策略、编码解码
"""

from __future__ import annotations

import random
from dataclasses import dataclass, field
from typing import Any, Dict, List, Tuple, Union
from enum import Enum

import torch
import torch.nn as nn

from src.utils.logger import get_logger


class ActivationType(Enum):
    """激活函数类型枚举"""
    RELU = "relu"
    LEAKY_RELU = "leaky_relu"
    GELU = "gelu"
    SWISH = "swish"
    TANH = "tanh"


class AttentionType(Enum):
    """注意力机制类型枚举"""
    MULTI_HEAD = "multi_head"
    MULTI_SCALE = "multi_scale"
    ADAPTIVE_DILATION = "adaptive_dilation"
    TEMPORAL_WRAPPER = "temporal_wrapper"
    CAUSAL = "causal"


class OptimizerType(Enum):
    """优化器类型枚举"""
    ADAM = "adam"
    ADAMW = "adamw"
    RMSPROP = "rmsprop"
    SGD = "sgd"


class LRSchedulerType(Enum):
    """学习率调度器类型枚举"""
    PLATEAU = "plateau"
    COSINE = "cosine"
    EXPONENTIAL = "exponential"
    STEP = "step"
    LINEAR = "linear"


class LossType(Enum):
    """损失函数类型枚举"""
    WGAN_GP = "wgan_gp"
    LSGAN = "lsgan"
    VANILLA = "vanilla"
    HINGE = "hinge"


class NormalizationType(Enum):
    """归一化类型枚举"""
    BATCH_NORM = "batch_norm"
    LAYER_NORM = "layer_norm"
    INSTANCE_NORM = "instance_norm"
    NONE = "none"


@dataclass
class GeneratorArchConfig:
    """生成器架构配置"""
    num_layers: int = 3  # 层数 (2-5)
    hidden_dim: int = 128  # 隐藏维度 (64-256)
    num_attention_heads: int = 4  # 注意力头数 (2-8)
    activation_type: ActivationType = ActivationType.LEAKY_RELU
    dropout_rate: float = 0.1  # dropout率 (0.0-0.3)
    use_layer_norm: bool = True  # 是否使用层归一化
    use_residual: bool = True  # 是否使用残差连接
    noise_injection_layers: List[int] = field(default_factory=list)  # 噪声注入层


@dataclass
class DiscriminatorArchConfig:
    """判别器架构配置"""
    num_layers: int = 4  # 层数 (3-6)
    hidden_dim: int = 192  # 隐藏维度 (96-384)
    branch_config: Dict[str, bool] = field(default_factory=lambda: {
        'trend_branch': True,
        'feature_branch': True,
        'temporal_branch': True
    })
    attention_types: List[AttentionType] = field(default_factory=lambda: [
        AttentionType.TEMPORAL_WRAPPER
    ])
    num_attention_heads: int = 4  # 注意力头数 (2-8)
    dropout_rate: float = 0.1  # dropout率 (0.0-0.3)
    use_spectral_norm: bool = True  # 是否使用谱归一化
    weight_sharing: bool = False  # 分支间是否共享权重


@dataclass
class FeatureEncoderArchConfig:
    """特征编码器架构配置"""
    conv_kernel_sizes: List[int] = field(default_factory=lambda: [3, 5, 7])  # 卷积核大小
    num_scales: int = 3  # 多尺度数量 (2-5)
    temporal_window_sizes: List[int] = field(default_factory=lambda: [12, 24, 48])  # 时序窗口大小
    amplification_factor: float = 1.5  # 先行信号放大因子 (1.0-3.0)
    use_causal_attention: bool = True  # 是否使用因果注意力
    encoder_depth: int = 2  # 编码器深度 (1-4)
    feature_fusion_type: str = "dynamic"  # 特征融合类型


@dataclass
class TrainingStrategyConfig:
    """训练策略配置"""
    # 优化器参数
    optimizer_type: OptimizerType = OptimizerType.ADAM  # 优化器类型
    generator_lr: float = 0.001  # 生成器学习率 (0.0001-0.01)
    discriminator_lr: float = 0.001  # 判别器学习率 (0.0001-0.01)
    lr_ratio: float = 1.0  # G/D学习率比例 (0.5-2.0)

    # 学习率调度
    lr_scheduler_type: LRSchedulerType = LRSchedulerType.PLATEAU  # 学习率调度器类型
    lr_decay_factor: float = 0.9  # 衰减因子 (0.1-0.95)
    lr_patience: int = 3  # 耐心值 (2-10)

    # 学习率平衡器配置 (新增)
    lr_balancer_enabled: bool = True  # 是否启用学习率平衡器
    lr_balancer_target_ratio: float = 1.2  # 目标损失比率 (0.8-2.0)
    lr_balancer_sensitivity: float = 0.15  # 调整敏感度 (0.05-0.3)
    lr_balancer_adjustment_mode: str = "adaptive"  # 调整模式 ["sync", "inverse", "adaptive"]
    lr_balancer_mode_switch_threshold: float = 0.3  # 模式切换阈值 (0.1-0.5)
    lr_balancer_performance_weight: float = 0.4  # 性能权重 (0.1-0.6)
    lr_balancer_stability_weight: float = 0.2  # 稳定性权重 (0.1-0.4)
    lr_balancer_loss_ratio_weight: float = 0.4  # 损失比率权重 (0.2-0.6)

    # 训练平衡
    n_critic: int = 1  # 判别器训练次数 (1-5)
    g_steps: int = 1  # 生成器训练次数 (1-3)
    training_balance_strategy: str = "fixed"  # ["fixed", "adaptive", "dynamic"]

    # 优化器特定参数
    beta1: float = 0.5  # Adam beta1 (0.0-0.9)
    beta2: float = 0.999  # Adam beta2 (0.9-0.999)
    weight_decay: float = 0.0  # 权重衰减 (0.0-0.01)
    momentum: float = 0.9  # SGD动量 (0.0-0.99)


@dataclass
class LossConfig:
    """损失函数配置"""
    # 损失权重
    adversarial_weight: float = 1.0  # 对抗损失权重 (0.5-2.0)
    feature_matching_weight: float = 1.0  # 特征匹配权重 (0.1-5.0)
    temporal_consistency_weight: float = 0.1  # 时序一致性权重 (0.01-1.0)
    regression_weight: float = 10.0  # 回归损失权重 (1.0-50.0)

    # 梯度惩罚
    lambda_gp: float = 0.1  # 梯度惩罚权重 (0.01-1.0)
    adaptive_lambda_gp: bool = True  # 是否自适应调整
    gp_target_norm: float = 1.0  # 目标梯度范数 (0.5-2.0)

    # 损失函数类型
    discriminator_loss_type: LossType = LossType.WGAN_GP  # 判别器损失类型
    generator_loss_type: LossType = LossType.WGAN_GP  # 生成器损失类型

    # 损失平滑和稳定性
    label_smoothing: float = 0.0  # 标签平滑 (0.0-0.3)
    loss_clipping: float = 0.0  # 损失裁剪 (0.0表示不裁剪, 0.1-10.0)


@dataclass
class RegularizationConfig:
    """正则化配置"""
    # Dropout配置
    generator_dropout: float = 0.1  # 生成器dropout (0.0-0.5)
    discriminator_dropout: float = 0.1  # 判别器dropout (0.0-0.5)
    feature_encoder_dropout: float = 0.1  # 特征编码器dropout (0.0-0.5)

    # 权重正则化
    weight_decay: float = 0.0  # 权重衰减 (0.0-0.01)
    spectral_norm: bool = True  # 谱归一化
    gradient_clip_val: float = 0.5  # 梯度裁剪 (0.1-5.0)
    gradient_clip_type: str = "norm"  # 梯度裁剪类型 ["norm", "value"]

    # 噪声注入
    noise_std: float = 0.1  # 噪声标准差 (0.0-0.5)
    input_noise_std: float = 0.0  # 输入噪声标准差 (0.0-0.1)

    # 归一化类型
    normalization_type: NormalizationType = NormalizationType.LAYER_NORM  # 归一化类型

    # 正则化策略
    dropout_schedule: str = "fixed"  # dropout调度 ["fixed", "decay", "adaptive"]
    l1_regularization: float = 0.0  # L1正则化 (0.0-0.01)
    l2_regularization: float = 0.0  # L2正则化 (0.0-0.01)


class GeneratorSearchSpace:
    """生成器搜索空间"""

    def __init__(self):
        self.logger = get_logger("GeneratorSearchSpace")

        # 搜索范围必须通过SearchSpaceManager从配置文件设置
        # 不提供默认值，确保单一配置来源
        self.num_layers_range: Tuple[int, int] | None = None
        self.hidden_dim_range: Tuple[int, int] | None = None
        self.num_heads_range: Tuple[int, int] | None = None
        self.dropout_range: Tuple[float, float] | None = None
        self.activation_types: List[ActivationType] | None = None

    def _validate_configuration(self):
        """验证配置是否已正确设置"""
        if self.num_layers_range is None:
            raise ValueError("GeneratorSearchSpace未配置num_layers_range，必须通过SearchSpaceManager设置")
        if self.hidden_dim_range is None:
            raise ValueError("GeneratorSearchSpace未配置hidden_dim_range，必须通过SearchSpaceManager设置")
        if self.num_heads_range is None:
            raise ValueError("GeneratorSearchSpace未配置num_heads_range，必须通过SearchSpaceManager设置")
        if self.dropout_range is None:
            raise ValueError("GeneratorSearchSpace未配置dropout_range，必须通过SearchSpaceManager设置")
        if self.activation_types is None:
            raise ValueError("GeneratorSearchSpace未配置activation_types，必须通过SearchSpaceManager设置")

    def sample_random(self) -> GeneratorArchConfig:
        """随机采样一个生成器架构"""
        self._validate_configuration()

        # 首先采样隐藏维度
        assert self.hidden_dim_range is not None
        hidden_dim = self._sample_power_of_2(*self.hidden_dim_range)

        # 根据隐藏维度确定兼容的注意力头数
        compatible_heads = self._get_compatible_heads(hidden_dim)
        if not compatible_heads:
            # 如果没有兼容的头数，抛出错误而不是使用默认值
            raise ValueError(f"隐藏维度 {hidden_dim} 没有兼容的注意力头数，请检查配置文件中的参数范围")

        num_attention_heads = random.choice(compatible_heads)

        assert self.num_layers_range is not None
        assert self.activation_types is not None
        assert self.dropout_range is not None

        config = GeneratorArchConfig(
            num_layers=random.randint(*self.num_layers_range),
            hidden_dim=hidden_dim,
            num_attention_heads=num_attention_heads,
            activation_type=random.choice(self.activation_types),
            dropout_rate=random.uniform(*self.dropout_range),
            use_layer_norm=random.choice([True, False]),
            use_residual=random.choice([True, False]),
            noise_injection_layers=self._sample_noise_injection_layers()
        )

        self.logger.debug(f"采样生成器架构: {config}")
        return config

    def _get_compatible_heads(self, hidden_dim: int) -> List[int]:
        """获取与隐藏维度兼容的注意力头数列表"""
        assert self.num_heads_range is not None
        min_heads, max_heads = self.num_heads_range
        compatible_heads = []

        for heads in range(min_heads, max_heads + 1):
            if hidden_dim % heads == 0:
                compatible_heads.append(heads)

        self.logger.debug(f"隐藏维度 {hidden_dim} 的兼容注意力头数: {compatible_heads}")
        return compatible_heads

    def _sample_power_of_2(self, min_val: int, max_val: int) -> int:
        """采样2的幂次的值"""
        powers = []
        power = 1
        while power <= max_val:
            if power >= min_val:
                powers.append(power)
            power *= 2
        return random.choice(powers) if powers else min_val

    def _sample_noise_injection_layers(self) -> List[int]:
        """采样噪声注入层"""
        max_layers = 5
        num_injection_layers = random.randint(0, max_layers // 2)
        if num_injection_layers == 0:
            return []
        return random.sample(range(max_layers), num_injection_layers)

    def validate_config(self, config: GeneratorArchConfig) -> bool:
        """验证配置的有效性"""
        if self.num_layers_range is None or self.hidden_dim_range is None or \
           self.num_heads_range is None or self.dropout_range is None:
            return False

        if not (self.num_layers_range[0] <= config.num_layers <= self.num_layers_range[1]):
            return False
        if not (self.hidden_dim_range[0] <= config.hidden_dim <= self.hidden_dim_range[1]):
            return False
        if not (self.num_heads_range[0] <= config.num_attention_heads <= self.num_heads_range[1]):
            return False
        if not (self.dropout_range[0] <= config.dropout_rate <= self.dropout_range[1]):
            return False

        # 验证注意力头数约束：hidden_dim 必须能被 num_attention_heads 整除
        if config.hidden_dim % config.num_attention_heads != 0:
            self.logger.debug(f"生成器配置验证失败: hidden_dim ({config.hidden_dim}) 不能被 num_attention_heads ({config.num_attention_heads}) 整除")
            return False

        return True


class DiscriminatorSearchSpace:
    """判别器搜索空间"""

    def __init__(self):
        self.logger = get_logger("DiscriminatorSearchSpace")

        # 定义搜索范围
        self.num_layers_range = (3, 6)
        self.hidden_dim_range = (96, 384)
        self.num_heads_range = (2, 8)
        self.dropout_range = (0.0, 0.3)
        self.attention_types = list(AttentionType)

    def sample_random(self) -> DiscriminatorArchConfig:
        """随机采样一个判别器架构"""
        # 随机选择分支配置
        branch_config = {
            'trend_branch': random.choice([True, False]),
            'feature_branch': random.choice([True, False]),
            'temporal_branch': random.choice([True, False])
        }
        # 确保至少有一个分支启用
        if not any(branch_config.values()):
            branch_config['temporal_branch'] = True

        # 随机选择注意力机制
        num_attention_types = random.randint(1, 3)
        attention_types = random.sample(self.attention_types, num_attention_types)

        # 首先采样隐藏维度
        hidden_dim = self._sample_power_of_2(*self.hidden_dim_range)

        # 根据隐藏维度确定兼容的注意力头数
        compatible_heads = self._get_compatible_heads(hidden_dim)
        if not compatible_heads:
            # 如果没有兼容的头数，重新采样隐藏维度
            self.logger.warning(f"隐藏维度 {hidden_dim} 没有兼容的注意力头数，重新采样")
            hidden_dim = 192  # 使用默认值
            compatible_heads = self._get_compatible_heads(hidden_dim)

        num_attention_heads = random.choice(compatible_heads)

        config = DiscriminatorArchConfig(
            num_layers=random.randint(*self.num_layers_range),
            hidden_dim=hidden_dim,
            branch_config=branch_config,
            attention_types=attention_types,
            num_attention_heads=num_attention_heads,
            dropout_rate=random.uniform(*self.dropout_range),
            use_spectral_norm=random.choice([True, False]),
            weight_sharing=random.choice([True, False])
        )

        self.logger.debug(f"采样判别器架构: {config}")
        return config

    def _get_compatible_heads(self, hidden_dim: int) -> List[int]:
        """获取与隐藏维度兼容的注意力头数列表"""
        min_heads, max_heads = self.num_heads_range
        compatible_heads = []

        for heads in range(min_heads, max_heads + 1):
            if hidden_dim % heads == 0:
                compatible_heads.append(heads)

        self.logger.debug(f"隐藏维度 {hidden_dim} 的兼容注意力头数: {compatible_heads}")
        return compatible_heads

    def _sample_power_of_2(self, min_val: int, max_val: int) -> int:
        """采样2的幂次的值"""
        powers = []
        power = 1
        while power <= max_val:
            if power >= min_val:
                powers.append(power)
            power *= 2
        return random.choice(powers) if powers else min_val

    def validate_config(self, config: DiscriminatorArchConfig) -> bool:
        """验证配置的有效性"""
        if not (self.num_layers_range[0] <= config.num_layers <= self.num_layers_range[1]):
            return False
        if not (self.hidden_dim_range[0] <= config.hidden_dim <= self.hidden_dim_range[1]):
            return False
        if not (self.num_heads_range[0] <= config.num_attention_heads <= self.num_heads_range[1]):
            return False
        if not any(config.branch_config.values()):
            return False
        if not config.attention_types:
            return False

        # 验证注意力头数约束：hidden_dim 必须能被 num_attention_heads 整除
        if config.hidden_dim % config.num_attention_heads != 0:
            self.logger.debug(f"判别器配置验证失败: hidden_dim ({config.hidden_dim}) 不能被 num_attention_heads ({config.num_attention_heads}) 整除")
            return False

        return True


class TrainingStrategySearchSpace:
    """训练策略搜索空间"""

    def __init__(self):
        self.logger = get_logger("TrainingStrategySearchSpace")

        # 定义搜索范围
        self.lr_range = (0.0001, 0.01)
        self.lr_ratio_range = (0.5, 2.0)
        self.lr_decay_range = (0.1, 0.95)
        self.lr_patience_range = (2, 10)
        self.n_critic_range = (1, 5)
        self.g_steps_range = (1, 3)
        self.beta1_range = (0.0, 0.9)
        self.beta2_range = (0.9, 0.999)
        self.weight_decay_range = (0.0, 0.01)
        self.momentum_range = (0.0, 0.99)

        # 学习率平衡器搜索范围 (新增)
        self.lr_balancer_target_ratio_range = (0.8, 2.0)
        self.lr_balancer_sensitivity_range = (0.05, 0.3)
        self.lr_balancer_mode_switch_threshold_range = (0.1, 0.5)
        self.lr_balancer_performance_weight_range = (0.1, 0.6)
        self.lr_balancer_stability_weight_range = (0.1, 0.4)
        self.lr_balancer_loss_ratio_weight_range = (0.2, 0.6)

        self.optimizer_types = list(OptimizerType)
        self.lr_scheduler_types = list(LRSchedulerType)
        self.balance_strategies = ["fixed", "adaptive", "dynamic"]
        self.lr_balancer_adjustment_modes = ["sync", "inverse", "adaptive"]

    def sample_random(self) -> TrainingStrategyConfig:
        """随机采样一个训练策略配置"""
        # 确保权重总和为1.0
        performance_weight = random.uniform(*self.lr_balancer_performance_weight_range)
        stability_weight = random.uniform(*self.lr_balancer_stability_weight_range)
        # 计算损失比率权重，确保三者总和为1.0
        remaining_weight = 1.0 - performance_weight - stability_weight
        loss_ratio_weight = max(0.1, min(0.8, remaining_weight))  # 限制在合理范围内

        # 重新归一化权重
        total_weight = performance_weight + stability_weight + loss_ratio_weight
        performance_weight /= total_weight
        stability_weight /= total_weight
        loss_ratio_weight /= total_weight

        config = TrainingStrategyConfig(
            optimizer_type=random.choice(self.optimizer_types),
            generator_lr=random.uniform(*self.lr_range),
            discriminator_lr=random.uniform(*self.lr_range),
            lr_ratio=random.uniform(*self.lr_ratio_range),
            lr_scheduler_type=random.choice(self.lr_scheduler_types),
            lr_decay_factor=random.uniform(*self.lr_decay_range),
            lr_patience=random.randint(*self.lr_patience_range),
            # 学习率平衡器参数 (新增)
            lr_balancer_enabled=random.choice([True, False]),
            lr_balancer_target_ratio=random.uniform(*self.lr_balancer_target_ratio_range),
            lr_balancer_sensitivity=random.uniform(*self.lr_balancer_sensitivity_range),
            lr_balancer_adjustment_mode=random.choice(self.lr_balancer_adjustment_modes),
            lr_balancer_mode_switch_threshold=random.uniform(*self.lr_balancer_mode_switch_threshold_range),
            lr_balancer_performance_weight=performance_weight,
            lr_balancer_stability_weight=stability_weight,
            lr_balancer_loss_ratio_weight=loss_ratio_weight,
            n_critic=random.randint(*self.n_critic_range),
            g_steps=random.randint(*self.g_steps_range),
            training_balance_strategy=random.choice(self.balance_strategies),
            beta1=random.uniform(*self.beta1_range),
            beta2=random.uniform(*self.beta2_range),
            weight_decay=random.uniform(*self.weight_decay_range),
            momentum=random.uniform(*self.momentum_range)
        )

        self.logger.debug(f"采样训练策略配置: {config}")
        return config

    def validate_config(self, config: TrainingStrategyConfig) -> bool:
        """验证配置的有效性"""
        if not (self.lr_range[0] <= config.generator_lr <= self.lr_range[1]):
            return False
        if not (self.lr_range[0] <= config.discriminator_lr <= self.lr_range[1]):
            return False
        if not (self.lr_ratio_range[0] <= config.lr_ratio <= self.lr_ratio_range[1]):
            return False

        # 验证学习率平衡器参数 (新增)
        if hasattr(config, 'lr_balancer_target_ratio'):
            if not (self.lr_balancer_target_ratio_range[0] <= config.lr_balancer_target_ratio <= self.lr_balancer_target_ratio_range[1]):
                return False
        if hasattr(config, 'lr_balancer_sensitivity'):
            if not (self.lr_balancer_sensitivity_range[0] <= config.lr_balancer_sensitivity <= self.lr_balancer_sensitivity_range[1]):
                return False
        if hasattr(config, 'lr_balancer_adjustment_mode'):
            if config.lr_balancer_adjustment_mode not in self.lr_balancer_adjustment_modes:
                return False

        # 验证权重总和接近1.0
        if hasattr(config, 'lr_balancer_performance_weight') and hasattr(config, 'lr_balancer_stability_weight') and hasattr(config, 'lr_balancer_loss_ratio_weight'):
            total_weight = config.lr_balancer_performance_weight + config.lr_balancer_stability_weight + config.lr_balancer_loss_ratio_weight
            if abs(total_weight - 1.0) > 0.1:  # 允许10%的误差
                return False

        return True


class LossSearchSpace:
    """损失函数搜索空间"""

    def __init__(self):
        self.logger = get_logger("LossSearchSpace")

        # 定义搜索范围
        self.adversarial_weight_range = (0.5, 2.0)
        self.feature_matching_weight_range = (0.1, 5.0)
        self.temporal_consistency_weight_range = (0.01, 1.0)
        self.regression_weight_range = (1.0, 50.0)
        self.lambda_gp_range = (0.01, 1.0)
        self.gp_target_norm_range = (0.5, 2.0)
        self.label_smoothing_range = (0.0, 0.3)
        self.loss_clipping_range = (0.0, 10.0)

        self.loss_types = list(LossType)

    def sample_random(self) -> LossConfig:
        """随机采样一个损失函数配置"""
        config = LossConfig(
            adversarial_weight=random.uniform(*self.adversarial_weight_range),
            feature_matching_weight=random.uniform(*self.feature_matching_weight_range),
            temporal_consistency_weight=random.uniform(*self.temporal_consistency_weight_range),
            regression_weight=random.uniform(*self.regression_weight_range),
            lambda_gp=random.uniform(*self.lambda_gp_range),
            adaptive_lambda_gp=random.choice([True, False]),
            gp_target_norm=random.uniform(*self.gp_target_norm_range),
            discriminator_loss_type=random.choice(self.loss_types),
            generator_loss_type=random.choice(self.loss_types),
            label_smoothing=random.uniform(*self.label_smoothing_range),
            loss_clipping=random.uniform(*self.loss_clipping_range)
        )

        self.logger.debug(f"采样损失函数配置: {config}")
        return config

    def validate_config(self, config: LossConfig) -> bool:
        """验证配置的有效性"""
        if not (self.adversarial_weight_range[0] <= config.adversarial_weight <= self.adversarial_weight_range[1]):
            return False
        if not (self.lambda_gp_range[0] <= config.lambda_gp <= self.lambda_gp_range[1]):
            return False
        return True


class RegularizationSearchSpace:
    """正则化搜索空间"""

    def __init__(self):
        self.logger = get_logger("RegularizationSearchSpace")

        # 定义搜索范围
        self.dropout_range = (0.0, 0.5)
        self.weight_decay_range = (0.0, 0.01)
        self.gradient_clip_range = (0.1, 5.0)
        self.noise_std_range = (0.0, 0.5)
        self.input_noise_std_range = (0.0, 0.1)
        self.l1_reg_range = (0.0, 0.01)
        self.l2_reg_range = (0.0, 0.01)

        self.normalization_types = list(NormalizationType)
        self.gradient_clip_types = ["norm", "value"]
        self.dropout_schedules = ["fixed", "decay", "adaptive"]

    def sample_random(self) -> RegularizationConfig:
        """随机采样一个正则化配置"""
        config = RegularizationConfig(
            generator_dropout=random.uniform(*self.dropout_range),
            discriminator_dropout=random.uniform(*self.dropout_range),
            feature_encoder_dropout=random.uniform(*self.dropout_range),
            weight_decay=random.uniform(*self.weight_decay_range),
            spectral_norm=random.choice([True, False]),
            gradient_clip_val=random.uniform(*self.gradient_clip_range),
            gradient_clip_type=random.choice(self.gradient_clip_types),
            noise_std=random.uniform(*self.noise_std_range),
            input_noise_std=random.uniform(*self.input_noise_std_range),
            normalization_type=random.choice(self.normalization_types),
            dropout_schedule=random.choice(self.dropout_schedules),
            l1_regularization=random.uniform(*self.l1_reg_range),
            l2_regularization=random.uniform(*self.l2_reg_range)
        )

        self.logger.debug(f"采样正则化配置: {config}")
        return config

    def validate_config(self, config: RegularizationConfig) -> bool:
        """验证配置的有效性"""
        if not (self.dropout_range[0] <= config.generator_dropout <= self.dropout_range[1]):
            return False
        if not (self.weight_decay_range[0] <= config.weight_decay <= self.weight_decay_range[1]):
            return False
        if not (self.gradient_clip_range[0] <= config.gradient_clip_val <= self.gradient_clip_range[1]):
            return False
        return True


class FeatureEncoderSearchSpace:
    """特征编码器搜索空间"""

    def __init__(self):
        self.logger = get_logger("FeatureEncoderSearchSpace")

        # 定义搜索范围
        self.num_scales_range = (2, 5)
        self.amplification_range = (1.0, 3.0)
        self.encoder_depth_range = (1, 4)
        self.kernel_size_options = [3, 5, 7, 9, 11]
        self.window_size_options = [6, 12, 24, 48, 96]
        self.fusion_types = ["dynamic", "static", "attention"]

    def sample_random(self) -> FeatureEncoderArchConfig:
        """随机采样一个特征编码器架构"""
        num_scales = random.randint(*self.num_scales_range)

        config = FeatureEncoderArchConfig(
            conv_kernel_sizes=random.sample(self.kernel_size_options, num_scales),
            num_scales=num_scales,
            temporal_window_sizes=random.sample(self.window_size_options,
                                               min(num_scales, len(self.window_size_options))),
            amplification_factor=random.uniform(*self.amplification_range),
            use_causal_attention=random.choice([True, False]),
            encoder_depth=random.randint(*self.encoder_depth_range),
            feature_fusion_type=random.choice(self.fusion_types)
        )

        self.logger.debug(f"采样特征编码器架构: {config}")
        return config

    def validate_config(self, config: FeatureEncoderArchConfig) -> bool:
        """验证配置的有效性"""
        if not (self.num_scales_range[0] <= config.num_scales <= self.num_scales_range[1]):
            return False
        if not (self.amplification_range[0] <= config.amplification_factor <= self.amplification_range[1]):
            return False
        if not (self.encoder_depth_range[0] <= config.encoder_depth <= self.encoder_depth_range[1]):
            return False
        if len(config.conv_kernel_sizes) != config.num_scales:
            return False
        return True


@dataclass
class ArchitectureConfig:
    """完整的架构配置"""
    generator: GeneratorArchConfig
    discriminator: DiscriminatorArchConfig
    feature_encoder: FeatureEncoderArchConfig
    training_strategy: TrainingStrategyConfig
    loss_config: LossConfig
    regularization: RegularizationConfig

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        def convert_enum_to_str(obj):
            """递归转换枚举为字符串"""
            if isinstance(obj, dict):
                return {k: convert_enum_to_str(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_enum_to_str(item) for item in obj]
            elif hasattr(obj, 'value'):  # 枚举类型
                return obj.value
            else:
                return obj

        return {
            'generator': convert_enum_to_str(self.generator.__dict__),
            'discriminator': convert_enum_to_str(self.discriminator.__dict__),
            'feature_encoder': convert_enum_to_str(self.feature_encoder.__dict__),
            'training_strategy': convert_enum_to_str(self.training_strategy.__dict__),
            'loss_config': convert_enum_to_str(self.loss_config.__dict__),
            'regularization': convert_enum_to_str(self.regularization.__dict__)
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ArchitectureConfig':
        """从字典创建配置"""
        def convert_str_to_enum(obj):
            """递归转换字符串为枚举"""
            if isinstance(obj, dict):
                result = {}
                for k, v in obj.items():
                    if k == 'activation_type' and isinstance(v, str):
                        result[k] = ActivationType(v)
                    elif k == 'attention_types' and isinstance(v, list):
                        result[k] = [AttentionType(item) if isinstance(item, str) else item for item in v]
                    elif k == 'optimizer_type' and isinstance(v, str):
                        result[k] = OptimizerType(v)
                    elif k == 'lr_scheduler_type' and isinstance(v, str):
                        result[k] = LRSchedulerType(v)
                    elif k == 'discriminator_loss_type' and isinstance(v, str):
                        result[k] = LossType(v)
                    elif k == 'generator_loss_type' and isinstance(v, str):
                        result[k] = LossType(v)
                    elif k == 'normalization_type' and isinstance(v, str):
                        result[k] = NormalizationType(v)
                    else:
                        result[k] = convert_str_to_enum(v)
                return result
            elif isinstance(obj, list):
                return [convert_str_to_enum(item) for item in obj]
            else:
                return obj

        converted_data = convert_str_to_enum(data)
        # 类型断言确保converted_data是字典
        assert isinstance(converted_data, dict)
        return cls(
            generator=GeneratorArchConfig(**converted_data['generator']),
            discriminator=DiscriminatorArchConfig(**converted_data['discriminator']),
            feature_encoder=FeatureEncoderArchConfig(**converted_data['feature_encoder']),
            training_strategy=TrainingStrategyConfig(**converted_data['training_strategy']),
            loss_config=LossConfig(**converted_data['loss_config']),
            regularization=RegularizationConfig(**converted_data['regularization'])
        )


class SearchSpaceManager:
    """搜索空间管理器 - 使用配置文件参数"""

    def __init__(self, config, memory_limit_gb: float = 3.0):
        """
        Args:
            config: 搜索空间配置，必须从配置文件提供
            memory_limit_gb: GPU内存限制(GB)
        """
        if config is None:
            raise ValueError("SearchSpaceManager必须提供SearchSpaceConfig配置，不支持默认值")

        self.logger = get_logger("SearchSpaceManager")
        self.config = config
        self.memory_limit_gb = memory_limit_gb

        # 从配置创建各个搜索空间
        self.generator_space = self._create_generator_space_from_config()
        self.discriminator_space = self._create_discriminator_space_from_config()
        self.feature_encoder_space = self._create_feature_encoder_space_from_config()
        self.training_strategy_space = self._create_training_strategy_space_from_config()
        self.loss_space = self._create_loss_space_from_config()
        self.regularization_space = self._create_regularization_space_from_config()

        self.logger.info("搜索空间管理器初始化完成，使用配置文件参数")

    def _create_generator_space_from_config(self) -> GeneratorSearchSpace:
        """从配置创建生成器搜索空间"""
        space = GeneratorSearchSpace()
        # 使用配置覆盖默认值
        space.num_layers_range = self.config.generator_num_layers_range
        space.hidden_dim_range = self.config.generator_hidden_dim_range
        space.num_heads_range = self.config.generator_num_heads_range
        space.dropout_range = self.config.generator_dropout_range
        # 转换激活函数字符串为枚举
        try:
            space.activation_types = [ActivationType(act.upper()) for act in self.config.generator_activation_types]
        except ValueError as e:
            self.logger.error(f"无效的激活函数类型: {e}")
            raise ValueError(f"配置文件中的激活函数类型无效: {e}。请检查generator_activation_types配置。")
        return space

    def _create_discriminator_space_from_config(self) -> DiscriminatorSearchSpace:
        """从配置创建判别器搜索空间"""
        space = DiscriminatorSearchSpace()
        space.num_layers_range = self.config.discriminator_num_layers_range
        space.hidden_dim_range = self.config.discriminator_hidden_dim_range
        return space

    def _create_feature_encoder_space_from_config(self) -> FeatureEncoderSearchSpace:
        """从配置创建特征编码器搜索空间"""
        space = FeatureEncoderSearchSpace()
        space.num_scales_range = self.config.feature_encoder_num_scales_range
        space.kernel_size_options = self.config.feature_encoder_kernel_size_options
        space.window_size_options = self.config.feature_encoder_temporal_window_options
        space.amplification_range = self.config.feature_encoder_amplification_factor_range
        space.fusion_types = self.config.feature_encoder_fusion_types
        return space

    def _create_training_strategy_space_from_config(self) -> TrainingStrategySearchSpace:
        """从配置创建训练策略搜索空间"""
        space = TrainingStrategySearchSpace()
        space.lr_range = self.config.training_lr_range
        space.optimizer_types = [OptimizerType(opt.upper()) for opt in self.config.training_optimizer_types]
        space.lr_scheduler_types = [LRSchedulerType(sched.upper()) for sched in self.config.training_lr_scheduler_types]
        space.n_critic_range = self.config.training_n_critic_range
        space.g_steps_range = self.config.training_g_steps_range
        return space

    def _create_loss_space_from_config(self) -> LossSearchSpace:
        """从配置创建损失函数搜索空间"""
        space = LossSearchSpace()
        space.adversarial_weight_range = self.config.loss_adversarial_weight_range
        space.feature_matching_weight_range = self.config.loss_feature_matching_weight_range
        space.temporal_consistency_weight_range = self.config.loss_temporal_consistency_weight_range
        # 映射到实际的属性名
        space.lambda_gp_range = self.config.loss_gradient_penalty_lambda_range
        space.gp_target_norm_range = self.config.loss_gradient_penalty_target_range
        space.loss_types = [LossType(loss.upper()) for loss in self.config.loss_types]
        space.label_smoothing_range = self.config.loss_label_smoothing_range
        space.loss_clipping_range = self.config.loss_loss_clipping_range
        return space

    def _create_regularization_space_from_config(self) -> RegularizationSearchSpace:
        """从配置创建正则化搜索空间"""
        space = RegularizationSearchSpace()
        space.dropout_range = self.config.regularization_dropout_range
        space.weight_decay_range = self.config.regularization_weight_decay_range
        # 映射到实际的属性名
        space.gradient_clip_range = self.config.regularization_gradient_clipping_range
        space.normalization_types = [NormalizationType(norm.upper()) for norm in self.config.regularization_normalization_types]
        space.noise_std_range = self.config.regularization_noise_std_range
        space.input_noise_std_range = self.config.regularization_input_noise_range
        return space

    def sample_architecture(self) -> ArchitectureConfig:
        """采样一个完整的架构配置"""
        max_attempts = 100
        for attempt in range(max_attempts):
            try:
                config = ArchitectureConfig(
                    generator=self.generator_space.sample_random(),
                    discriminator=self.discriminator_space.sample_random(),
                    feature_encoder=self.feature_encoder_space.sample_random(),
                    training_strategy=self.training_strategy_space.sample_random(),
                    loss_config=self.loss_space.sample_random(),
                    regularization=self.regularization_space.sample_random()
                )

                if self.validate_architecture(config):
                    self.logger.info(f"成功采样架构配置 (尝试 {attempt + 1}/{max_attempts})")
                    return config

            except Exception as e:
                self.logger.warning(f"采样架构失败 (尝试 {attempt + 1}): {e}")

        raise RuntimeError(f"在 {max_attempts} 次尝试后无法采样有效架构")

    def validate_architecture(self, config: ArchitectureConfig) -> bool:
        """验证架构配置的有效性"""
        # 验证各组件配置
        if not self.generator_space.validate_config(config.generator):
            return False
        if not self.discriminator_space.validate_config(config.discriminator):
            return False
        if not self.feature_encoder_space.validate_config(config.feature_encoder):
            return False
        if not self.training_strategy_space.validate_config(config.training_strategy):
            return False
        if not self.loss_space.validate_config(config.loss_config):
            return False
        if not self.regularization_space.validate_config(config.regularization):
            return False

        # 估算内存使用量
        estimated_memory = self.estimate_memory_usage(config)
        if estimated_memory > self.memory_limit_gb:
            self.logger.debug(f"架构内存使用量过大: {estimated_memory:.2f}GB > {self.memory_limit_gb}GB")
            return False

        return True

    def estimate_memory_usage(self, config: ArchitectureConfig) -> float:
        """估算架构的内存使用量 (GB)"""
        # 简化的内存估算模型
        gen_params = config.generator.num_layers * config.generator.hidden_dim ** 2
        disc_params = config.discriminator.num_layers * config.discriminator.hidden_dim ** 2
        encoder_params = config.feature_encoder.encoder_depth * config.feature_encoder.num_scales * 1000

        total_params = gen_params + disc_params + encoder_params

        # 估算内存使用 (参数 + 梯度 + 激活值)
        memory_gb = total_params * 4 * 3 / (1024 ** 3)  # 4 bytes per float32, 3x for params+grads+activations

        return memory_gb
