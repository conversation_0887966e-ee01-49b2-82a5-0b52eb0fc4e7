"""
超参数优化核心模块

本模块提供使用 Optuna 框架进行超参数优化的核心功能。实现了两阶段优化策略，
通过快速筛选阶段高效探索参数空间，并报告最有潜力的参数组合。

主要组件:
----------
1. objective: Optuna目标函数，负责评估单个参数组合的性能
2. run_optimization: 主优化函数，管理整个优化过程
3. 各种配置辅助函数: 用于配置模型、训练、数据等参数

模块化设计:
----------
本模块采用模块化设计，将不同功能拆分到专门的模块中:
1. parameter_exploration: 参数探索策略
2. metric_extraction: 从训练结果中提取评估指标
3. memory_management: 内存监控和管理
4. result_analysis: 结果分析和报告

优化流程:
----------
1. 创建或加载Optuna Study
2. 执行快速筛选阶段，使用简化配置快速评估多个参数组合
3. 严格验证参数一致性，确保设定的参数被正确应用
4. 分析结果，找出最有潜力的参数组合
5. 报告前N个最佳参数组合

参数优化范围:
----------
- 模型容量 (medium/large)
- 注意力机制参数 (启用/禁用，头数)
- 窗口大小 (36-72)
- Dropout率 (0.1-0.5)
- 特征匹配权重 (0.5-5.0)
- 梯度惩罚权重 (5.0-25.0)

日志记录:
----------
本模块提供详细的日志记录，包括:
- 试验开始和结束信息
- 参数配置详情
- 训练过程信息
- 评估指标提取
- 内存使用情况
- 错误和异常处理

使用方式:
----------
通常通过optimize.py脚本调用，也可以在Python代码中直接使用:

```python
from src.optimization.hyperparameter_optimizer import run_optimization

run_optimization(
    n_trials=100,
    study_name="my_study",
    storage="sqlite:///my_study.db",
    top_n=5,
    base_config_path="config.yaml"
)
```

相关文件:
----------
- optimize.py: 命令行入口脚本
- src/optimization/parameter_exploration.py: 参数探索策略
- src/optimization/metric_extraction.py: 指标提取功能
- src/optimization/memory_management.py: 内存管理功能
- src/optimization/result_analysis.py: 结果分析功能
- src/optimization/logging_config.py: 日志配置
- config.yaml: 基础配置文件
"""

import csv  # ADDED
import math
import os
import time
from datetime import datetime

import optuna
import torch

from main import PipelineRunner

# Keep exceptions from optimization module
from src.optimization.exceptions import ConfigurationError, MetricError

# 导入内存管理模块
from src.optimization.memory_management import cleanup_memory, monitor_memory

# 导入指标提取模块
from src.optimization.metric_extraction import extract_metric

# 导入分级参数探索模块
from src.optimization.parameter_exploration import (
    parameter_explorer,
    update_runner_config,
)

# 导入结果分析模块
from src.optimization.result_analysis import (
    analyze_parameter_importance,
    analyze_trials,
    report_best_trials,
    report_optimization_summary,
)

# 导入验证模块
from src.optimization.validation import verify_parameter_consistency

# Import specific config types from utils using correct paths and names
from src.utils.config_manager import ConfigManager
from src.utils.logger import LoggerFactory

# 获取日志记录器
logger = LoggerFactory().get_logger("HyperOptimizer")

# 定义日志分隔符常量
LOG_SEPARATOR_SMALL = "-" * 40
LOG_SEPARATOR_MEDIUM = "=" * 60
LOG_SEPARATOR_LARGE = "#" * 80

def _configure_fast_mode(trial: optuna.trial.Trial, config: ConfigManager) -> None:
    """配置快速模式设置 - 始终启用，并临时禁用动态批次大小"""
    # 尝试从配置中获取快速模式设置
    if config.optimization is None:
        raise ConfigurationError(f"Trial #{trial.number}: 'optimization' 配置段为 None，无法应用快速模式。")

    fast_mode_config = config.optimization.fast_mode
    if fast_mode_config is None:
        raise ConfigurationError(f"Trial #{trial.number}: 'optimization.fast_mode' 配置段为 None，无法应用快速模式。")

    try:
        fast_epochs = fast_mode_config.num_epochs
        fast_batch_size = fast_mode_config.batch_size
        fast_log_level = fast_mode_config.log_level
    except AttributeError as e:
        # 此处 AttributeError 意味着 fast_mode_config 存在，但缺少其应有的属性
        raise ConfigurationError(f"Trial #{trial.number}: 'optimization.fast_mode' 配置对象缺少必需的属性: {e}") from e

    # 验证类型 (可选但推荐)
    if not isinstance(fast_epochs, int) or fast_epochs <= 0:
        raise ConfigurationError(f"Trial #{trial.number}: 'optimization.fast_mode.num_epochs' 必须是正整数，但得到 {fast_epochs}")
    if not isinstance(fast_batch_size, int) or fast_batch_size <= 0:
        raise ConfigurationError(f"Trial #{trial.number}: 'optimization.fast_mode.batch_size' 必须是正整数，但得到 {fast_batch_size}")
    if not isinstance(fast_log_level, str):
        raise ConfigurationError(f"Trial #{trial.number}: 'optimization.fast_mode.log_level' 必须是字符串，但得到 {fast_log_level}")


    # 保存原始值用于日志记录 (在应用更改之前)
    original_epochs = getattr(config.training, 'num_epochs', 'N/A')
    original_data_batch_size = getattr(config.data, 'batch_size', 'N/A')
    original_training_batch_size = getattr(config.training, 'batch_size', 'N/A')
    original_log_level = getattr(config.logging, 'level', 'N/A')
    dynamic_batch_size_enabled = getattr(config.training, 'dynamic_batch_size', 'N/A') if hasattr(config.training, 'dynamic_batch_size') else 'N/A'
    batch_size_optimizer = getattr(config.training, 'batch_size_optimizer', None)
    batch_size_optimizer_enabled = getattr(batch_size_optimizer, 'enabled', 'N/A') if batch_size_optimizer and hasattr(batch_size_optimizer, 'enabled') else 'N/A'


    # 应用快速模式配置 (从配置中读取)
    if hasattr(config.training, 'num_epochs'):
        config.training.num_epochs = fast_epochs
    else:
        raise ConfigurationError(f"Trial #{trial.number}: config.training 缺少 num_epochs 属性")

    if hasattr(config.data, 'batch_size'):
        config.data.batch_size = fast_batch_size
    else:
        raise ConfigurationError(f"Trial #{trial.number}: config.data 缺少 batch_size 属性")

    if hasattr(config.training, 'batch_size'):
        config.training.batch_size = fast_batch_size # 保持与 data.batch_size 一致
    else:
        raise ConfigurationError(f"Trial #{trial.number}: config.training 缺少 batch_size 属性")

    if hasattr(config.logging, 'level'):
        config.logging.level = fast_log_level
    else:
        raise ConfigurationError(f"Trial #{trial.number}: config.logging 缺少 level 属性")

    # 仍然禁用动态批次大小，因为这是快速模式的核心目的
    if hasattr(config.training, 'dynamic_batch_size'):
        config.training.dynamic_batch_size = False
    if batch_size_optimizer is not None and hasattr(batch_size_optimizer, 'enabled'):
        batch_size_optimizer.enabled = False


    # 记录配置变更
    logger.info(f"Trial #{trial.number}: 应用快速模式配置 (来自 optimization.fast_mode):")
    logger.info(f"- 训练轮数: {fast_epochs} (原始值: {original_epochs})")
    logger.info(f"- 批次大小(data): {fast_batch_size} (原始值: {original_data_batch_size})")
    logger.info(f"- 批次大小(training): {fast_batch_size} (原始值: {original_training_batch_size})")
    logger.info(f"- 日志级别: {fast_log_level} (原始值: {original_log_level})")
    logger.info(f"- 动态批次大小: 已禁用 (原始dynamic_batch_size: {dynamic_batch_size_enabled}, 原始batch_size_optimizer.enabled: {batch_size_optimizer_enabled})")


# 注意：以下函数已被参数探索模块中的分级参数探索策略替代，不再使用
# _configure_lr_params, _configure_balance_params, _configure_dropout_rate, _configure_window_size
# 这些函数的功能现在由 src/optimization/parameter_exploration.py 中的 HierarchicalParameterExplorer 处理


def objective(trial: optuna.trial.Trial, base_config_path: str = "config.yaml") -> float:
    """
    Optuna 目标函数。

    Args:
        trial: Optuna试验对象
        base_config_path: 基础配置文件路径

    Returns:
        float: 评估指标值(val_mae)
    """
    # 记录试验开始信息
    trial_start_time = time.time()
    logger.info(LOG_SEPARATOR_MEDIUM)
    logger.info(f"开始 Optuna 试验 #{trial.number}")
    logger.info(LOG_SEPARATOR_SMALL)

    # 初始化变量
    runner = None
    config: ConfigManager | None = None

    # 记录CUDA内存初始状态
    if torch.cuda.is_available():
        try:
            initial_memory = torch.cuda.memory_allocated() / (1024 * 1024)
            initial_reserved = torch.cuda.memory_reserved() / (1024 * 1024)
            logger.info(f"CUDA内存初始状态: 已分配={initial_memory:.2f}MB, 已缓存={initial_reserved:.2f}MB")
            # 重置峰值统计
            torch.cuda.reset_peak_memory_stats()
        except Exception as e:
            logger.warning(f"无法获取CUDA内存初始状态: {e}")

    try:
        # 1. 加载配置
        logger.info(f"加载基础配置: '{base_config_path}'")
        config_load_start = time.time()
        config = ConfigManager.from_yaml(base_config_path)
        config_load_time = time.time() - config_load_start
        logger.info(f"配置加载完成，耗时: {config_load_time:.2f}秒")

        # 2. 配置各个部分的超参数
        logger.info("开始配置超参数...")

        # 应用快速模式配置
        _configure_fast_mode(trial, config)

        # 使用分级参数探索策略配置参数
        logger.info(f"Trial #{trial.number}: 使用分级参数探索策略配置参数...")
        config = parameter_explorer.configure_parameters(trial, config)

        # 记录当前试验的参数
        logger.info("当前试验参数:")
        for key, value in trial.params.items():
            logger.info(f"- {key}: {value}")

        # 注意：OOM风险检查和嵌入维度与注意力头数的兼容性现在由 parameter_exploration.py 在建议参数时处理或剪枝
        # 因此，这里不再需要额外的检查或调整代码。

        logger.info("超参数配置完成")

        # 3. 严格验证参数一致性
        if config is not None:
            verify_parameter_consistency(trial, config)
        else:
            logger.error(f"Trial #{trial.number}: 无法验证参数一致性，配置对象为None")
            raise ConfigurationError("配置对象为None，无法验证参数一致性")

        # 4. 初始化 PipelineRunner
        logger.info("初始化PipelineRunner...")
        runner_init_start = time.time()
        runner = PipelineRunner(config_path=base_config_path)
        runner_init_time = time.time() - runner_init_start
        logger.info(f"PipelineRunner初始化完成，耗时: {runner_init_time:.2f}秒")

        # 更新运行器配置 (使用当前 trial 修改后的 config)
        if config is not None:
            update_runner_config(trial, runner, config)
        else:
            logger.error(f"Trial #{trial.number}: 无法更新运行器配置，配置对象为None")
            raise ConfigurationError("配置对象为None，无法更新运行器配置")

        # 5. 执行数据准备并获取实际维度
        logger.info(f"Trial #{trial.number}: 执行数据准备以获取实际维度...")
        prep_start_time = time.time()
        try:
            prep_result = runner.prepare_data_and_get_dimensions()
            actual_condition_feature_dim = prep_result["feature_dim"]
            precomputed_data = prep_result["model_input"]
            precomputed_pipeline = prep_result["data_pipeline"] # 获取 pipeline 实例
            prep_time = time.time() - prep_start_time
            logger.info(f"Trial #{trial.number}: 数据准备完成，实际 condition_feature_dim = {actual_condition_feature_dim}，耗时: {prep_time:.2f}秒")
        except Exception as e:
            logger.error(f"Trial #{trial.number}: 数据准备失败: {e}", exc_info=True)
            raise optuna.exceptions.TrialPruned(f"数据准备失败: {e}") from e

        # 6. 执行基于实际维度的剪枝检查 (针对注意力头数)
        logger.info(f"Trial #{trial.number}: 执行基于实际维度的剪枝检查...")
        try:
            # 获取 n_heads (确保从 trial.params 获取最新值)
            n_heads = trial.params.get('model.n_heads')

            # 检查 n_heads 是否已由 Optuna 建议
            if n_heads is not None:
                logger.info(f"Trial #{trial.number}: 检测到注意力头数参数，执行维度兼容性检查。")

                # 验证 n_heads 的类型和值
                if not isinstance(n_heads, int) or n_heads <= 0:
                    raise ConfigurationError(f"Trial #{trial.number}: 无效的 n_heads 值: {n_heads} (类型: {type(n_heads)})")

                # 计算 embed_dim (假设 target_dim = 1)
                embed_dim = 1 + actual_condition_feature_dim

                # 检查 embed_dim 是否能被 n_heads 整除
                if embed_dim % n_heads != 0:
                    prune_msg = f"Pruned trial {trial.number}: Actual embed_dim ({embed_dim} = 1 + {actual_condition_feature_dim}) not divisible by n_heads ({n_heads})"
                    logger.info(prune_msg)
                    raise optuna.exceptions.TrialPruned(prune_msg)
                else:
                    logger.info(f"Trial #{trial.number}: 实际维度检查通过: embed_dim ({embed_dim}) 可被 n_heads ({n_heads}) 整除。")
            else:
                logger.info(f"Trial #{trial.number}: 未检测到注意力头数参数，跳过维度兼容性检查。")

        except ConfigurationError as ce: # 专门处理配置错误
            logger.error(f"Trial #{trial.number}: 配置错误导致维度检查失败: {ce}", exc_info=True)
            raise ce # 重新抛出配置错误，不再转换为剪枝
        except optuna.exceptions.TrialPruned as tp: # 捕获并重新抛出剪枝异常
             logger.info(f"Trial {trial.number} 被剪枝 (维度不兼容).")
             raise tp
        except Exception as e: # 处理其他非配置错误
            logger.error(f"Trial #{trial.number}: 执行实际维度检查时发生意外错误: {e}", exc_info=True)
            raise optuna.exceptions.TrialPruned(f"维度检查错误: {e}") from e # 其他错误仍然剪枝

        # 7. 运行训练 (使用预计算的数据)
        logger.info(f"Trial #{trial.number}: 开始训练 (使用预计算数据)...")
        train_start_time = time.time()

        # 定义剪枝回调函数
        def pruning_callback():
            """Optuna剪枝回调，在检测到NaN/Inf时触发"""
            logger.warning(f"Trial #{trial.number}: 在训练早期检测到NaN/Inf，触发剪枝回调。")
            raise optuna.exceptions.TrialPruned("Pruned due to NaN/Inf detected in early training")

        # 执行训练，传递预计算的数据、维度和剪枝回调
        training_results = runner.train(
            precomputed_data=precomputed_data,
            precomputed_feature_dim=actual_condition_feature_dim,
            precomputed_pipeline=precomputed_pipeline, # 传递 pipeline
            pruning_callback=pruning_callback # 传递剪枝回调
        )

        # 记录训练完成
        train_time = time.time() - train_start_time
        logger.info(f"Trial #{trial.number}: 训练完成，耗时: {train_time:.2f}秒")

        # 4. 提取和验证指标
        logger.info(f"Trial #{trial.number}: 开始提取评估指标...")

        # 记录训练结果的基本结构
        # 确保 training_results 是字典再访问 keys
        if isinstance(training_results, dict):
            logger.debug(f"Trial #{trial.number}: 训练结果键: {list(training_results.keys())}")
            # 获取历史记录
            history = training_results.get("history", {})
        else:
            # If training results are not a dict, we cannot proceed. Prune the trial.
            error_msg = f"Trial #{trial.number}: 训练返回了非预期的结果类型: {type(training_results)}，无法提取历史记录"
            logger.error(error_msg)
            raise optuna.exceptions.TrialPruned(error_msg)
            # history = {} # No longer needed as we prune

        if not history:
            # If history is empty, we cannot extract metrics. Prune the trial.
            error_msg = f"Trial #{trial.number}: 训练未返回任何历史记录，无法提取指标"
            logger.error(error_msg)
            raise optuna.exceptions.TrialPruned(error_msg)
            # return float('inf') # No longer needed

        # 尝试从训练结果中提取指标
        val_mae = None # Initialize val_mae

        # 检查training_results中是否直接包含metrics
        if 'metrics' in training_results and isinstance(training_results['metrics'], dict):
            logger.debug(f"Trial #{trial.number}: 训练结果直接包含metrics: {list(training_results['metrics'].keys())}")

            # 如果metrics中包含mae，直接使用
            if 'mae' in training_results['metrics']:
                val_mae = training_results['metrics']['mae']
                logger.info(f"Trial #{trial.number}: 直接从训练结果metrics获取mae: {val_mae}")
            else:
                # 否则，尝试从history中提取
                logger.debug(f"Trial #{trial.number}: metrics中不包含mae，尝试从history中提取")
                try:
                    val_mae = extract_metric(history, trial.number)
                except MetricError as me:
                    # If metric extraction fails, prune the trial.
                    error_msg = f"Trial #{trial.number}: 从history中提取指标失败: {me}"
                    logger.error(error_msg)
                    raise optuna.exceptions.TrialPruned(error_msg) from me
                    # val_mae = float('inf') # No longer needed
        else:
            # 从history中提取
            logger.debug(f"Trial #{trial.number}: 训练结果不包含metrics，尝试从history中提取")
            try:
                val_mae = extract_metric(history, trial.number)
            except MetricError as me:
                # If metric extraction fails, prune the trial.
                error_msg = f"Trial #{trial.number}: 从history中提取指标失败: {me}"
                logger.error(error_msg)
                raise optuna.exceptions.TrialPruned(error_msg) from me
                # val_mae = float('inf') # No longer needed

        # 记录最终指标值
        if math.isfinite(val_mae):
            logger.info(f"Trial #{trial.number}: 训练完成，目标指标 (val_mae): {val_mae:.6f}")
        else:
            logger.info(f"Trial #{trial.number}: 训练完成，目标指标 (val_mae): {val_mae} (无效值)")

            # 添加对训练过程的更多分析
            logger.info(f"Trial #{trial.number}: 分析训练过程中可能的问题...")

            # 检查训练参数
            critical_params = []
            # 检查窗口大小和注意力头数的组合
            if 'model.n_heads' in trial.params and 'data.window_size' in trial.params:
                n_heads = trial.params['model.n_heads']
                window_size = trial.params['data.window_size']
                if n_heads > 4 and window_size > 36:
                    critical_params.append(f"大窗口大小({window_size})与多注意力头数({n_heads})组合")

            if 'model.dropout_rate' in trial.params and trial.params['model.dropout_rate'] < 0.15:
                critical_params.append(f"低Dropout率({trial.params['model.dropout_rate']})")

            if 'model.loss.feature_matching_weight' in trial.params and trial.params['model.loss.feature_matching_weight'] > 4.0:
                critical_params.append(f"高特征匹配权重({trial.params['model.loss.feature_matching_weight']})")

            if critical_params:
                logger.warning(f"Trial #{trial.number}: 检测到可能导致训练不稳定的参数组合:")
                for param in critical_params:
                    logger.warning(f"  - {param}")
                logger.warning(f"Trial #{trial.number}: 建议在未来的试验中避免这些参数组合")
            else:
                logger.info(f"Trial #{trial.number}: 未检测到明显的问题参数组合")

            # 检查训练历史中的异常模式
            if 'history' in training_results and isinstance(training_results['history'], dict):
                history = training_results['history']
                # 检查损失是否在第一轮就变为NaN
                for loss_key in ['train_g_loss', 'train_d_loss', 'val_loss']:
                    if loss_key in history and isinstance(history[loss_key], list) and history[loss_key] and not math.isfinite(history[loss_key][0]):
                            logger.warning(f"Trial #{trial.number}: {loss_key}在第一轮就变为NaN，可能是初始化或学习率问题")

                # 检查是否存在梯度爆炸
                if 'gradient_stats' in history and isinstance(history['gradient_stats'], dict):
                    # 从配置获取梯度爆炸阈值
                    try:
                        grad_threshold = config.training.gradient_explosion_threshold
                        if not isinstance(grad_threshold, int | float) or grad_threshold <= 0:
                             raise ConfigurationError(f"config.training.gradient_explosion_threshold ('{grad_threshold}') 必须是正数")
                    except AttributeError as e:
                        raise ConfigurationError(f"Trial #{trial.number}: 缺少 'config.training.gradient_explosion_threshold' 配置，无法检查梯度爆炸。") from e

                    for model_name, stats in history['gradient_stats'].items():
                        if isinstance(stats, dict) and 'max' in stats and stats['max'] > grad_threshold: # 使用配置中的阈值
                                logger.warning(f"Trial #{trial.number}: 检测到{model_name}可能存在梯度爆炸 (最大梯度: {stats['max']:.2f} > 阈值: {grad_threshold:.2f})")

        # 使用内存管理模块监控CUDA内存使用情况
        current_memory, peak_memory = monitor_memory()
        if current_memory > 0:
            logger.info(f"Trial #{trial.number}: CUDA内存使用: 当前={current_memory:.2f}MB, 峰值={peak_memory:.2f}MB")

        # --- BEGIN: 添加保存完整试验结果到CSV文件的逻辑 ---
        if math.isfinite(val_mae): # 只记录有效的试验结果
            output_dir = "outputs/results"
            # 确保路径分隔符正确，并使用os.path.join
            # 对于Windows，os.path.join会自动使用反斜杠，但为了跨平台，这是好习惯
            # 对于此项目，我们知道是Windows，但保持良好实践
            base_project_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")) # 获取项目根目录 E:\prj\u21
            full_output_dir = os.path.join(base_project_dir, output_dir)
            os.makedirs(full_output_dir, exist_ok=True)
            results_file_path = os.path.join(full_output_dir, "parameter_exploration_full_results.csv")

            # 将 trial.params 转换为扁平字典，以便写入CSV
            flat_params = {key: str(value) for key, value in trial.params.items()}

            # 确保字段名一致且包含所有需要的信息
            fieldnames = [*sorted(flat_params.keys()), 'objective_value', 'trial_number', 'trial_datetime_start', 'trial_duration_seconds']

            write_header = not os.path.exists(results_file_path) or os.path.getsize(results_file_path) == 0

            trial_duration = time.time() - trial_start_time # trial_start_time 在 objective 函数开头定义

            with open(results_file_path, 'a', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames, extrasaction='ignore') # extrasaction='ignore' 避免因params中新增字段导致错误
                if write_header:
                    writer.writeheader()

                row_data = flat_params.copy()
                row_data['objective_value'] = str(val_mae)
                row_data['trial_number'] = str(trial.number)
                row_data['trial_datetime_start'] = trial.datetime_start.isoformat() if trial.datetime_start else 'N/A'
                row_data['trial_duration_seconds'] = str(round(trial_duration, 2))
                writer.writerow(row_data)
            logger.info(f"Trial #{trial.number}: 结果已追加到 {results_file_path}")
        else:
            logger.info(f"Trial #{trial.number}: 由于目标指标无效 ({val_mae})，未将结果保存到CSV。")
        # --- END: 添加保存完整试验结果到CSV文件的逻辑 ---

        # 返回指标值（可能是有效值或无效值）
        return float(val_mae)

    except optuna.exceptions.TrialPruned as tp:
        # Let prune exceptions propagate
        logger.info(f"Trial {trial.number} 被剪枝.")
        raise tp
    except ConfigurationError as ce:
        # 配置错误是严重错误，直接抛出，导致程序退出
        logger.critical(f"Trial {trial.number} 失败 (配置错误): {ce!s}", exc_info=True)
        logger.critical("配置错误是严重错误，程序将立即退出")
        raise ce  # 直接重新抛出配置错误，不转换为TrialPruned
    except MetricError as me:
        # 指标错误可以转换为TrialPruned，允许优化继续
        logger.error(f"Trial {trial.number} 失败 (指标错误): {me!s}", exc_info=True)
        raise optuna.exceptions.TrialPruned(f"指标错误: {me}") from me
    except Exception as e:
        # 其他异常转换为TrialPruned，以便Optuna可以继续优化过程
        logger.error(f"Trial {trial.number} 失败 (其他错误): {e!s}", exc_info=True)
        raise optuna.exceptions.TrialPruned(f"其他错误: {e}") from e

    finally:
        # 清理资源
        if runner is not None:
            try:
                logger.info(f"Trial #{trial.number}: 清理资源...")
                runner.cleanup()
                logger.info(f"Trial #{trial.number}: 资源清理完成")
            except Exception as e:
                logger.error(f"Trial #{trial.number}: 资源清理失败: {e}", exc_info=True)

        # 使用内存管理模块清理CUDA内存
        cleanup_memory(trial)

        # 计算总耗时
        duration = time.time() - trial_start_time
        minutes, seconds = divmod(duration, 60)
        time_str = f"{int(minutes)}分钟{seconds:.2f}秒"

        logger.info(LOG_SEPARATOR_SMALL)
        logger.info(f"Trial #{trial.number}: 结束，总耗时: {time_str}")
        logger.info(LOG_SEPARATOR_MEDIUM)


def run_optimization(
    n_trials: int = 100,
    study_name: str = "gan_hyperopt",
    storage: str = "sqlite:///optuna_study.db",
    top_n: int = 5,
    base_config_path: str = "config.yaml"
) -> None:
    """
    创建并运行 Optuna Study (两阶段优化)。

    Args:
        n_trials: 试验次数
        study_name: Optuna Study名称
        storage: Optuna存储后端URL
        top_n: 报告前N个最佳参数组合
        base_config_path: 基础配置文件路径
    """
    # 记录优化开始
    optimization_start_time = time.time()
    logger.info(LOG_SEPARATOR_LARGE)
    logger.info(f"开始超参数优化 | {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(LOG_SEPARATOR_MEDIUM)
    logger.info("配置信息:")
    logger.info(f"- Study名称: '{study_name}'")
    logger.info(f"- 存储后端: '{storage}'")
    logger.info(f"- 试验次数: {n_trials}")
    logger.info(f"- 报告前N个: {top_n}")
    logger.info(f"- 基础配置: '{base_config_path}'")

    # 记录系统信息
    if torch.cuda.is_available():
        device_name = torch.cuda.get_device_name(0)
        device_cap = torch.cuda.get_device_capability(0)
        total_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        logger.info(f"CUDA设备: {device_name} (能力: {device_cap[0]}.{device_cap[1]}, 内存: {total_memory:.1f}GB)")
    else:
        logger.warning("CUDA不可用，将使用CPU进行优化")

    # 检查配置文件
    if not os.path.exists(base_config_path):
        error_msg = f"基础配置文件不存在: {base_config_path}"
        logger.error(error_msg)
        raise FileNotFoundError(error_msg)

    try:
        # 创建或加载Study
        logger.info(f"创建/加载Optuna Study: '{study_name}'")

        study = optuna.create_study(
            study_name=study_name,
            storage=storage,
            direction='minimize',
            load_if_exists=True
        )

        # 记录Study信息
        existing_trials = len(study.trials)
        if existing_trials > 0:
            logger.info(f"已加载现有Study，包含 {existing_trials} 个试验")
            # 检查是否有已完成的试验，避免在没有完成试验时访问 best_trial 导致错误
            completed_trials = study.get_trials(deepcopy=False, states=(optuna.trial.TrialState.COMPLETE,))
            if completed_trials:
                try:
                    # 只有在存在已完成试验时才尝试获取最佳试验
                    best_trial = study.best_trial
                    logger.info(f"当前最佳试验: #{best_trial.number}, 值: {best_trial.value}")
                except ValueError as e:
                    # 添加额外的捕获，以防万一 get_trials 和 best_trial 之间存在竞态条件或意外情况
                    logger.warning(f"尝试获取最佳试验时出错（可能没有已完成的试验）: {e}")
            else:
                logger.info("当前没有已完成的试验记录。")
        else:
            logger.info("创建了新的Study")

        # --- 第一阶段: 快速筛选 ---
        logger.info(LOG_SEPARATOR_MEDIUM)
        logger.info("=== 第一阶段: 快速参数筛选 ===")
        logger.info(LOG_SEPARATOR_SMALL)

        # 开始优化
        phase1_start_time = time.time()
        study.optimize(
            lambda trial: objective(trial, base_config_path=base_config_path),
            n_trials=n_trials,
            timeout=None,
            gc_after_trial=True,
        )
        phase1_duration = time.time() - phase1_start_time

        # --- 分析结果并报告最佳参数 ---
        # 使用结果分析模块报告阶段完成信息
        report_optimization_summary(study, optimization_start_time, phase1_duration, "快速筛选阶段")

        # 使用结果分析模块分析试验结果
        _, sorted_trials = analyze_trials(study)  # 第一个返回值是试验统计信息，这里不需要使用

        # 检查是否有有效的完成试验
        if not sorted_trials:
            logger.warning("未找到有效的已完成试验。无法确定最佳参数。")
            return

        # 使用结果分析模块报告最佳参数组合
        report_best_trials(sorted_trials, top_n)

        # 尝试分析参数重要性
        analyze_parameter_importance(study)

        # 使用结果分析模块报告优化总结
        report_optimization_summary(study, optimization_start_time)

    except ConfigurationError as ce:
        # 配置错误是严重错误，需要立即修复
        logger.critical(f"优化过程失败 (配置错误): {ce!s}", exc_info=True)
        logger.critical("配置错误是严重错误，需要立即修复后再运行")

        # 使用内存管理模块清理资源
        cleanup_memory()

        # 直接重新抛出配置错误，不做任何转换
        raise
    except Exception as e:
        logger.error(f"优化过程失败: {e!s}", exc_info=True)

        # 使用内存管理模块清理资源
        cleanup_memory()

        # 重新抛出异常
        raise

if __name__ == '__main__':
    run_optimization(n_trials=50, top_n=5, study_name="gan_hpo_fast_v3", storage="sqlite:///gan_hpo_study_fast.db")
