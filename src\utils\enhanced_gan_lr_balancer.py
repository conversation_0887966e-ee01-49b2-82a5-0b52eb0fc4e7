"""
增强版GAN学习率平衡器 - 基于多指标动态调整

该平衡器综合考虑以下指标进行学习率调整：
1. 损失比率 (D/G Loss Ratio)
2. 模型性能指标 (MAE, MSE, RMSE)
3. 训练稳定性指标 (梯度范数, 损失变化率)
4. 收敛趋势分析 (性能改善趋势)
"""

import math
from collections import deque
from typing import Dict, Optional, Tuple

import torch
from torch import optim

from src.utils.logger import get_logger


class EnhancedGanLrBalancer:
    """
    增强版GAN学习率平衡器 - 基于多指标动态调整

    该平衡器综合考虑损失比率、性能指标、训练稳定性等多个维度
    来动态调整生成器和判别器的学习率，实现更智能的训练控制。
    """

    def __init__(
        self,
        optimizer_g: optim.Optimizer,
        optimizer_d: optim.Optimizer,
        target_ratio: float = 1.2,
        sensitivity: float = 0.1,
        min_lr: float = 1e-4,
        max_lr: float = 4e-3,
        epsilon: float = 1e-8,
        # 性能指标相关参数
        performance_weight: float = 0.3,
        stability_weight: float = 0.2,
        loss_ratio_weight: float = 0.5,
        # 历史窗口大小
        history_window: int = 5,
        # 性能改善阈值
        improvement_threshold: float = 0.05,
        # 稳定性阈值
        stability_threshold: float = 0.1,
        # 新增：调整模式配置
        adjustment_mode: str = "adaptive",  # "sync", "inverse", "adaptive"
        mode_switch_threshold: float = 0.3,  # 自适应模式切换阈值
        logger_name: Optional[str] = None
    ):
        """
        初始化增强版学习率平衡器

        Args:
            optimizer_g: 生成器优化器
            optimizer_d: 判别器优化器
            target_ratio: 目标D/G损失比率
            sensitivity: 调整敏感度
            min_lr: 最小学习率
            max_lr: 最大学习率
            epsilon: 数值稳定性参数
            performance_weight: 性能指标权重
            stability_weight: 稳定性指标权重
            loss_ratio_weight: 损失比率权重
            history_window: 历史数据窗口大小
            improvement_threshold: 性能改善阈值
            stability_threshold: 稳定性阈值
            adjustment_mode: 调整模式 ("sync": 同步调整, "inverse": 反向调整, "adaptive": 自适应)
            mode_switch_threshold: 自适应模式切换阈值
            logger_name: 日志器名称
        """
        self.optimizer_g = optimizer_g
        self.optimizer_d = optimizer_d
        self.target_ratio = target_ratio
        self.sensitivity = sensitivity
        self.min_lr = min_lr
        self.max_lr = max_lr
        self.epsilon = epsilon

        # 多指标权重
        self.performance_weight = performance_weight
        self.stability_weight = stability_weight
        self.loss_ratio_weight = loss_ratio_weight

        # 确保权重和为1
        total_weight = performance_weight + stability_weight + loss_ratio_weight
        if abs(total_weight - 1.0) > 1e-6:
            self.performance_weight /= total_weight
            self.stability_weight /= total_weight
            self.loss_ratio_weight /= total_weight

        # 历史数据存储
        self.history_window = history_window
        self.loss_history = deque(maxlen=history_window)
        self.performance_history = deque(maxlen=history_window)
        self.gradient_norm_history = deque(maxlen=history_window)

        # 阈值设置
        self.improvement_threshold = improvement_threshold
        self.stability_threshold = stability_threshold

        # 新增：调整模式配置
        self.adjustment_mode = adjustment_mode
        self.mode_switch_threshold = mode_switch_threshold

        # 验证调整模式
        valid_modes = ["sync", "inverse", "adaptive"]
        if adjustment_mode not in valid_modes:
            raise ValueError(f"adjustment_mode必须是{valid_modes}中的一个，但获取到: {adjustment_mode}")

        # 日志器
        self.logger = get_logger(logger_name or self.__class__.__name__)

        self.logger.info(
            f"增强版GAN学习率平衡器初始化完成:\n"
            f"- 目标D/G损失比率: {self.target_ratio:.4f}\n"
            f"- 调整敏感度: {self.sensitivity:.4f}\n"
            f"- 学习率范围: [{self.min_lr:.6f}, {self.max_lr:.6f}]\n"
            f"- 权重分配: 损失比率={self.loss_ratio_weight:.2f}, "
            f"性能={self.performance_weight:.2f}, 稳定性={self.stability_weight:.2f}\n"
            f"- 历史窗口: {self.history_window}\n"
            f"- 调整模式: {self.adjustment_mode} (切换阈值: {self.mode_switch_threshold:.2f})"
        )

    def step(
        self,
        g_loss: float,
        d_loss: float,
        performance_metrics: Dict[str, float],
        gradient_norms: Optional[Dict[str, float]] = None
    ):
        """
        基于多指标进行学习率调整

        Args:
            g_loss: 生成器损失
            d_loss: 判别器损失
            performance_metrics: 性能指标字典 (包含mae, mse, rmse等)
            gradient_norms: 梯度范数字典 (可选)
        """
        # 1. 计算损失比率分数
        loss_ratio_score = self._calculate_loss_ratio_score(g_loss, d_loss)

        # 2. 计算性能指标分数
        performance_score = self._calculate_performance_score(performance_metrics)

        # 3. 计算稳定性分数
        stability_score = self._calculate_stability_score(g_loss, d_loss, gradient_norms)

        # 4. 综合评分
        composite_score = (
            self.loss_ratio_weight * loss_ratio_score +
            self.performance_weight * performance_score +
            self.stability_weight * stability_score
        )

        # 5. 基于综合评分调整学习率
        self._adjust_learning_rates(composite_score, g_loss, d_loss, performance_metrics)

        # 6. 更新历史数据
        self._update_history(g_loss, d_loss, performance_metrics, gradient_norms)

    def _calculate_loss_ratio_score(self, g_loss: float, d_loss: float) -> float:
        """计算损失比率分数 (-1到1，0表示完美平衡)"""
        # 处理极小损失值
        if abs(g_loss) < self.epsilon:
            g_loss = self.epsilon if g_loss >= 0 else -self.epsilon

        # 计算实际比率
        actual_ratio = abs(d_loss) / abs(g_loss)

        # 限制比率范围
        actual_ratio = max(0.1, min(actual_ratio, 10.0))

        # 计算偏差并归一化到[-1, 1]
        deviation = actual_ratio - self.target_ratio
        normalized_deviation = deviation / self.target_ratio

        # 限制到[-1, 1]范围
        return max(-1.0, min(1.0, normalized_deviation))

    def _calculate_performance_score(self, performance_metrics: Dict[str, float]) -> float:
        """计算性能指标分数 (-1到1，1表示性能改善，-1表示性能恶化) - 优化版本"""
        if len(self.performance_history) < 1:
            return 0.0  # 历史数据不足，返回中性分数

        # 获取主要性能指标 (优先使用MAE)
        current_mae = performance_metrics.get('val_mae', performance_metrics.get('mae', None))
        if current_mae is None:
            return 0.0

        # 如果只有一个历史记录，与当前值比较
        if len(self.performance_history) == 1:
            prev_mae = self.performance_history[0].get('val_mae', self.performance_history[0].get('mae', float('inf')))
            if prev_mae > 0 and current_mae > 0:
                improvement = (prev_mae - current_mae) / prev_mae
                normalized_improvement = improvement / self.improvement_threshold
                return max(-1.0, min(1.0, normalized_improvement))
            return 0.0

        # 计算短期和长期改善趋势
        recent_performance = list(self.performance_history)[-3:]  # 最近3个值

        # 短期改善 (最近1-2步)
        short_term_improvements = []
        if len(recent_performance) >= 2:
            for i in range(1, min(3, len(recent_performance) + 1)):  # 包含当前值
                if i == len(recent_performance):
                    # 当前值与最近历史值比较
                    prev_mae = recent_performance[-1].get('val_mae', recent_performance[-1].get('mae', float('inf')))
                    curr_mae = current_mae
                else:
                    prev_mae = recent_performance[i-1].get('val_mae', recent_performance[i-1].get('mae', float('inf')))
                    curr_mae = recent_performance[i].get('val_mae', recent_performance[i].get('mae', float('inf')))

                if prev_mae > 0 and curr_mae > 0:
                    improvement = (prev_mae - curr_mae) / prev_mae
                    short_term_improvements.append(improvement)

        # 长期趋势 (整个历史窗口)
        long_term_improvements = []
        all_performance = list(self.performance_history) + [performance_metrics]
        if len(all_performance) >= 2:
            for i in range(1, len(all_performance)):
                prev_mae = all_performance[i-1].get('val_mae', all_performance[i-1].get('mae', float('inf')))
                curr_mae = all_performance[i].get('val_mae', all_performance[i].get('mae', float('inf')))

                if prev_mae > 0 and curr_mae > 0:
                    improvement = (prev_mae - curr_mae) / prev_mae
                    long_term_improvements.append(improvement)

        # 综合评分：短期权重70%，长期权重30%
        short_term_score = 0.0
        if short_term_improvements:
            avg_short_improvement = sum(short_term_improvements) / len(short_term_improvements)
            short_term_score = avg_short_improvement / self.improvement_threshold

        long_term_score = 0.0
        if long_term_improvements:
            avg_long_improvement = sum(long_term_improvements) / len(long_term_improvements)
            long_term_score = avg_long_improvement / self.improvement_threshold

        # 加权综合评分
        composite_performance_score = 0.7 * short_term_score + 0.3 * long_term_score

        # 对显著改善给予额外奖励
        if short_term_improvements and max(short_term_improvements) > self.improvement_threshold * 2:
            composite_performance_score *= 1.2  # 20%奖励

        # 归一化到[-1, 1]
        return max(-1.0, min(1.0, composite_performance_score))

    def _calculate_stability_score(
        self,
        g_loss: float,
        d_loss: float,
        gradient_norms: Optional[Dict[str, float]]
    ) -> float:
        """计算稳定性分数 (-1到1，1表示稳定，-1表示不稳定)"""
        if len(self.loss_history) < 3:
            return 0.0  # 历史数据不足

        # 计算损失变化的稳定性
        recent_losses = list(self.loss_history)[-3:]
        g_losses = [loss['g_loss'] for loss in recent_losses]
        d_losses = [loss['d_loss'] for loss in recent_losses]

        # 计算变异系数 (标准差/均值)
        g_cv = self._coefficient_of_variation(g_losses)
        d_cv = self._coefficient_of_variation(d_losses)

        # 平均变异系数
        avg_cv = (g_cv + d_cv) / 2

        # 归一化稳定性分数 (变异系数越小越稳定)
        stability_from_loss = max(0.0, 1.0 - avg_cv / self.stability_threshold)

        # 如果有梯度范数信息，也考虑梯度稳定性
        stability_from_gradient = 0.5  # 默认中性分数
        if gradient_norms and len(self.gradient_norm_history) >= 2:
            recent_grad_norms = list(self.gradient_norm_history)[-3:]
            if recent_grad_norms:
                g_grad_norms = [gn.get('g_grad_norm', 1.0) for gn in recent_grad_norms if gn]
                d_grad_norms = [gn.get('d_grad_norm', 1.0) for gn in recent_grad_norms if gn]

                if g_grad_norms and d_grad_norms:
                    g_grad_cv = self._coefficient_of_variation(g_grad_norms)
                    d_grad_cv = self._coefficient_of_variation(d_grad_norms)
                    avg_grad_cv = (g_grad_cv + d_grad_cv) / 2
                    stability_from_gradient = max(0.0, 1.0 - avg_grad_cv / self.stability_threshold)

        # 综合稳定性分数
        overall_stability = (stability_from_loss + stability_from_gradient) / 2

        # 转换到[-1, 1]范围 (0.5对应0，1对应1，0对应-1)
        return 2 * overall_stability - 1

    def _coefficient_of_variation(self, values: list) -> float:
        """计算变异系数"""
        if len(values) < 2:
            return 0.0

        mean_val = sum(values) / len(values)
        if abs(mean_val) < self.epsilon:
            return 0.0

        variance = sum((x - mean_val) ** 2 for x in values) / len(values)
        std_val = math.sqrt(variance)

        return std_val / abs(mean_val)

    def _adjust_learning_rates(
        self,
        composite_score: float,
        g_loss: float,
        d_loss: float,
        performance_metrics: Dict[str, float]
    ):
        """基于综合评分调整学习率 - 支持多种调整模式"""
        # 获取当前学习率
        current_g_lr = self._get_lr(self.optimizer_g)
        current_d_lr = self._get_lr(self.optimizer_d)

        # 计算各个分量分数用于详细日志
        loss_ratio_score = self._calculate_loss_ratio_score(g_loss, d_loss)
        performance_score = self._calculate_performance_score(performance_metrics)
        stability_score = self._calculate_stability_score(g_loss, d_loss, None)

        # 检查学习率恢复机制 - 防止学习率过度下降
        lr_recovery_needed = self._check_lr_recovery_needed(current_g_lr, current_d_lr)

        # 确定当前使用的调整模式
        active_mode = self._determine_adjustment_mode(composite_score, loss_ratio_score, performance_score, stability_score)

        # 根据模式调整学习率
        if active_mode == "sync":
            new_g_lr, new_d_lr, direction = self._sync_adjustment(
                composite_score, current_g_lr, current_d_lr, lr_recovery_needed
            )
        elif active_mode == "inverse":
            new_g_lr, new_d_lr, direction = self._inverse_adjustment(
                loss_ratio_score, current_g_lr, current_d_lr
            )
        else:  # adaptive mode
            new_g_lr, new_d_lr, direction = self._adaptive_adjustment(
                composite_score, loss_ratio_score, current_g_lr, current_d_lr, lr_recovery_needed
            )

        # 应用学习率限制
        new_g_lr = max(self.min_lr, min(self.max_lr, new_g_lr))
        new_d_lr = max(self.min_lr, min(self.max_lr, new_d_lr))

        # 检查学习率趋势并发出警告
        self._check_lr_trend_warning(current_g_lr, new_g_lr)

        # 设置新学习率
        self._set_lr(self.optimizer_g, new_g_lr)
        self._set_lr(self.optimizer_d, new_d_lr)

        # 增强日志记录 - 包含各分量分数和调整模式
        mae_value = performance_metrics.get('val_mae', performance_metrics.get('mae', 'N/A'))
        self.logger.info(
            f"多指标学习率调整: 综合评分={composite_score:.4f}, 模式={active_mode}, 调整方向='{direction}'\n"
            f"  分量评分: 损失比率={loss_ratio_score:.3f}, 性能={performance_score:.3f}, 稳定性={stability_score:.3f}\n"
            f"  性能指标: MAE={mae_value}\n"
            f"  损失状态: G={g_loss:.4f}, D={d_loss:.4f}\n"
            f"  学习率: G_LR {current_g_lr:.6f} -> {new_g_lr:.6f}, "
            f"D_LR {current_d_lr:.6f} -> {new_d_lr:.6f}"
        )

    def _determine_adjustment_mode(
        self,
        composite_score: float,
        loss_ratio_score: float,
        performance_score: float,
        stability_score: float
    ) -> str:
        """确定当前使用的调整模式"""
        if self.adjustment_mode != "adaptive":
            return self.adjustment_mode

        # 自适应模式：根据当前状态选择最佳调整策略

        # 如果损失比率严重不平衡，优先使用反向调整
        if abs(loss_ratio_score) > self.mode_switch_threshold:
            return "inverse"

        # 如果性能或稳定性问题突出，使用同步调整
        if abs(performance_score) > self.mode_switch_threshold or abs(stability_score) > self.mode_switch_threshold:
            return "sync"

        # 如果综合评分较高，使用同步调整
        if abs(composite_score) > self.mode_switch_threshold:
            return "sync"

        # 默认使用反向调整（更保守）
        return "inverse"

    def _sync_adjustment(
        self,
        composite_score: float,
        current_g_lr: float,
        current_d_lr: float,
        lr_recovery_needed: bool
    ) -> tuple[float, float, str]:
        """同步调整模式：生成器和判别器同步调整学习率"""
        # 计算调整因子
        base_adjustment = 1.0 + self.sensitivity * abs(composite_score)

        # 根据评分强度使用不同的调整策略
        if abs(composite_score) > 0.5:  # 强信号
            adjustment_factor = min(base_adjustment, 1.4)  # 较大调整
        elif abs(composite_score) > 0.2:  # 中等信号
            adjustment_factor = min(base_adjustment, 1.2)  # 中等调整
        else:  # 弱信号
            adjustment_factor = min(base_adjustment, 1.1)  # 小幅调整

        # 调整阈值
        positive_threshold = 0.05
        negative_threshold = -0.05

        # 根据综合评分决定调整方向
        if composite_score > positive_threshold or lr_recovery_needed:
            new_g_lr = current_g_lr * adjustment_factor
            new_d_lr = current_d_lr * adjustment_factor
            if lr_recovery_needed:
                direction = "同步恢复学习率"
                recovery_factor = 1.3
                new_g_lr = current_g_lr * recovery_factor
                new_d_lr = current_d_lr * recovery_factor
            else:
                direction = "同步增强训练"
        elif composite_score < negative_threshold:
            new_g_lr = current_g_lr / adjustment_factor
            new_d_lr = current_d_lr / adjustment_factor
            direction = "同步减缓训练"
        else:
            new_g_lr = current_g_lr
            new_d_lr = current_d_lr
            direction = "同步保持当前"

        return new_g_lr, new_d_lr, direction

    def _inverse_adjustment(
        self,
        loss_ratio_score: float,
        current_g_lr: float,
        current_d_lr: float
    ) -> tuple[float, float, str]:
        """反向调整模式：基于损失比率反向调整生成器和判别器学习率"""
        # 计算调整因子
        base_adjustment = 1.0 + self.sensitivity * abs(loss_ratio_score)
        adjustment_factor = min(base_adjustment, 1.3)  # 反向调整更保守

        # 调整阈值
        threshold = 0.1

        if loss_ratio_score > threshold:  # D强/G弱 -> 提高G，降低D
            new_g_lr = current_g_lr * adjustment_factor
            new_d_lr = current_d_lr / adjustment_factor
            direction = "反向调整: 提高G/降低D"
        elif loss_ratio_score < -threshold:  # G强/D弱 -> 降低G，提高D
            new_g_lr = current_g_lr / adjustment_factor
            new_d_lr = current_d_lr * adjustment_factor
            direction = "反向调整: 降低G/提高D"
        else:
            new_g_lr = current_g_lr
            new_d_lr = current_d_lr
            direction = "反向调整: 保持平衡"

        return new_g_lr, new_d_lr, direction

    def _adaptive_adjustment(
        self,
        composite_score: float,
        loss_ratio_score: float,
        current_g_lr: float,
        current_d_lr: float,
        lr_recovery_needed: bool
    ) -> tuple[float, float, str]:
        """自适应调整模式：结合同步和反向调整的优势"""
        # 计算两种调整策略的结果
        sync_g_lr, sync_d_lr, sync_direction = self._sync_adjustment(
            composite_score, current_g_lr, current_d_lr, lr_recovery_needed
        )
        inverse_g_lr, inverse_d_lr, inverse_direction = self._inverse_adjustment(
            loss_ratio_score, current_g_lr, current_d_lr
        )

        # 根据当前状态决定权重
        if abs(loss_ratio_score) > abs(composite_score - loss_ratio_score):
            # 损失比率问题更突出，偏向反向调整
            weight_inverse = 0.7
            weight_sync = 0.3
            direction = f"自适应调整: 偏向反向 ({inverse_direction})"
        else:
            # 综合问题更突出，偏向同步调整
            weight_inverse = 0.3
            weight_sync = 0.7
            direction = f"自适应调整: 偏向同步 ({sync_direction})"

        # 加权融合
        new_g_lr = weight_sync * sync_g_lr + weight_inverse * inverse_g_lr
        new_d_lr = weight_sync * sync_d_lr + weight_inverse * inverse_d_lr

        return new_g_lr, new_d_lr, direction

    def _update_history(
        self,
        g_loss: float,
        d_loss: float,
        performance_metrics: Dict[str, float],
        gradient_norms: Optional[Dict[str, float]]
    ):
        """更新历史数据"""
        self.loss_history.append({'g_loss': g_loss, 'd_loss': d_loss})
        self.performance_history.append(performance_metrics.copy())

        if gradient_norms:
            self.gradient_norm_history.append(gradient_norms.copy())
        else:
            self.gradient_norm_history.append({})

    def _get_lr(self, optimizer: optim.Optimizer) -> float:
        """获取优化器的学习率"""
        return optimizer.param_groups[0]['lr']

    def _set_lr(self, optimizer: optim.Optimizer, lr: float):
        """设置优化器的学习率"""
        for param_group in optimizer.param_groups:
            param_group['lr'] = lr

    def _check_lr_recovery_needed(self, current_g_lr: float, current_d_lr: float) -> bool:
        """检查是否需要学习率恢复机制"""
        # 如果学习率接近最小值且连续下降，则需要恢复
        min_lr_threshold = self.min_lr * 2.0  # 最小学习率的2倍作为阈值

        # 检查当前学习率是否过低
        if current_g_lr <= min_lr_threshold or current_d_lr <= min_lr_threshold:
            # 检查最近的性能是否有改善趋势
            if len(self.performance_history) >= 2:
                recent_performance = list(self.performance_history)[-2:]
                if len(recent_performance) == 2:
                    prev_mae = recent_performance[0].get('val_mae', recent_performance[0].get('mae', float('inf')))
                    curr_mae = recent_performance[1].get('val_mae', recent_performance[1].get('mae', float('inf')))

                    # 如果性能有改善但学习率过低，触发恢复
                    if prev_mae > curr_mae and curr_mae != float('inf'):
                        self.logger.info(f"检测到学习率过低但性能改善，触发学习率恢复机制")
                        return True

        return False

    def _check_lr_trend_warning(self, old_lr: float, new_lr: float):
        """检查学习率趋势并发出警告"""
        # 记录学习率变化历史
        if not hasattr(self, '_lr_change_history'):
            self._lr_change_history = []

        # 记录变化方向 (1: 增加, -1: 减少, 0: 不变)
        if new_lr > old_lr:
            change_direction = 1
        elif new_lr < old_lr:
            change_direction = -1
        else:
            change_direction = 0

        self._lr_change_history.append(change_direction)

        # 只保留最近5次变化
        if len(self._lr_change_history) > 5:
            self._lr_change_history.pop(0)

        # 检查是否连续下降
        if len(self._lr_change_history) >= 3:
            recent_changes = self._lr_change_history[-3:]
            if all(change == -1 for change in recent_changes):
                self.logger.warning(
                    f"警告: 学习率连续3次下降，当前学习率={new_lr:.6f}，"
                    f"接近最小值={self.min_lr:.6f}，可能影响训练效果"
                )

        # 检查学习率是否接近边界
        if new_lr <= self.min_lr * 1.1:
            self.logger.warning(f"警告: 学习率接近最小值边界 {new_lr:.6f} <= {self.min_lr * 1.1:.6f}")
        elif new_lr >= self.max_lr * 0.9:
            self.logger.warning(f"警告: 学习率接近最大值边界 {new_lr:.6f} >= {self.max_lr * 0.9:.6f}")
