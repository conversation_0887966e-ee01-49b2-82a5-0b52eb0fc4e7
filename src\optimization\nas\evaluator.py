"""架构评估器模块 - 评估候选架构的性能指标

模块路径: src/optimization/nas/evaluator.py

功能说明：
1. 架构性能评估：MAE、训练稳定性、推理速度
2. 资源使用评估：参数量、显存占用、训练时间
3. 多目标优化：平衡精度与效率
4. 早停机制：快速筛选劣质架构
"""

from __future__ import annotations

import time
import traceback
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

import torch
import torch.nn as nn
from torch.utils.data import DataLoader

from src.models.gan.gan_model import GANModel
from src.models.gan.trainer import GANTrainer
from src.utils.config_manager import ConfigManager
from src.utils.cuda import cuda_manager
from src.utils.logger import get_logger

from .search_space import ArchitectureConfig
from .utils import ResourceMonitor


@dataclass
class ArchitectureMetrics:
    """架构评估指标"""
    # 主要指标 - 移除默认值，强制显式设置
    mae_score: float  # MAE分数 (越小越好) - 必须显式提供
    training_stability: float  # 训练稳定性 (0-1, 越大越好)

    # 辅助指标
    inference_speed: float  # 推理速度 (samples/sec)
    parameter_count: int  # 参数数量
    memory_usage_mb: float  # 显存使用量 (MB)
    training_time_minutes: float  # 训练时间 (分钟)

    # 复合指标
    efficiency_score: float = 0.0  # 效率分数 (综合考虑精度和效率)
    overall_score: float = 0.0  # 总体分数

    def __post_init__(self):
        """计算复合指标"""
        import math

        # 验证必要指标
        if not isinstance(self.mae_score, (int, float)):
            raise ValueError(f"mae_score必须是数值类型，当前类型: {type(self.mae_score)}")
        if self.mae_score < 0:
            raise ValueError(f"mae_score必须是非负数值，当前值: {self.mae_score}")
        if not math.isfinite(self.mae_score):
            raise ValueError(f"mae_score必须是有限数值，当前值: {self.mae_score}")

        if not isinstance(self.training_stability, (int, float)):
            raise ValueError(f"training_stability必须是数值类型，当前类型: {type(self.training_stability)}")
        if not (0 <= self.training_stability <= 1):
            raise ValueError(f"training_stability必须在[0,1]范围内，当前值: {self.training_stability}")

        self.efficiency_score = self._calculate_efficiency_score()
        self.overall_score = self._calculate_overall_score()

    def _calculate_efficiency_score(self) -> float:
        """计算效率分数"""
        if self.inference_speed <= 0:
            raise ValueError(f"inference_speed必须为正数，当前值: {self.inference_speed}")

        # 归一化MAE (假设好的MAE在0.01-0.1范围)
        normalized_mae = max(0, 1 - (self.mae_score - 0.01) / 0.09)

        # 归一化推理速度 (降低速度阈值要求)
        normalized_speed = min(1, max(0, (self.inference_speed - 50) / 950))

        # 加权组合
        return 0.7 * normalized_mae + 0.3 * normalized_speed

    def _calculate_overall_score(self) -> float:
        """计算总体分数"""
        # 调整权重分配
        mae_weight = 0.4
        stability_weight = 0.3
        efficiency_weight = 0.3

        # 归一化MAE (假设好的MAE在0.01-0.1范围)
        normalized_mae = max(0, 1 - (self.mae_score - 0.01) / 0.09)

        return (mae_weight * normalized_mae +
                stability_weight * self.training_stability +
                efficiency_weight * self.efficiency_score)


@dataclass
class EvaluationResult:
    """评估结果"""
    architecture: ArchitectureConfig
    metrics: ArchitectureMetrics
    success: bool = True
    error_message: str = ""
    evaluation_time: float = 0.0

    def is_better_than(self, other: 'EvaluationResult',
                      primary_metric: str = 'overall_score') -> bool:
        """比较两个评估结果"""
        if not self.success:
            return False
        if not other.success:
            return True

        self_value = getattr(self.metrics, primary_metric)
        other_value = getattr(other.metrics, primary_metric)

        # 对于MAE等越小越好的指标
        if primary_metric in ['mae_score', 'memory_usage_mb', 'training_time_minutes']:
            return self_value < other_value
        else:
            return self_value > other_value


class NASEvaluator:
    """神经架构搜索评估器"""

    def __init__(self,
                 config: ConfigManager,
                 train_loader: DataLoader,
                 val_loader: DataLoader,
                 max_epochs: int = 10,
                 early_stop_patience: int = 3,
                 memory_limit_mb: float = 2800,  # 留出200MB缓冲
                 target_standardizer=None):
        """
        Args:
            config: 基础配置管理器
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            max_epochs: 最大训练轮数
            early_stop_patience: 早停耐心值
            memory_limit_mb: 显存限制 (MB)
            target_standardizer: 目标标准化器 (用于反标准化MAE计算)
        """
        self.logger = get_logger("NASEvaluator")
        self.config = config
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.max_epochs = max_epochs
        self.early_stop_patience = early_stop_patience
        self.memory_limit_mb = memory_limit_mb
        self.target_standardizer = target_standardizer

        self.resource_monitor = ResourceMonitor()
        self.device = cuda_manager.device

        # 记录标准化器状态
        if self.target_standardizer is not None:
            self.logger.info("NAS评估器已设置target_standardizer，将同时计算标准化和反标准化MAE")
        else:
            self.logger.info("NAS评估器未设置target_standardizer，将只计算标准化空间MAE用于评分")

        self.logger.info(f"NAS评估器初始化完成 - 最大轮数: {max_epochs}, "
                        f"早停耐心: {early_stop_patience}, 显存限制: {memory_limit_mb}MB")

    def evaluate_architecture(self, architecture: ArchitectureConfig) -> EvaluationResult:
        """评估单个架构"""
        start_time = time.time()
        self.logger.info(f"开始评估架构: {architecture}")

        try:
            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # 创建模型
            model = self._create_model_from_architecture(architecture)
            if model is None:
                raise RuntimeError("模型创建失败")

            # 检查内存使用
            initial_memory = self.resource_monitor.get_gpu_memory_usage()
            if initial_memory > self.memory_limit_mb:
                raise RuntimeError(f"初始内存使用超限: {initial_memory:.1f}MB > {self.memory_limit_mb}MB")

            # 训练和评估
            metrics = self._train_and_evaluate(model, architecture)

            evaluation_time = time.time() - start_time
            self.logger.info(f"架构评估完成 - 总体分数: {metrics.overall_score:.4f}, "
                           f"MAE: {metrics.mae_score:.4f}, 耗时: {evaluation_time:.1f}秒")

            return EvaluationResult(
                architecture=architecture,
                metrics=metrics,
                success=True,
                evaluation_time=evaluation_time
            )

        except Exception as e:
            error_msg = f"评估架构时发生错误: {str(e)}"
            self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
            # 移除默认值回退，直接抛出异常暴露问题
            raise RuntimeError(f"架构评估失败: {error_msg}") from e
        finally:
            # 清理资源
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

    def _create_model_from_architecture(self, architecture: ArchitectureConfig) -> Optional[GANModel]:
        """根据架构配置创建模型"""
        try:
            # 创建临时配置
            temp_config = self._create_temp_config(architecture)

            # 获取特征维度
            feature_dim = self._get_feature_dim_from_data()

            # 创建模型
            model = GANModel(
                config=temp_config,
                feature_dim=feature_dim,
                window_size=self.config.data.window_size
            )

            # 将模型移动到正确的设备
            model = model.to(self.device)
            self.logger.debug(f"模型已移动到设备: {self.device}")

            # 统计参数数量
            param_count = sum(p.numel() for p in model.parameters())
            self.logger.debug(f"创建模型成功 - 参数数量: {param_count:,}")

            return model

        except Exception as e:
            self.logger.error(f"创建模型失败: {e}")
            return None

    def _create_temp_config(self, architecture: ArchitectureConfig) -> ConfigManager:
        """根据架构配置创建临时配置"""
        # 复制基础配置
        temp_config = ConfigManager.from_config(self.config)

        # 更新模型配置
        gen_config = architecture.generator
        disc_config = architecture.discriminator
        encoder_config = architecture.feature_encoder

        # 安全地更新配置属性
        try:
            temp_config.model.hidden_dim = gen_config.hidden_dim
            temp_config.model.dropout_rate = gen_config.dropout_rate
        except Exception as e:
            self.logger.warning(f"无法更新基础模型配置: {e}")

        # 更新生成器配置 - 使用字典方式
        try:
            if hasattr(temp_config.model, 'generator') and temp_config.model.generator:
                if isinstance(temp_config.model.generator, dict):
                    temp_config.model.generator['num_layers'] = gen_config.num_layers
                else:
                    temp_config.model.generator.num_layers = gen_config.num_layers
        except Exception as e:
            self.logger.warning(f"无法更新生成器配置: {e}")

        # 更新判别器配置 - 使用字典方式
        try:
            if hasattr(temp_config.model, 'discriminator') and temp_config.model.discriminator:
                if isinstance(temp_config.model.discriminator, dict):
                    temp_config.model.discriminator['hidden_dim'] = disc_config.hidden_dim
                    temp_config.model.discriminator['num_layers'] = disc_config.num_layers
                    temp_config.model.discriminator['enable_dynamic_fusion'] = disc_config.branch_config.get('trend_branch', True)
                else:
                    temp_config.model.discriminator.hidden_dim = disc_config.hidden_dim
                    temp_config.model.discriminator.num_layers = disc_config.num_layers
                    temp_config.model.discriminator.enable_dynamic_fusion = disc_config.branch_config.get('trend_branch', True)
        except Exception as e:
            self.logger.warning(f"无法更新判别器配置: {e}")

        # 更新注意力配置 - 使用字典方式
        try:
            if hasattr(temp_config.model, 'attention') and temp_config.model.attention:
                if isinstance(temp_config.model.attention, dict):
                    temp_config.model.attention['multi_head_num_heads'] = gen_config.num_attention_heads
                    temp_config.model.attention['multi_head_dropout'] = gen_config.dropout_rate
                else:
                    temp_config.model.attention.multi_head_num_heads = gen_config.num_attention_heads
                    temp_config.model.attention.multi_head_dropout = gen_config.dropout_rate
        except Exception as e:
            self.logger.warning(f"无法更新注意力配置: {e}")

        # 更新特征提取器配置 - 使用字典方式
        try:
            if hasattr(temp_config.model, 'feature_extractor') and temp_config.model.feature_extractor:
                if isinstance(temp_config.model.feature_extractor, dict):
                    temp_config.model.feature_extractor['msfe_num_scales'] = encoder_config.num_scales
                    temp_config.model.feature_extractor['msfe_hidden_dim'] = gen_config.hidden_dim

                    # 处理卷积核大小列表
                    if hasattr(encoder_config, 'conv_kernel_sizes') and encoder_config.conv_kernel_sizes:
                        temp_config.model.feature_extractor['msfe_kernel_sizes'] = encoder_config.conv_kernel_sizes
                else:
                    temp_config.model.feature_extractor.msfe_num_scales = encoder_config.num_scales
                    temp_config.model.feature_extractor.msfe_hidden_dim = gen_config.hidden_dim

                    # 处理卷积核大小列表
                    if hasattr(encoder_config, 'conv_kernel_sizes') and encoder_config.conv_kernel_sizes:
                        temp_config.model.feature_extractor.msfe_kernel_sizes = encoder_config.conv_kernel_sizes
        except Exception as e:
            self.logger.warning(f"无法更新特征提取器配置: {e}")
        return temp_config

    def _get_feature_dim_from_data(self) -> int:
        """从数据中获取特征维度"""
        try:
            batch = next(iter(self.train_loader))
            features = batch['features']
            return features.shape[-1]  # [batch, seq_len, feature_dim]
        except Exception as e:
            self.logger.error(f"无法从数据获取特征维度: {e}")
            raise RuntimeError(f"无法从数据获取特征维度: {e}") from e

    def _train_and_evaluate(self, model: GANModel, architecture: ArchitectureConfig) -> ArchitectureMetrics:
        """训练并评估模型"""
        training_start = time.time()

        # 简化训练过程 - 使用快速评估
        best_mae = self._fast_evaluation(model)
        mae_history = [best_mae]

        # 如果快速评估结果异常（MAE=0或>1.0），跳过完整训练
        if best_mae > 1.0 or best_mae == 0.0:
            self.logger.info(f"快速评估MAE异常: {best_mae:.4f}, 跳过完整训练")
        else:
            # 进行少量epoch的完整训练
            best_mae, mae_history = self._limited_training(model)

        training_time = time.time() - training_start

        # 计算指标
        metrics = self._calculate_metrics(
            model=model,
            best_mae=best_mae,
            mae_history=mae_history,
            training_time=training_time
        )

        return metrics

    def _fast_evaluation(self, model: GANModel) -> float:
        """快速评估模型性能 (使用反标准化数据)"""
        try:
            model.eval()
            total_mae = 0.0
            num_batches = 0
            max_batches = 5  # 只评估前5个批次

            with torch.no_grad():
                for i, batch in enumerate(self.val_loader):
                    if i >= max_batches:
                        break

                    features = batch['features'].to(self.device)
                    targets = batch['target'].to(self.device)

                    # 生成预测
                    predictions = model(features)

                    # 计算标准化空间的MAE (用于评分)
                    mae = torch.mean(torch.abs(predictions - targets)).item()
                    self.logger.debug(f"NAS快速评估 - 标准化空间MAE: {mae:.4f}")

                    # 同时计算反标准化MAE (用于日志记录和对比)
                    if hasattr(self, 'target_standardizer') and self.target_standardizer is not None:
                        try:
                            # 反标准化预测值
                            batch_size, seq_len, _ = predictions.shape
                            inv_predictions = self.target_standardizer.inverse_transform(
                                predictions.view(-1, 1).to(self.device),
                                is_normalized=True
                            ).view(batch_size, seq_len, -1)

                            # 反标准化目标值
                            inv_targets = self.target_standardizer.inverse_transform(
                                targets.view(-1, 1).to(self.device),
                                is_normalized=True
                            ).view(batch_size, seq_len, -1)

                            # 在反标准化空间计算MAE (仅用于日志)
                            inv_mae = torch.mean(torch.abs(inv_predictions - inv_targets)).item()
                            self.logger.debug(f"NAS快速评估 - 反标准化MAE: {inv_mae:.4f} (仅供参考)")
                        except Exception as e:
                            self.logger.warning(f"反标准化MAE计算失败: {e}")
                    else:
                        self.logger.debug("target_standardizer不可用，跳过反标准化MAE计算")

                    total_mae += mae
                    num_batches += 1

            avg_mae = total_mae / max(num_batches, 1)
            self.logger.debug(f"NAS快速评估完成 - 平均MAE: {avg_mae:.4f} (共{num_batches}个批次)")
            return avg_mae

        except Exception as e:
            self.logger.error(f"快速评估失败: {e}")
            raise RuntimeError(f"快速评估失败: {e}") from e

    def _limited_training(self, model: GANModel) -> Tuple[float, List[float]]:
        """有限轮数的训练"""
        try:
            from src.models.gan.trainer import GANTrainer

            # 创建临时训练器
            temp_trainer = GANTrainer(
                config=self.config,
                model=model,
                train_loader=self.train_loader,
                val_loader=self.val_loader,
                target_standardizer=self.target_standardizer
            )

            # 执行少量训练
            batch_size = getattr(self.train_loader, 'batch_size', 32)  # 提供默认值
            results = temp_trainer.run_training_loop(
                batch_size=batch_size,
                num_epochs=min(3, self.max_epochs)
            )

            # 提取MAE历史 - 修正键名
            mae_history = results.get('val_mae', [])
            if not mae_history:
                # 尝试其他可能的键名
                mae_history = results.get('val_mae_history', [])
            if not mae_history:
                self.logger.warning(f"训练结果中未找到MAE历史记录，可用键: {list(results.keys())}")
                raise RuntimeError("训练完成但未获得MAE历史记录")

            best_mae = min(mae_history)
            return best_mae, mae_history

        except Exception as e:
            self.logger.error(f"有限训练失败: {e}")
            raise RuntimeError(f"有限训练失败: {e}") from e

    def _calculate_metrics(self,
                          model: GANModel,
                          best_mae: float,
                          mae_history: List[float],
                          training_time: float) -> ArchitectureMetrics:
        """计算架构指标"""
        # 参数数量
        param_count = sum(p.numel() for p in model.parameters())

        # 内存使用
        memory_usage = self.resource_monitor.get_gpu_memory_usage()

        # 训练稳定性 (基于MAE历史的变异系数)
        stability = self._calculate_training_stability(mae_history)

        # 推理速度
        inference_speed = self._measure_inference_speed(model)

        return ArchitectureMetrics(
            mae_score=best_mae,
            training_stability=stability,
            inference_speed=inference_speed,
            parameter_count=param_count,
            memory_usage_mb=memory_usage,
            training_time_minutes=training_time / 60.0
        )

    def _calculate_training_stability(self, mae_history: List[float]) -> float:
        """计算训练稳定性"""
        if len(mae_history) < 2:
            return 0.5  # 默认中等稳定性

        import numpy as np
        mae_array = np.array(mae_history)

        # 计算变异系数 (标准差/均值)
        if mae_array.mean() == 0:
            return 0.0

        cv = mae_array.std() / mae_array.mean()

        # 转换为稳定性分数 (越小的变异系数表示越稳定)
        stability = max(0, 1 - cv)

        return stability

    def _measure_inference_speed(self, model: GANModel) -> float:
        """测量推理速度"""
        try:
            model.eval()

            # 准备测试数据
            batch = next(iter(self.val_loader))
            features = batch['features'][:1].to(self.device)  # 单样本

            # 预热
            with torch.no_grad():
                for _ in range(5):
                    _ = model(features)

            # 测量推理时间
            num_samples = 100
            start_time = time.time()

            with torch.no_grad():
                for _ in range(num_samples):
                    _ = model(features)

            if torch.cuda.is_available():
                torch.cuda.synchronize()

            total_time = time.time() - start_time
            speed = num_samples / total_time

            return speed

        except Exception as e:
            self.logger.error(f"测量推理速度失败: {e}")
            raise RuntimeError(f"测量推理速度失败: {e}") from e
