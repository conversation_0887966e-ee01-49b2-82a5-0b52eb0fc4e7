"""配置加载器 - 负责从YAML文件加载配置"""

from dataclasses import fields as dc_fields
from dataclasses import is_dataclass
from pathlib import Path
from typing import Any  # 添加更多类型

import yaml

from src.utils.config.batch_size_optimizer import (
    BatchSizeOptimizerConfig,  # Corrected import path
)
from src.utils.config.data import (
    BaseFeaturesConfig,
    CandidateSelectionConfig,  # 添加CandidateSelectionConfig
    DataConfig,
    DiffFeaturesConfig,
    FeatureEngineeringConfig,
    FeatureSelectionConfig,
    InteractionFeaturesConfig,  # 添加InteractionFeaturesConfig
    LayerConfig,  # 添加LayerConfig
    TimeSeriesFeaturesConfig,  # Added FeatureSelectionConfig
)
from src.utils.config.manager import ConfigManager
from src.utils.config.model import (  # Added SequenceStrategyConfig
    AttentionConfig,
    BaseModelConfig,
    FeatureExtractorConfig,  # 添加 FeatureExtractorConfig 导入
    GANModelConfig,
    SequenceStrategyConfig,
)
from src.utils.config.optimization import (  # Added OptimizationConfig and FastModeConfig imports
    OptimizationConfig,
)
from src.utils.config.paths import PathsConfig
from src.utils.config.prediction import (
    PredictionConfig,  # Added PredictionConfig import
)
from src.utils.config.system import LoggingConfig, SystemConfig
from src.utils.config.training import (
    BalanceConfig,
    CheckpointConfig,
    EarlyStoppingConfig,
    EvaluationConfig,
    LossConfig,
    LrBalancerConfig,
    TrainingConfig,  # Added LossConfig for clarity, though it might be transitively imported
)
from src.utils.config.monitoring import MonitoringConfig
from src.utils.logger import get_logger


class ConfigLoader:
    """配置加载工厂类"""

    @staticmethod
    @staticmethod
    def _recursive_create_dataclass(dc_type: type, data: dict[str, Any]) -> Any:
        """递归地从字典创建数据类实例，处理嵌套数据类"""
        logger = get_logger(__name__)

        # 特殊处理MixedPrecisionConfig
        if dc_type.__name__ == 'MixedPrecisionConfig':
            logger.debug(f"特殊处理MixedPrecisionConfig: {data}")
            try:
                # 确保所有必需字段都存在
                required_fields = ['enabled', 'dtype', 'init_scale', 'growth_factor', 'backoff_factor', 'growth_interval', 'cast_model_outputs']
                missing_fields = [field for field in required_fields if field not in data]
                if missing_fields:
                    error_msg = f"mixed_precision配置缺少必需字段: {', '.join(missing_fields)}"
                    logger.error(error_msg)
                    raise ValueError(error_msg)

                # 直接创建实例
                return dc_type(**data)
            except Exception as e:
                logger.error(f"创建MixedPrecisionConfig实例失败: {e!s}")
                raise

        # 获取数据类的字段
        fields = dc_fields(dc_type)
        kwargs = {}

        # 遍历字段
        for field in fields:
            name = field.name
            field_type = field.type
            value = data.get(name)

            # 如果字段不存在，跳过
            if name not in data:
                continue

            # 处理嵌套数据类
            if isinstance(field_type, type) and is_dataclass(field_type):
                if value is None:
                    kwargs[name] = None
                elif isinstance(value, dict):
                    kwargs[name] = ConfigLoader._recursive_create_dataclass(field_type, value)
                else:
                    raise TypeError(f"字段 '{name}' 必须是字典类型，但获取到的是 {type(value)}")
            else:
                kwargs[name] = value

        # 创建数据类实例
        try:
            return dc_type(**kwargs)
        except Exception as e:
            logger.error(f"创建{dc_type.__name__}实例失败: {e!s}")
            raise

    @staticmethod
    def load_from_yaml(yaml_path: str | Path) -> ConfigManager:
        """从YAML文件加载配置"""
        logger = get_logger(__name__) # Initialize logger for this method
        try:
            yaml_file_path = Path(yaml_path).resolve()
            base_dir = yaml_file_path.parent
            with open(yaml_file_path, encoding='utf-8') as f:
                config_data = yaml.safe_load(f) or {}

            # 验证必需字段
            if 'version' not in config_data:
                raise ValueError("配置文件中必须包含version字段")

            # 解析所有配置部分
            version = config_data['version']
            noise_dim, dimensions = ConfigLoader._get_model_base_params(config_data)

            # 实例化各配置部分
            paths = ConfigLoader._load_paths_config(config_data, noise_dim, dimensions, base_dir)
            system = ConfigLoader._load_system_config(config_data, noise_dim, dimensions)
            logging_config = ConfigLoader._load_logging_config(config_data, noise_dim, dimensions)
            model = ConfigLoader._load_model_config(config_data, noise_dim, dimensions)
            data = ConfigLoader._load_data_config(config_data, paths, noise_dim, dimensions)
            training = ConfigLoader._load_training_config(config_data, noise_dim, dimensions)
            feature_engineering = ConfigLoader._load_feature_engineering_config(config_data, noise_dim, dimensions)
            evaluation = ConfigLoader._load_evaluation_config(config_data, noise_dim, dimensions)
            # preprocessing = config_data.get('preprocessing', {}) # 移除默认值
            try:
                preprocessing = config_data['preprocessing'] # 强制要求 'preprocessing' 节存在
                if not isinstance(preprocessing, dict):
                     raise TypeError(f"'preprocessing' 必须是字典类型, 得到 {type(preprocessing)}")
            except KeyError:
                raise ValueError("配置文件中必须包含 'preprocessing' 部分")

            # 加载 prediction 配置
            prediction_data = config_data.get('prediction')
            if prediction_data is not None and isinstance(prediction_data, dict):
                # 确保嵌套配置也有必需的基础字段 (如果 PredictionConfig 继承自 BaseConfig)
                if 'noise_dim' not in prediction_data: # Assuming PredictionConfig needs these if it inherits BaseConfig
                    prediction_data['noise_dim'] = noise_dim
                if 'dimensions' not in prediction_data:
                    prediction_data['dimensions'] = dimensions

                # 处理device字段，确保它是str类型或None
                if 'device' in prediction_data:
                    device_value = prediction_data['device']
                    # 如果device是${system.device}形式的变量引用，则替换为实际值
                    if isinstance(device_value, str) and device_value.startswith('${') and device_value.endswith('}'):
                        var_path = device_value[2:-1].split('.')
                        if len(var_path) == 2 and var_path[0] == 'system' and var_path[1] == 'device':
                            # 从system配置中获取device值
                            prediction_data['device'] = system.device
                            logger.debug(f"将prediction.device从'{device_value}'替换为'{system.device}'")

                # 直接创建PredictionConfig实例，而不是使用_recursive_create_dataclass
                try:
                    # 过滤出PredictionConfig所需的字段
                    valid_fields = {f.name for f in dc_fields(PredictionConfig)}
                    filtered_config = {k: v for k, v in prediction_data.items() if k in valid_fields}
                    prediction_config_instance = PredictionConfig(**filtered_config)
                    logger.info(f"成功加载并创建 PredictionConfig 实例: {prediction_config_instance}")
                except Exception as e:
                    logger.error(f"直接创建 PredictionConfig 实例失败: {e!s}")
                    # 移除回退逻辑，直接抛出异常
                    raise ValueError(f"创建 PredictionConfig 实例失败: {e!s}") from e
            elif prediction_data is None:
                 logger.error("配置文件中缺少必需的 'prediction' 部分。")
                 raise ValueError("配置文件中缺少必需的 'prediction' 部分。")
            else:
                logger.error(f"配置文件中 'prediction' 部分格式不正确（不是字典），得到: {type(prediction_data)}")
                raise ValueError("配置文件中 'prediction' 部分格式不正确。")

            # 加载特征选择配置
            feature_selection_data = config_data.get('feature_selection')
            if feature_selection_data is not None and isinstance(feature_selection_data, dict):
                # 注入从 BaseConfig 继承的必需字段 (noise_dim, dimensions)
                # 到 feature_selection_data 及其嵌套的子配置字典中
                # 以确保 _recursive_create_dataclass 不会因为缺少这些字段而失败

                # 为 FeatureSelectionConfig 注入
                if 'noise_dim' not in feature_selection_data:
                    feature_selection_data['noise_dim'] = noise_dim
                if 'dimensions' not in feature_selection_data:
                    feature_selection_data['dimensions'] = dimensions

                # 为 LaggedCorrConfig (如果存在) 注入并转换为对象
                if 'lagged_corr' in feature_selection_data and isinstance(feature_selection_data['lagged_corr'], dict):
                    if 'noise_dim' not in feature_selection_data['lagged_corr']:
                        feature_selection_data['lagged_corr']['noise_dim'] = noise_dim
                    if 'dimensions' not in feature_selection_data['lagged_corr']:
                        feature_selection_data['lagged_corr']['dimensions'] = dimensions

                    # 导入 LaggedCorrConfig 类
                    from src.utils.config.data import LaggedCorrConfig

                    # 将字典转换为 LaggedCorrConfig 对象
                    feature_selection_data['lagged_corr'] = ConfigLoader._recursive_create_dataclass(
                        LaggedCorrConfig,
                        feature_selection_data['lagged_corr']
                    )

                # 为 NoiseDetectionConfig (如果存在) 注入并转换为对象
                if 'noise_detection' in feature_selection_data and isinstance(feature_selection_data['noise_detection'], dict):
                    if 'noise_dim' not in feature_selection_data['noise_detection']:
                        feature_selection_data['noise_detection']['noise_dim'] = noise_dim
                    if 'dimensions' not in feature_selection_data['noise_detection']:
                        feature_selection_data['noise_detection']['dimensions'] = dimensions

                    # 导入 NoiseDetectionConfig 类
                    from src.utils.config.data import NoiseDetectionConfig

                    # 将字典转换为 NoiseDetectionConfig 对象
                    feature_selection_data['noise_detection'] = ConfigLoader._recursive_create_dataclass(
                        NoiseDetectionConfig,
                        feature_selection_data['noise_detection']
                    )

                # 直接创建FeatureSelectionConfig实例，不允许任何回退
                # 过滤出FeatureSelectionConfig所需的字段
                valid_fields = {f.name for f in dc_fields(FeatureSelectionConfig)}
                filtered_config = {k: v for k, v in feature_selection_data.items() if k in valid_fields}
                feature_selection = FeatureSelectionConfig(**filtered_config)
                logger.info(f"成功加载并创建 FeatureSelectionConfig 实例: {feature_selection}")
            elif feature_selection_data is None:
                # 如果 YAML 中没有 feature_selection，这是配置错误
                error_msg = "配置文件中缺少必需的 'feature_selection' 部分"
                logger.error(error_msg)
                raise ValueError(error_msg)
            else:
                 # 如果 feature_selection_data 不是字典，则格式不正确
                 logger.error(f"配置文件中 'feature_selection' 部分格式不正确（不是字典或 None），得到: {type(feature_selection_data)}")
                 raise ValueError("配置文件中 'feature_selection' 部分格式不正确。")

            # 加载 optimization 配置
            optimization_config_instance = None
            optimization_data = config_data.get('optimization')
            if optimization_data is not None and isinstance(optimization_data, dict):
                # 注入必需的基础字段 (如果 OptimizationConfig 或其子类继承自 BaseConfig)
                # 当前 OptimizationConfig 和 FastModeConfig 并不直接继承 BaseConfig，所以不需要注入 noise_dim 和 dimensions
                # 但如果将来它们改变了继承关系，这里需要相应调整

                # 导入 FastModeConfig 类
                from src.utils.config.optimization import FastModeConfig

                fast_mode_instance = None
                fast_mode_data = optimization_data.get('fast_mode')
                if fast_mode_data is not None and isinstance(fast_mode_data, dict):
                    # FastModeConfig 也不继承 BaseConfig
                    fast_mode_instance = ConfigLoader._recursive_create_dataclass(FastModeConfig, fast_mode_data)
                    logger.info(f"成功加载并创建 FastModeConfig 实例: {fast_mode_instance}")

                # 创建 OptimizationConfig 实例，传入可能已创建的 fast_mode_instance
                # _recursive_create_dataclass 会处理 Optional[FastModeConfig]
                # 我们需要确保 optimization_data 传递给 _recursive_create_dataclass 时，
                # 'fast_mode' 键对应的是 fast_mode_instance (如果已创建) 或原始数据 (如果未创建或为 None)

                # 处理 parameter_exploration 配置
                parameter_exploration_instance = None
                parameter_exploration_data = optimization_data.get('parameter_exploration')
                if parameter_exploration_data is not None and isinstance(parameter_exploration_data, dict):
                    # 导入 ParameterExplorationModeConfig 类
                    from src.utils.config.optimization import ParameterExplorationModeConfig

                    # 递归创建 ParameterExplorationModeConfig 实例
                    parameter_exploration_instance = ConfigLoader._recursive_create_dataclass(
                        ParameterExplorationModeConfig,
                        parameter_exploration_data
                    )
                    logger.info(f"成功加载并创建 ParameterExplorationModeConfig 实例: {parameter_exploration_instance}")

                # 检查 fast_mode_instance 是否为 None，如果是，则抛出错误，因为它是 OptimizationConfig 的必需字段
                if fast_mode_instance is None:
                    logger.error("optimization.fast_mode 配置缺失或无效，无法创建 OptimizationConfig")
                    raise ValueError("optimization.fast_mode 配置缺失或无效")

                # 直接创建OptimizationConfig实例，不允许任何回退
                # 过滤出OptimizationConfig所需的字段
                valid_fields = {f.name for f in dc_fields(OptimizationConfig)}
                filtered_config = {}

                # 设置fast_mode实例 (现在一定不为None)
                filtered_config['fast_mode'] = fast_mode_instance

                # 设置parameter_exploration实例 (如果是 Optional)
                if parameter_exploration_instance is not None:
                    filtered_config['parameter_exploration'] = parameter_exploration_instance
                elif 'parameter_exploration' in valid_fields: # 如果 parameter_exploration 不是 Optional 但缺失
                     raise ValueError("OptimizationConfig 需要 'parameter_exploration' 但配置中未提供或无效")

                # 设置 optimization_start_date (如果是 Optional)
                if 'optimization_start_date' in optimization_data:
                     filtered_config['optimization_start_date'] = optimization_data['optimization_start_date']

                # 创建OptimizationConfig实例
                optimization_config_instance = OptimizationConfig(**filtered_config)
                logger.info(f"成功加载并创建 OptimizationConfig 实例: {optimization_config_instance}")
            elif optimization_data is None:
                 logger.info("配置文件中未找到 'optimization' 部分，将使用 None。")
                 optimization_config_instance = None # 显式设置为 None
            else:
                logger.error("配置文件中 'optimization' 部分格式不正确 (不是字典或 None)。")
                raise ValueError("配置文件中 'optimization' 部分格式不正确。")

            # 加载 monitoring 配置
            monitoring_config_instance = None
            monitoring_data = config_data.get('monitoring')
            if monitoring_data is not None and isinstance(monitoring_data, dict):
                # 直接创建MonitoringConfig实例
                try:
                    # 过滤出MonitoringConfig所需的字段
                    valid_fields = {f.name for f in dc_fields(MonitoringConfig)}
                    filtered_config = {k: v for k, v in monitoring_data.items() if k in valid_fields}
                    monitoring_config_instance = MonitoringConfig(**filtered_config)
                    logger.info(f"成功加载并创建 MonitoringConfig 实例: {monitoring_config_instance}")
                except Exception as e:
                    logger.error(f"直接创建 MonitoringConfig 实例失败: {e!s}")
                    raise ValueError(f"创建 MonitoringConfig 实例失败: {e!s}") from e
            elif monitoring_data is None:
                logger.info("配置文件中未找到 'monitoring' 部分，将使用 None。")
                monitoring_config_instance = None # 显式设置为 None
            else:
                logger.error("配置文件中 'monitoring' 部分格式不正确 (不是字典或 None)。")
                raise ValueError("配置文件中 'monitoring' 部分格式不正确。")

            # 加载NAS配置
            nas_config_instance = ConfigLoader._load_nas_config(config_data)

            # 创建配置管理器实例
            config_kwargs = {
                "version": version,
                "paths": paths,
                "system": system,
                "logging": logging_config,
                "data": data,
                "training": training,
                "model": model,
                "feature_engineering": feature_engineering,
                "evaluation": evaluation,
                "preprocessing": preprocessing,
                "feature_selection": feature_selection, # Added feature_selection
                "prediction": prediction_config_instance, # Added prediction
                "optimization": optimization_config_instance, # Added optimization
                "monitoring": monitoring_config_instance, # Added monitoring
                "nas": nas_config_instance # Added NAS
            }
            # DEBUG LOGGING before ConfigManager instantiation
            opt_val = config_kwargs.get("optimization")
            logger.debug(f"Before ConfigManager instantiation, optimization value in kwargs: {opt_val}, type: {type(opt_val)}")
            return ConfigManager(**config_kwargs)
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"加载配置文件失败: {e!s}")
            raise

    @staticmethod
    def _load_nas_config(config_data: dict[str, Any]) -> 'NASConfig | None':
        """加载NAS配置"""
        try:
            from .nas import NASConfig, SearchSpaceConfig, NASManagerConfig
        except ImportError as e:
            logger = get_logger(__name__)
            logger.error(f"无法导入NAS配置类: {e}")
            return None
        logger = get_logger(__name__)

        if 'nas' not in config_data:
            logger.info("配置文件中未找到 'nas' 部分，将使用 None")
            return None

        nas_data = config_data['nas']
        if not isinstance(nas_data, dict):
            raise ValueError("配置文件中 'nas' 部分必须是字典类型")

        logger.info("加载NAS配置")

        # 加载搜索空间配置 - 必须存在
        if 'search_space' not in nas_data:
            raise ValueError("NAS配置中必须包含 'search_space' 部分")

        search_space_data = nas_data['search_space']
        if not isinstance(search_space_data, dict):
            raise ValueError("NAS配置中 'search_space' 部分必须是字典类型")

        # 验证搜索空间必需字段
        required_search_space_fields = {
            'generator_num_layers_range', 'generator_hidden_dim_range', 'generator_num_heads_range',
            'generator_dropout_range', 'generator_activation_types',
            'discriminator_num_layers_range', 'discriminator_hidden_dim_range',
            'feature_encoder_num_scales_range', 'feature_encoder_kernel_size_options',
            'feature_encoder_temporal_window_options', 'feature_encoder_amplification_factor_range',
            'feature_encoder_fusion_types',
            'training_lr_range', 'training_optimizer_types', 'training_lr_scheduler_types',
            'training_n_critic_range', 'training_g_steps_range',
            'loss_adversarial_weight_range', 'loss_feature_matching_weight_range',
            'loss_temporal_consistency_weight_range', 'loss_gradient_penalty_lambda_range',
            'loss_gradient_penalty_target_range', 'loss_types', 'loss_label_smoothing_range',
            'loss_loss_clipping_range',
            'regularization_dropout_range', 'regularization_weight_decay_range',
            'regularization_gradient_clipping_range', 'regularization_normalization_types',
            'regularization_noise_std_range', 'regularization_input_noise_range'
        }

        missing_fields = required_search_space_fields - set(search_space_data.keys())
        if missing_fields:
            raise ValueError(f"搜索空间配置缺少必需字段: {', '.join(sorted(missing_fields))}")

        # 创建搜索空间配置
        search_space_config = SearchSpaceConfig(**search_space_data)

        # 加载管理器配置 - 必须存在的字段
        required_manager_fields = {
            'search_strategy', 'max_iterations', 'time_budget_hours',
            'max_epochs_per_eval', 'early_stop_patience', 'memory_limit_mb',
            'population_size', 'mutation_rate', 'crossover_rate',
            'darts_learning_rate', 'darts_momentum',
            'gpu_memory_limit_gb', 'enable_distributed', 'num_workers',
            'log_dir', 'save_dir', 'experiment_name', 'save_intermediate_results'
        }

        missing_manager_fields = required_manager_fields - set(nas_data.keys())
        if missing_manager_fields:
            raise ValueError(f"NAS管理器配置缺少必需字段: {', '.join(sorted(missing_manager_fields))}")

        # 提取管理器配置数据
        manager_data = {k: v for k, v in nas_data.items() if k in required_manager_fields}
        manager_config = NASManagerConfig(**manager_data)

        return NASConfig(
            search_space=search_space_config,
            manager=manager_config
        )

    @staticmethod
    def _get_model_base_params(config_data: dict[str, Any]) -> tuple[int, dict[str, Any]]:
        """获取模型基础参数(noise_dim和dimensions)"""
        try:
            model_config = config_data['model'] # 强制要求 'model' 节存在
        except KeyError:
            raise ValueError("配置文件中必须包含 'model' 部分")
        try:
            noise_dim = model_config['noise_dim']
        except KeyError:
            raise ValueError("配置文件 'model' 部分必须包含 'noise_dim' 字段")
        dimensions = model_config['dimensions'] # 强制要求 'dimensions' 存在
        return noise_dim, dimensions

    @staticmethod
    def _load_paths_config(config_data: dict[str, Any], noise_dim: int, dimensions: dict[str, Any], base_dir: Path) -> PathsConfig:
        """加载路径配置"""
        # 使用noise_dim和dimensions参数，避免未使用警告
        logger = get_logger(__name__)
        logger.debug(f"加载路径配置，noise_dim={noise_dim}, dimensions={dimensions}")

        try:
            paths_config_data = config_data['paths'] # 强制要求 'paths' 节存在
        except KeyError:
            raise ValueError("配置文件中必须包含 'paths' 部分")
        # valid_fields should only contain fields defined in PathsConfig that are expected from the yaml 'paths' section
        # _base_dir is passed explicitly. noise_dim and dimensions are not part of PathsConfig.
        path_field_names = {f.name for f in dc_fields(PathsConfig) if f.name != '_base_dir'}
        filtered_config = {k: v for k, v in paths_config_data.items() if k in path_field_names}

        return PathsConfig(_base_dir=base_dir, **filtered_config)

    @staticmethod
    def _load_system_config(config_data: dict[str, Any], noise_dim: int, dimensions: dict[str, Any]) -> SystemConfig:
        """加载系统配置"""
        # 使用noise_dim和dimensions参数，避免未使用警告
        logger = get_logger(__name__)
        logger.debug(f"加载系统配置，noise_dim={noise_dim}, dimensions={dimensions}")

        try:
            system_config = config_data['system'] # 强制要求 'system' 节存在
        except KeyError:
            raise ValueError("配置文件中必须包含 'system' 部分")
        valid_fields = {f.name for f in dc_fields(SystemConfig)}
        filtered_config = {k: v for k, v in system_config.items() if k in valid_fields}
        return SystemConfig(**filtered_config)

    @staticmethod
    def _load_logging_config(config_data: dict[str, Any], noise_dim: int, dimensions: dict[str, Any]) -> LoggingConfig:
        """加载日志配置"""
        # 使用noise_dim和dimensions参数，避免未使用警告
        logger = get_logger(__name__)
        logger.debug(f"加载日志配置，noise_dim={noise_dim}, dimensions={dimensions}")

        try:
            logging_config = config_data['logging'] # 强制要求 'logging' 节存在
        except KeyError:
            raise ValueError("配置文件中必须包含 'logging' 部分")
        valid_fields = {f.name for f in dc_fields(LoggingConfig)}
        filtered_config = {k: v for k, v in logging_config.items() if k in valid_fields}
        return LoggingConfig(**filtered_config)

    @staticmethod
    def _load_model_config(config_data: dict[str, Any], noise_dim: int, dimensions: dict[str, Any]) -> BaseModelConfig | GANModelConfig:
        """加载模型配置"""
        logger = get_logger(__name__) # 添加 logger 初始化
        try:
            model_config = config_data['model'] # 强制要求 'model' 节存在
        except KeyError:
            raise ValueError("配置文件中必须包含 'model' 部分")
        # 强制要求 model.type 必须在配置中指定
        try:
            model_type = model_config['type'].lower()
        except KeyError:
            raise ValueError("配置文件 'model' 部分必须包含 'type' 字段")

        if model_type == 'gan':
            # Prepare arguments for GANModelConfig, handling nested dataclasses
            gan_kwargs = {k: v for k, v in model_config.items() if k in {f.name for f in dc_fields(GANModelConfig)}}

            if 'loss' in gan_kwargs and isinstance(gan_kwargs['loss'], dict):
                gan_kwargs['loss'] = ConfigLoader._recursive_create_dataclass(LossConfig, gan_kwargs['loss'])

            if 'sequence_strategy' in gan_kwargs and isinstance(gan_kwargs['sequence_strategy'], dict):
                gan_kwargs['sequence_strategy'] = ConfigLoader._recursive_create_dataclass(SequenceStrategyConfig, gan_kwargs['sequence_strategy'])

            # --- BEGIN MODIFICATION FOR NoiseParamsConfig ---
            if 'noise' in gan_kwargs and isinstance(gan_kwargs['noise'], dict):
                noise_data = gan_kwargs['noise']
                # Import NoiseParamsConfig
                from src.utils.config.model import NoiseParamsConfig  # 在方法内部导入

                required_noise_fields = [
                    'dim', 'distribution', 'scale', 'seed', 'dtype',
                    'structured', 'temporal_correlation', 'feature_correlation',
                    'noise_patterns'
                ]
                missing_fields = [field for field in required_noise_fields if field not in noise_data]
                if missing_fields:
                    error_msg = (f"GAN model 'noise' configuration is missing "
                                 f"required fields: {', '.join(missing_fields)}")
                    logger.error(error_msg)
                    raise ValueError(error_msg)

                # Create NoiseParamsConfig instance
                try:
                    # 过滤出NoiseParamsConfig所需的字段，以避免未预期的参数错误
                    valid_noise_param_fields = {f.name for f in dc_fields(NoiseParamsConfig)}
                    filtered_noise_data = {k: v for k, v in noise_data.items() if k in valid_noise_param_fields}
                    gan_kwargs['noise'] = NoiseParamsConfig(**filtered_noise_data)
                    logger.debug(f"Successfully created NoiseParamsConfig instance: {gan_kwargs['noise']}")
                except Exception as e:
                    logger.error(f"Failed to create NoiseParamsConfig instance: {e!s}")
                    raise ValueError(f"Failed to create NoiseParamsConfig instance from 'noise' config: {e!s}") from e
            elif 'noise' in gan_kwargs: # noise 键存在但不是字典
                 error_msg = f"GAN model 'noise' configuration must be a dictionary, but got {type(gan_kwargs['noise'])}"
                 logger.error(error_msg)
                 raise TypeError(error_msg)
            else: # noise 键缺失，并且它对于 GANModelConfig 是必需的
                error_msg = "GAN model configuration is missing the 'noise' section."
                logger.error(error_msg)
                raise ValueError(error_msg)
            # --- END MODIFICATION FOR NoiseParamsConfig ---

            # --- BEGIN MODIFICATION FOR AttentionConfig ---
            if 'attention' in gan_kwargs and isinstance(gan_kwargs['attention'], dict):
                attention_data = gan_kwargs['attention']
                # AttentionConfig should already be imported at the top of the file

                required_attention_fields = [
                    'multi_head_num_heads', 'multi_head_dropout',
                    'multi_scale_num_heads', 'multi_scale_num_scales',
                    'multi_scale_dropout', 'multi_scale_dilation_rates',
                    'temporal_wrapper_dropout', 'adaptive_attention_num_scales',
                    'adaptive_attention_dropout', 'adaptive_attention_num_heads'
                ]
                missing_fields = [field for field in required_attention_fields if field not in attention_data]
                if missing_fields:
                    error_msg = (f"GAN model 'attention' configuration is missing "
                                 f"required fields: {', '.join(missing_fields)}")
                    logger.error(error_msg)
                    raise ValueError(error_msg)

                try:
                    # Filter for fields defined in AttentionConfig to avoid unexpected argument errors
                    valid_attention_param_fields = {f.name for f in dc_fields(AttentionConfig)}
                    filtered_attention_data = {k: v for k, v in attention_data.items() if k in valid_attention_param_fields}

                    # Ensure correct data types before instantiation if necessary
                    # For example, dilation_rates should be List[int]
                    if 'dilation_rates' in filtered_attention_data and not isinstance(filtered_attention_data['dilation_rates'], list):
                        raise TypeError(f"AttentionConfig field 'dilation_rates' must be a list, got {type(filtered_attention_data['dilation_rates'])}")
                    if 'num_heads' in filtered_attention_data and not isinstance(filtered_attention_data['num_heads'], int):
                        raise TypeError(f"AttentionConfig field 'num_heads' must be an int, got {type(filtered_attention_data['num_heads'])}")
                    if 'num_scales' in filtered_attention_data and not isinstance(filtered_attention_data['num_scales'], int):
                        raise TypeError(f"AttentionConfig field 'num_scales' must be an int, got {type(filtered_attention_data['num_scales'])}")
                    if 'dropout' in filtered_attention_data:
                        if isinstance(filtered_attention_data['dropout'], int):
                            filtered_attention_data['dropout'] = float(filtered_attention_data['dropout'])
                        elif not isinstance(filtered_attention_data['dropout'], float):
                            raise TypeError(f"AttentionConfig field 'dropout' must be a float, got {type(filtered_attention_data['dropout'])}")

                    # Validate and type check new fields
                    if 'temporal_wrapper_dropout' in filtered_attention_data:
                        if isinstance(filtered_attention_data['temporal_wrapper_dropout'], int):
                            filtered_attention_data['temporal_wrapper_dropout'] = float(filtered_attention_data['temporal_wrapper_dropout'])
                        elif not isinstance(filtered_attention_data['temporal_wrapper_dropout'], float):
                            raise TypeError(f"AttentionConfig field 'temporal_wrapper_dropout' must be a float, got {type(filtered_attention_data['temporal_wrapper_dropout'])}")

                    if 'adaptive_attention_num_scales' in filtered_attention_data and not isinstance(filtered_attention_data['adaptive_attention_num_scales'], int):
                        raise TypeError(f"AttentionConfig field 'adaptive_attention_num_scales' must be an int, got {type(filtered_attention_data['adaptive_attention_num_scales'])}")

                    if 'adaptive_attention_dropout' in filtered_attention_data:
                        if isinstance(filtered_attention_data['adaptive_attention_dropout'], int):
                            filtered_attention_data['adaptive_attention_dropout'] = float(filtered_attention_data['adaptive_attention_dropout'])
                        elif not isinstance(filtered_attention_data['adaptive_attention_dropout'], float):
                            raise TypeError(f"AttentionConfig field 'adaptive_attention_dropout' must be a float, got {type(filtered_attention_data['adaptive_attention_dropout'])}")

                    if 'adaptive_attention_num_heads' in filtered_attention_data and not isinstance(filtered_attention_data['adaptive_attention_num_heads'], int):
                        raise TypeError(f"AttentionConfig field 'adaptive_attention_num_heads' must be an int, got {type(filtered_attention_data['adaptive_attention_num_heads'])}")

                    gan_kwargs['attention'] = AttentionConfig(**filtered_attention_data)
                    logger.debug(f"Successfully created AttentionConfig instance: {gan_kwargs['attention']}")
                except Exception as e:
                    logger.error(f"Failed to create AttentionConfig instance: {e!s}")
                    raise ValueError(f"Failed to create AttentionConfig instance from 'attention' config: {e!s}") from e
            elif 'attention' in gan_kwargs: # attention key exists but is not a dictionary
                 error_msg = f"GAN model 'attention' configuration must be a dictionary, but got {type(gan_kwargs['attention'])}"
                 logger.error(error_msg)
                 raise TypeError(error_msg)
            else: # attention key is missing, and it is required for GANModelConfig
                error_msg = "GAN model configuration is missing the 'attention' section."
                logger.error(error_msg)
                raise ValueError(error_msg)
            # --- END MODIFICATION FOR AttentionConfig ---

            # --- BEGIN MODIFICATION FOR FeatureExtractorConfig ---
            if 'feature_extractor' in gan_kwargs and isinstance(gan_kwargs['feature_extractor'], dict):
                fe_data = gan_kwargs['feature_extractor']
                logger.debug(f"处理 feature_extractor 配置: {fe_data}")

                required_fe_fields = {
                    'msfe_num_scales', 'msfe_dropout', 'msfe_kernel_sizes',
                    'tsfe_num_layers', 'tsfe_dropout', 'tsfe_hidden_dim',
                    'msfe_input_dim', 'msfe_hidden_dim', 'tsfe_input_dim', 'tsfe_output_dim' # 新增字段
                }
                missing_fe_fields = [field for field in required_fe_fields if field not in fe_data]
                if missing_fe_fields:
                    error_msg = (f"GAN model 'feature_extractor' configuration is missing "
                                 f"required fields: {', '.join(missing_fe_fields)}")
                    logger.error(error_msg)
                    raise ValueError(error_msg)

                # 类型检查和转换 (示例)
                try:
                    msfe_num_scales = int(fe_data['msfe_num_scales'])
                    msfe_dropout = float(fe_data['msfe_dropout'])
                    msfe_kernel_sizes = [int(k) for k in fe_data['msfe_kernel_sizes']]
                    tsfe_num_layers = int(fe_data['tsfe_num_layers'])
                    tsfe_dropout = float(fe_data['tsfe_dropout'])
                    tsfe_hidden_dim = int(fe_data['tsfe_hidden_dim'])
                    msfe_input_dim = int(fe_data['msfe_input_dim'])    # 新增
                    msfe_hidden_dim = int(fe_data['msfe_hidden_dim'])   # 新增
                    tsfe_input_dim = int(fe_data['tsfe_input_dim'])     # 新增
                    tsfe_output_dim = int(fe_data['tsfe_output_dim'])    # 新增
                except (ValueError, TypeError) as e:
                    logger.error(f"FeatureExtractorConfig 字段类型转换错误: {e!s}")
                    raise ValueError(f"FeatureExtractorConfig 字段类型错误: {e!s}") from e

                # 过滤出FeatureExtractorConfig所需的字段
                valid_fe_param_fields = {f.name for f in dc_fields(FeatureExtractorConfig)}
                filtered_fe_data = {
                    'msfe_num_scales': msfe_num_scales,
                    'msfe_dropout': msfe_dropout,
                    'msfe_kernel_sizes': msfe_kernel_sizes,
                    'tsfe_num_layers': tsfe_num_layers,
                    'tsfe_dropout': tsfe_dropout,
                    'tsfe_hidden_dim': tsfe_hidden_dim,
                    'msfe_input_dim': msfe_input_dim,        # 新增
                    'msfe_hidden_dim': msfe_hidden_dim,       # 新增
                    'tsfe_input_dim': tsfe_input_dim,         # 新增
                    'tsfe_output_dim': tsfe_output_dim        # 新增
                }
                # 确保只传递在 FeatureExtractorConfig 中定义的字段
                final_filtered_fe_data = {k: v for k, v in filtered_fe_data.items() if k in valid_fe_param_fields}

                gan_kwargs['feature_extractor'] = FeatureExtractorConfig(**final_filtered_fe_data)
                logger.debug(f"成功创建 FeatureExtractorConfig 实例: {gan_kwargs['feature_extractor']}")
            elif 'feature_extractor' in gan_kwargs: # feature_extractor 键存在但不是字典
                error_msg = f"GAN model 'feature_extractor' configuration must be a dictionary, but got {type(gan_kwargs['feature_extractor'])}"
                logger.error(error_msg)
                raise TypeError(error_msg)
            else: # feature_extractor 键缺失，并且它对于 GANModelConfig 是必需的
                error_msg = "GAN model configuration is missing the 'feature_extractor' section."
                logger.error(error_msg)
                raise ValueError(error_msg)
            # --- END MODIFICATION FOR FeatureExtractorConfig ---

            # --- BEGIN MODIFICATION FOR GeneratorConfig ---
            # 修复：处理generator配置
            if 'generator' in gan_kwargs and isinstance(gan_kwargs['generator'], dict):
                # 导入生成器配置类
                from src.utils.config.model import GeneratorConfig
                generator_data = gan_kwargs['generator']
                logger.debug(f"处理 generator 配置: {generator_data}")

                # 创建GeneratorConfig实例
                try:
                    # 过滤出GeneratorConfig所需的字段
                    valid_generator_fields = {f.name for f in dc_fields(GeneratorConfig)}
                    filtered_generator_data = {k: v for k, v in generator_data.items() if k in valid_generator_fields}
                    gan_kwargs['generator'] = ConfigLoader._recursive_create_dataclass(GeneratorConfig, filtered_generator_data)
                    logger.debug(f"成功创建 GeneratorConfig 实例: {gan_kwargs['generator']}")
                except Exception as e:
                    logger.error(f"创建GeneratorConfig实例失败: {e!s}")
                    raise ValueError(f"创建GeneratorConfig实例失败: {e!s}") from e
            elif 'generator' in gan_kwargs and gan_kwargs['generator'] is not None:
                error_msg = f"GAN model 'generator' configuration must be a dictionary, but got {type(gan_kwargs['generator'])}"
                logger.error(error_msg)
                raise ValueError(error_msg)
            # --- END MODIFICATION FOR GeneratorConfig ---

            # --- BEGIN MODIFICATION FOR DiscriminatorConfig ---
            # 修复：处理discriminator配置
            if 'discriminator' in gan_kwargs and isinstance(gan_kwargs['discriminator'], dict):
                # 导入判别器配置类
                from src.utils.config.model import DiscriminatorConfig
                discriminator_data = gan_kwargs['discriminator']
                logger.debug(f"处理 discriminator 配置: {discriminator_data}")

                # 创建DiscriminatorConfig实例
                try:
                    # 过滤出DiscriminatorConfig所需的字段
                    valid_discriminator_fields = {f.name for f in dc_fields(DiscriminatorConfig)}
                    filtered_discriminator_data = {k: v for k, v in discriminator_data.items() if k in valid_discriminator_fields}
                    gan_kwargs['discriminator'] = ConfigLoader._recursive_create_dataclass(DiscriminatorConfig, filtered_discriminator_data)
                    logger.debug(f"成功创建 DiscriminatorConfig 实例: {gan_kwargs['discriminator']}")
                except Exception as e:
                    logger.error(f"创建DiscriminatorConfig实例失败: {e!s}")
                    raise ValueError(f"创建DiscriminatorConfig实例失败: {e!s}") from e
            elif 'discriminator' in gan_kwargs and gan_kwargs['discriminator'] is not None:
                error_msg = f"GAN model 'discriminator' configuration must be a dictionary, but got {type(gan_kwargs['discriminator'])}"
                logger.error(error_msg)
                raise ValueError(error_msg)
            # --- END MODIFICATION FOR DiscriminatorConfig ---

            # Ensure all required fields are passed to GANModelConfig
            # Get mixed_precision from training config
            if 'training' not in config_data or 'mixed_precision' not in config_data['training']:
                raise ValueError("配置文件必须包含 training.mixed_precision 配置")

            gan_kwargs['noise_dim'] = noise_dim
            gan_kwargs['dimensions'] = dimensions
            gan_kwargs['mixed_precision'] = config_data['training']['mixed_precision']

            # Remove any keys not in GANModelConfig fields before splatting
            final_gan_kwargs = {f.name: gan_kwargs[f.name] for f in dc_fields(GANModelConfig) if f.name in gan_kwargs}
            return GANModelConfig(**final_gan_kwargs)
        else:
            # Prepare arguments for BaseModelConfig, handling nested dataclasses
            base_kwargs = {k: v for k, v in model_config.items() if k in {f.name for f in dc_fields(BaseModelConfig)}}

            if 'loss' in base_kwargs and isinstance(base_kwargs['loss'], dict):
                base_kwargs['loss'] = ConfigLoader._recursive_create_dataclass(LossConfig, base_kwargs['loss'])

            # BaseModelConfig now explicitly requires noise_dim and dimensions
            base_kwargs['noise_dim'] = noise_dim
            base_kwargs['dimensions'] = dimensions

            final_base_kwargs = {f.name: base_kwargs[f.name] for f in dc_fields(BaseModelConfig) if f.name in base_kwargs}
            return BaseModelConfig(**final_base_kwargs)

    @staticmethod
    def _load_data_config(
        config_data: dict[str, Any],
        paths: PathsConfig,
        noise_dim: int,
        dimensions: dict[str, Any]
    ) -> DataConfig:
        """加载数据配置"""
        logger = get_logger(__name__) # 添加 logger 初始化
        try:
            config_data['data'] # 强制要求 'data' 节存在
        except KeyError:
            raise ValueError("配置文件中必须包含 'data' 部分")
        data_config_yaml = config_data['data'] # Get the 'data' section from YAML

        # Prepare kwargs for DataConfig constructor
        # Include all fields defined in DataConfig, attempting to get them from data_config_yaml
        data_kwargs = {}
        data_field_names = {f.name for f in dc_fields(DataConfig)}

        for field_name in data_field_names:
            if field_name == 'paths':
                data_kwargs['paths'] = paths # Explicitly passed
            elif field_name == 'noise_dim':
                data_kwargs['noise_dim'] = noise_dim # Explicitly passed from BaseConfig needs
            elif field_name == 'dimensions':
                data_kwargs['dimensions'] = dimensions # Explicitly passed from BaseConfig needs
            elif field_name in data_config_yaml:
                 # Handle potential nested dataclasses if DataConfig definition requires it
                 # field_type = next((f.type for f in dc_fields(DataConfig) if f.name == field_name), None)
                 # if field_type and is_dataclass(field_type) and isinstance(data_config_yaml[field_name], dict):
                 #     data_kwargs[field_name] = ConfigLoader._recursive_create_dataclass(field_type, data_config_yaml[field_name])
                 # else:
                 data_kwargs[field_name] = data_config_yaml[field_name]
            # else:
                # Let the DataConfig constructor handle missing required fields by raising TypeError

        # Handle 'columns' specifically - 必须存在且为字典类型
        if 'columns' not in data_config_yaml:
            error_msg = "配置文件 data 部分缺少必需的 'columns' 字段"
            logger.error(error_msg)
            raise ValueError(error_msg)

        columns = data_config_yaml['columns']
        if not isinstance(columns, dict):
            error_msg = f"data.columns 必须是字典类型，但获取到的是 {type(columns)}"
            logger.error(error_msg)
            raise TypeError(error_msg)
        data_kwargs['columns'] = columns

        # Attempt to create DataConfig instance. This will raise TypeError if required args are missing.
        return DataConfig(**data_kwargs)

    @staticmethod
    def _load_training_config(config_data: dict[str, Any], noise_dim: int, dimensions: dict[str, Any]) -> TrainingConfig:
        """加载训练配置"""
        logger = get_logger(__name__)
        try:
            training_config = config_data['training'] # 强制要求 'training' 节存在
        except KeyError:
            raise ValueError("配置文件中必须包含 'training' 部分")

        # 处理 batch_size_optimizer 嵌套配置
        if 'batch_size_optimizer' in training_config:
            batch_size_optimizer_data = training_config['batch_size_optimizer']
            # 确保嵌套配置也有必需的基础字段
            batch_size_optimizer_data['noise_dim'] = noise_dim
            batch_size_optimizer_data['dimensions'] = dimensions
            # 递归创建 BatchSizeOptimizerConfig 实例
            batch_size_optimizer = ConfigLoader._recursive_create_dataclass(BatchSizeOptimizerConfig, batch_size_optimizer_data)
            training_config['batch_size_optimizer'] = batch_size_optimizer

        # 处理 adaptive_lambda_gp 嵌套配置
        if 'adaptive_lambda_gp' in training_config:
            # 导入训练配置模块中的AdaptiveLambdaGPConfig类
            from src.utils.config.training import AdaptiveLambdaGPConfig
            adaptive_lambda_gp_data = training_config['adaptive_lambda_gp']
            logger.debug(f"处理 adaptive_lambda_gp 配置: {adaptive_lambda_gp_data}")

            # 构造adaptive_lambda_gp配置数据
            # 强制要求 enabled 字段存在
            if 'enabled' not in adaptive_lambda_gp_data:
                raise ValueError("adaptive_lambda_gp 配置缺少必需字段 'enabled'")

            # 创建 AdaptiveLambdaGPConfig 实例
            adaptive_lambda_gp_config = ConfigLoader._recursive_create_dataclass(AdaptiveLambdaGPConfig, adaptive_lambda_gp_data)
            logger.debug(f"成功创建 AdaptiveLambdaGPConfig 实例: {adaptive_lambda_gp_config}")
            training_config['adaptive_lambda_gp'] = adaptive_lambda_gp_config

        # 处理 balance 嵌套配置
        if 'balance' in training_config:
            balance_data = training_config['balance']
            # 确保嵌套配置也有必需的基础字段
            balance_data['noise_dim'] = noise_dim
            balance_data['dimensions'] = dimensions
            # 递归创建 BalanceConfig 实例
            balance_config = ConfigLoader._recursive_create_dataclass(BalanceConfig, balance_data)
            training_config['balance'] = balance_config

        # 处理 lr_balancer 嵌套配置
        if 'lr_balancer' in training_config:
            lr_balancer_data = training_config['lr_balancer']
            # 确保嵌套配置也有必需的基础字段
            lr_balancer_data['noise_dim'] = noise_dim
            lr_balancer_data['dimensions'] = dimensions
            # 递归创建 LrBalancerConfig 实例
            lr_balancer_config = ConfigLoader._recursive_create_dataclass(LrBalancerConfig, lr_balancer_data)
            training_config['lr_balancer'] = lr_balancer_config

        # 处理 early_stopping 嵌套配置
        if 'early_stopping' in training_config:
            early_stopping_data = training_config['early_stopping']
            # 确保嵌套配置也有必需的基础字段
            early_stopping_data['noise_dim'] = noise_dim
            early_stopping_data['dimensions'] = dimensions
            # 递归创建 EarlyStoppingConfig 实例
            early_stopping_config = ConfigLoader._recursive_create_dataclass(EarlyStoppingConfig, early_stopping_data)
            training_config['early_stopping'] = early_stopping_config

        # 处理 checkpoint 嵌套配置
        if 'checkpoint' in training_config:
            checkpoint_data = training_config['checkpoint']
            # 确保嵌套配置也有必需的基础字段
            checkpoint_data['noise_dim'] = noise_dim
            checkpoint_data['dimensions'] = dimensions
            # 递归创建 CheckpointConfig 实例
            checkpoint_config = ConfigLoader._recursive_create_dataclass(CheckpointConfig, checkpoint_data)
            training_config['checkpoint'] = checkpoint_config

        # Explicitly handle other nested dataclasses within training_config
        # Import necessary config types if not already imported at the top
        from src.utils.config.training import (
            MixedPrecisionConfig,
            OptimizerConfig,
        )

        if 'mixed_precision' in training_config:
            mixed_precision_data = training_config['mixed_precision']
            logger.debug(f"处理 mixed_precision 配置: {mixed_precision_data}")

            # 确保所有必需字段都存在
            required_fields = ['enabled', 'dtype', 'init_scale', 'growth_factor', 'backoff_factor', 'growth_interval', 'cast_model_outputs']
            missing_fields = [field for field in required_fields if field not in mixed_precision_data]
            if missing_fields:
                error_msg = f"mixed_precision配置缺少必需字段: {', '.join(missing_fields)}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # 创建 MixedPrecisionConfig 实例
            mixed_precision_config = ConfigLoader._recursive_create_dataclass(MixedPrecisionConfig, mixed_precision_data)
            logger.debug(f"成功创建 MixedPrecisionConfig 实例: {mixed_precision_config}")
            training_config['mixed_precision'] = mixed_precision_config

        if 'optimizer' in training_config and isinstance(training_config['optimizer'], dict):
            # Assuming OptimizerConfig does not need noise_dim/dimensions itself
            training_config['optimizer'] = ConfigLoader._recursive_create_dataclass(OptimizerConfig, training_config['optimizer'])


        # Add top-level required fields AFTER processing nested structures
        training_config['noise_dim'] = noise_dim
        training_config['dimensions'] = dimensions

        # 创建 TrainingConfig 实例
        # 直接使用字典构造TrainingConfig实例，而不是再次调用_recursive_create_dataclass
        try:
            # 过滤出TrainingConfig所需的字段
            valid_fields = {f.name for f in dc_fields(TrainingConfig)}
            filtered_config = {k: v for k, v in training_config.items() if k in valid_fields}
            return TrainingConfig(**filtered_config)
        except Exception as e:
            logger.error(f"创建TrainingConfig实例失败: {e!s}")
            # 移除回退逻辑，直接抛出异常
            raise ValueError(f"创建 TrainingConfig 实例失败: {e!s}") from e

    @staticmethod
    def _load_feature_engineering_config(
        config_data: dict[str, Any],
        noise_dim: int,
        dimensions: dict[str, Any]
    ) -> FeatureEngineeringConfig:
        """加载特征工程配置"""
        logger = get_logger(__name__)
        try:
            fe_config_data = config_data['feature_engineering'] # 强制要求 'feature_engineering' 节存在
        except KeyError:
            raise ValueError("配置文件中必须包含 'feature_engineering' 部分")

        # 详细记录原始配置数据
        logger.debug(f"原始特征工程配置数据: {fe_config_data}")

        # 确保必需参数
        fe_config_data['noise_dim'] = noise_dim
        fe_config_data['dimensions'] = dimensions

        # 1. 验证基础配置结构
        if 'base_features' not in fe_config_data:
            logger.error("配置中缺少 feature_engineering.base_features 部分")
            raise ValueError("配置中缺少 feature_engineering.base_features 部分")

        # 2. 详细检查base_features结构
        try:
            base_features = fe_config_data['base_features'] # 强制要求 'base_features' 存在
        except KeyError:
            logger.error("配置中缺少 feature_engineering.base_features 部分")
            raise ValueError("配置中缺少 feature_engineering.base_features 部分")
        logger.debug(f"base_features原始配置: {base_features}")

        if not isinstance(base_features, dict):
            logger.error(f"base_features必须是字典类型，实际类型: {type(base_features)}")
            raise TypeError(f"base_features必须是字典类型，实际类型: {type(base_features)}")

        if 'enable' not in base_features:
            logger.error("配置中缺少 feature_engineering.base_features.enable 字段")
            raise ValueError("配置中缺少 feature_engineering.base_features.enable 字段")

        # 3. 验证base_features内容
        try:
            # 强制要求 keep_original 必须在配置中指定
            if 'keep_original' not in base_features:
                 raise ValueError("配置中缺少 feature_engineering.base_features.keep_original 字段")
            base_features_config = BaseFeaturesConfig(
                enable=base_features['enable'],
                keep_original=base_features['keep_original']
            )
            logger.debug(f"成功创建BaseFeaturesConfig实例: {base_features_config}")
        except Exception as e:
            logger.error(f"创建BaseFeaturesConfig失败: {e!s}")
            raise ValueError(f"创建BaseFeaturesConfig失败: {e!s}")

        # 4. 检查差分特征配置
        if 'time_series_features' not in fe_config_data:
            logger.error("配置中缺少 feature_engineering.time_series_features 部分")
            raise ValueError("配置中缺少 feature_engineering.time_series_features 部分")

        try:
            ts_features = fe_config_data['time_series_features'] # 强制要求 'time_series_features' 存在
        except KeyError:
            logger.error("配置中缺少 feature_engineering.time_series_features 部分")
            raise ValueError("配置中缺少 feature_engineering.time_series_features 部分")
        if not isinstance(ts_features, dict):
            logger.error(f"time_series_features必须是字典类型，实际类型: {type(ts_features)}")
            raise TypeError(f"time_series_features必须是字典类型，实际类型: {type(ts_features)}")

        if 'diff_features' not in ts_features:
            logger.error("配置中缺少 feature_engineering.time_series_features.diff_features 部分")
            raise ValueError("配置中缺少 feature_engineering.time_series_features.diff_features 部分")

        try:
            diff_features = ts_features['diff_features'] # 强制要求 'diff_features' 存在
        except KeyError:
            logger.error("配置中缺少 feature_engineering.time_series_features.diff_features 部分")
            raise ValueError("配置中缺少 feature_engineering.time_series_features.diff_features 部分")
        if not isinstance(diff_features, dict):
            logger.error(f"diff_features必须是字典类型，实际类型: {type(diff_features)}")
            raise TypeError(f"diff_features必须是字典类型，实际类型: {type(diff_features)}")

        if 'enable' not in diff_features:
            logger.error("配置中缺少 feature_engineering.time_series_features.diff_features.enable 字段")
            raise ValueError("配置中缺少 feature_engineering.time_series_features.diff_features.enable 字段")

        if 'orders' not in diff_features:
            logger.error("配置中缺少 feature_engineering.time_series_features.diff_features.orders 字段")
            raise ValueError("配置中缺少 feature_engineering.time_series_features.diff_features.orders 字段")

        # 5. 创建差分特征配置实例
        try:
            diff_features_config = DiffFeaturesConfig(
                enable=diff_features['enable'],
                orders=diff_features['orders']
            )
            logger.debug(f"成功创建DiffFeaturesConfig实例: {diff_features_config}")
        except Exception as e:
            logger.error(f"创建DiffFeaturesConfig失败: {e!s}")
            raise ValueError(f"创建DiffFeaturesConfig失败: {e!s}")

        # 6. 创建时间序列特征配置实例
        try:
            # Import necessary nested config types
            from src.utils.config.data import (
                LagFeaturesConfig,
                VolatilityFeaturesConfig,
                WindowFeaturesConfig,
            )

            # 强制要求 time_series_features.enable 必须在配置中指定
            if 'enable' not in ts_features:
                raise ValueError("配置中缺少 feature_engineering.time_series_features.enable 字段")

            # Recursively create instances for nested dataclasses
            lag_features_data = ts_features.get('lag_features', {})
            lag_features_instance = ConfigLoader._recursive_create_dataclass(LagFeaturesConfig, lag_features_data) if isinstance(lag_features_data, dict) else lag_features_data

            window_features_data = ts_features.get('window_features', {})
            window_features_instance = ConfigLoader._recursive_create_dataclass(WindowFeaturesConfig, window_features_data) if isinstance(window_features_data, dict) else window_features_data

            volatility_features_data = ts_features.get('volatility_features', {})
            volatility_features_instance = ConfigLoader._recursive_create_dataclass(VolatilityFeaturesConfig, volatility_features_data) if isinstance(volatility_features_data, dict) else volatility_features_data

            time_series_config = TimeSeriesFeaturesConfig(
                diff_features=diff_features_config,
                enable=ts_features['enable'],
                lag_features=lag_features_instance,
                window_features=window_features_instance,
                volatility_features=volatility_features_instance
            )
            logger.debug(f"成功创建TimeSeriesFeaturesConfig实例: {time_series_config}")
        except Exception as e:
            logger.error(f"创建TimeSeriesFeaturesConfig失败: {e!s}")
            raise ValueError(f"创建TimeSeriesFeaturesConfig失败: {e!s}")

        # 7. 确保配置正确设置
        fe_config_data['base_features'] = base_features_config
        fe_config_data['time_series_features'] = time_series_config

        # Explicitly handle other nested dataclasses within fe_config_data
        # Import necessary config types if not already imported at the top
        from src.utils.config.data import (
            FrequencyFeaturesConfig,
            PCAConfig,
            QualityControlConfig,
            StatisticalFeaturesConfig,
            TimePreprocessingConfig,
            WindowFeaturesConfig,
        )

        # Handle time_preprocessing (now a required field in FeatureEngineeringConfig)
        if 'time_preprocessing' in fe_config_data and isinstance(fe_config_data['time_preprocessing'], dict):
            fe_config_data['time_preprocessing'] = ConfigLoader._recursive_create_dataclass(
                TimePreprocessingConfig,
                fe_config_data['time_preprocessing']
            )
            logger.debug(f"成功创建并设置 TimePreprocessingConfig 实例: {fe_config_data['time_preprocessing']}")
        elif 'time_preprocessing' not in fe_config_data:
            # This field is now mandatory in FeatureEngineeringConfig as per previous changes
            logger.error("配置文件 'feature_engineering' 部分缺少必需的 'time_preprocessing' 节")
            raise ValueError("配置文件 'feature_engineering' 部分缺少必需的 'time_preprocessing' 节")
        # If it exists but is not a dict, _recursive_create_dataclass for FeatureEngineeringConfig will handle it (or __post_init__ will complain)

        if 'statistical_features' in fe_config_data and isinstance(fe_config_data['statistical_features'], dict):
            statistical_features_data = fe_config_data['statistical_features']
            # Recursively instantiate nested fields within statistical_features first
            if 'pca' in statistical_features_data and isinstance(statistical_features_data['pca'], dict):
                statistical_features_data['pca'] = ConfigLoader._recursive_create_dataclass(PCAConfig, statistical_features_data['pca'])
            if 'rolling_stats' in statistical_features_data and isinstance(statistical_features_data['rolling_stats'], dict):
                 # Note: StatisticalFeaturesConfig uses WindowFeaturesConfig for rolling_stats
                statistical_features_data['rolling_stats'] = ConfigLoader._recursive_create_dataclass(WindowFeaturesConfig, statistical_features_data['rolling_stats'])

            # Now create the StatisticalFeaturesConfig instance with potentially instantiated nested objects
            fe_config_data['statistical_features'] = ConfigLoader._recursive_create_dataclass(StatisticalFeaturesConfig, statistical_features_data)

        if 'frequency_features' in fe_config_data and isinstance(fe_config_data['frequency_features'], dict):
            fe_config_data['frequency_features'] = ConfigLoader._recursive_create_dataclass(FrequencyFeaturesConfig, fe_config_data['frequency_features'])

        if 'quality_control' in fe_config_data and isinstance(fe_config_data['quality_control'], dict):
            quality_control_data = fe_config_data['quality_control']
            # Import necessary nested config type
            from src.utils.config.data import OutlierDetectionConfig
            # Recursively instantiate nested fields within quality_control first
            if 'outlier_detection' in quality_control_data and isinstance(quality_control_data['outlier_detection'], dict):
                quality_control_data['outlier_detection'] = ConfigLoader._recursive_create_dataclass(OutlierDetectionConfig, quality_control_data['outlier_detection'])

            # Now create the QualityControlConfig instance with potentially instantiated nested objects
            fe_config_data['quality_control'] = ConfigLoader._recursive_create_dataclass(QualityControlConfig, quality_control_data)

        # 处理 interaction_features 配置
        if 'interaction_features' in fe_config_data and isinstance(fe_config_data['interaction_features'], dict):
            interaction_features_data = fe_config_data['interaction_features']
            logger.debug(f"处理 interaction_features 配置: {interaction_features_data}")

            # 递归实例化 candidate_selection 字段
            if 'candidate_selection' in interaction_features_data and isinstance(interaction_features_data['candidate_selection'], dict):
                candidate_selection_data = interaction_features_data['candidate_selection']
                logger.debug(f"处理 candidate_selection 配置: {candidate_selection_data}")

                # 直接创建 CandidateSelectionConfig 实例，而不是使用_recursive_create_dataclass
                try:
                    # 强制要求所有必需字段存在，直接访问
                    candidate_selection_config = CandidateSelectionConfig(
                        enable=candidate_selection_data['enable'],
                        methods=candidate_selection_data['methods'],
                        combination_logic=candidate_selection_data['combination_logic'],
                        lag_corr=candidate_selection_data['lag_corr'],
                        mutual_info=candidate_selection_data['mutual_info'],
                        top_n_final_candidates=candidate_selection_data['top_n_final_candidates']
                    )
                    interaction_features_data['candidate_selection'] = candidate_selection_config
                    logger.debug(f"成功创建 CandidateSelectionConfig 实例: {candidate_selection_config}")
                except Exception as e:
                    logger.error(f"创建 CandidateSelectionConfig 实例失败: {e!s}")
                    # 移除回退逻辑，直接抛出异常
                    raise ValueError(f"创建 CandidateSelectionConfig 实例失败: {e!s}") from e

            # 直接创建 InteractionFeaturesConfig 实例，而不是使用_recursive_create_dataclass
            try:
                 # 确保必需的字段存在
                 required_fields_if = ['enable', 'top_k'] # candidate_selection 是 Optional
                 for field in required_fields_if:
                     if field not in interaction_features_data:
                         raise ValueError(f"interaction_features 配置缺少必需字段 {field}")

                 interaction_features_config = InteractionFeaturesConfig(
                     enable=interaction_features_data['enable'],
                     top_k=interaction_features_data['top_k'],
                     candidate_selection=interaction_features_data.get('candidate_selection') # 使用处理过的实例或 None
                 )
                 fe_config_data['interaction_features'] = interaction_features_config
                 logger.debug(f"成功创建 InteractionFeaturesConfig 实例: {interaction_features_config}")
            except Exception as e:
                 logger.error(f"创建 InteractionFeaturesConfig 实例失败: {e!s}")
                 # 移除回退逻辑，直接抛出异常
                 raise ValueError(f"创建 InteractionFeaturesConfig 实例失败: {e!s}") from e

        # 处理 layers 配置
        if 'layers' in fe_config_data and isinstance(fe_config_data['layers'], list):
            layers_data = fe_config_data['layers']
            logger.debug(f"处理 layers 配置: {layers_data}")

            # 创建 LayerConfig 实例列表
            layers_config = []
            for i, layer_data in enumerate(layers_data):
                try:
                    # 强制要求所有必需字段存在，直接访问
                    layer_config = LayerConfig(
                        level=layer_data['level'],
                        generators=layer_data['generators'],
                        keep_input_features=layer_data['keep_input_features']
                    )
                    layers_config.append(layer_config)
                    logger.debug(f"成功创建 LayerConfig 实例 {i}: {layer_config}")
                except Exception as e:
                    logger.error(f"创建 LayerConfig 实例 {i} 失败: {e!s}")
                    # 移除回退逻辑，直接抛出异常
                    raise ValueError(f"创建 LayerConfig 实例 {i} 失败: {e!s}") from e

            fe_config_data['layers'] = layers_config
            logger.debug(f"成功创建 layers 配置: {layers_config}")

        # 8. 递归创建配置实例 (Now with properly instantiated nested objects)
        try:
            interaction_features_val = fe_config_data.get('interaction_features')
            logger.debug(f"Before creating FeatureEngineeringConfig, interaction_features value: {interaction_features_val}, type: {type(interaction_features_val)}")

            # 直接创建 FeatureEngineeringConfig 实例，而不是使用_recursive_create_dataclass
            try:
                # 确保必需的字段存在
                required_fields = ['keep_original_in_final', 'enable', 'statistical_features', 'frequency_features', 'quality_control', 'columns', 'time_preprocessing', 'interlayer_quality_filter']
                missing_fields = []
                for field in required_fields:
                    if field not in fe_config_data:
                        missing_fields.append(field)

                if missing_fields:
                    error_msg = f"feature_engineering 配置缺少必需字段: {missing_fields}"
                    logger.error(error_msg)
                    raise ValueError(error_msg)

                # 处理层级间质量过滤配置
                if 'interlayer_quality_filter' in fe_config_data:
                    interlayer_filter_data = fe_config_data['interlayer_quality_filter']
                    if isinstance(interlayer_filter_data, dict):
                        from src.utils.config.data import InterlayerQualityFilterConfig
                        interlayer_quality_filter_config = InterlayerQualityFilterConfig(
                            enable=interlayer_filter_data['enable'],
                            variance_threshold=interlayer_filter_data['variance_threshold'],
                            min_features_threshold=interlayer_filter_data['min_features_threshold']
                        )
                        fe_config_data['interlayer_quality_filter'] = interlayer_quality_filter_config
                        logger.debug(f"成功创建InterlayerQualityFilterConfig实例: {interlayer_quality_filter_config}")

                # 添加必需的基础字段
                fe_config_data['noise_dim'] = noise_dim
                fe_config_data['dimensions'] = dimensions

                fe_instance = FeatureEngineeringConfig(**fe_config_data)
                logger.debug(f"成功创建特征工程配置实例: {fe_instance}")
            except Exception as e:
                logger.error(f"直接创建 FeatureEngineeringConfig 实例失败: {e!s}")
                # 移除回退逻辑，直接抛出异常
                raise ValueError(f"创建 FeatureEngineeringConfig 实例失败: {e!s}") from e

            # 9. 详细验证实例结构
            # 移除hasattr检查，直接访问属性，如果不存在会抛出AttributeError
            try:
                # 检查基本属性
                _ = fe_instance.enable
                _ = fe_instance.keep_original_in_final

                # 检查 layers 或 (base_features 和 time_series_features) 是否存在
                layers_present = fe_instance.layers is not None
                old_structure_present = (fe_instance.base_features is not None and
                                        fe_instance.time_series_features is not None)
            except AttributeError as e:
                error_msg = f"特征工程配置实例缺少必需属性: {e}"
                logger.error(error_msg)
                raise ValueError(error_msg) from e

            if fe_instance.enable and not (layers_present or old_structure_present):
                logger.error("特征工程配置实例启用 (enable=True)，但缺少 layers 或 (base_features 和 time_series_features)")
                raise ValueError("特征工程配置实例启用 (enable=True)，但缺少 layers 或 (base_features 和 time_series_features)")

            # 如果使用旧结构，检查 base_features 和 time_series_features
            if old_structure_present:
                # 检查 base_features.enable 属性
                if not hasattr(fe_instance.base_features, 'enable'):
                    logger.error("特征工程配置实例的 base_features 缺少 enable 属性")
                    raise ValueError("特征工程配置实例的 base_features 缺少 enable 属性")

                # 检查 time_series_features.enable 属性
                if not hasattr(fe_instance.time_series_features, 'enable'):
                    logger.error("特征工程配置实例的 time_series_features 缺少 enable 属性")
                    raise ValueError("特征工程配置实例的 time_series_features 缺少 enable 属性")

                # 检查 diff_features 属性
                if hasattr(fe_instance, 'time_series_features') and fe_instance.time_series_features is not None:
                    if not hasattr(fe_instance.time_series_features, 'diff_features'):
                        logger.error("特征工程配置实例的 time_series_features 缺少 diff_features 属性")
                        raise ValueError("特征工程配置实例的 time_series_features 缺少 diff_features 属性")

                    # 检查 diff_features.enable 属性
                    if hasattr(fe_instance.time_series_features, 'diff_features') and fe_instance.time_series_features.diff_features is not None:
                        if not hasattr(fe_instance.time_series_features.diff_features, 'enable'):
                            logger.error("特征工程配置实例的 time_series_features.diff_features 缺少 enable 属性")
                            raise ValueError("特征工程配置实例的 time_series_features.diff_features 缺少 enable 属性")

                        # 检查 diff_features.orders 属性
                        if not hasattr(fe_instance.time_series_features.diff_features, 'orders'):
                            logger.error("特征工程配置实例的 time_series_features.diff_features 缺少 orders 属性")
                            raise ValueError("特征工程配置实例的 time_series_features.diff_features 缺少 orders 属性")

            # 如果使用新结构，检查 layers
            if layers_present:
                # 检查 layers 是否为列表
                if not isinstance(fe_instance.layers, list):
                    logger.error("特征工程配置实例的 layers 不是列表")
                    raise ValueError("特征工程配置实例的 layers 不是列表")

                # 检查 layers 是否为空
                if not fe_instance.layers:
                    logger.error("特征工程配置实例的 layers 为空列表")
                    raise ValueError("特征工程配置实例的 layers 为空列表")

                # 检查每个 layer 是否为 LayerConfig 类型
                for i, layer in enumerate(fe_instance.layers):
                    if not isinstance(layer, LayerConfig):
                        logger.error(f"特征工程配置实例的 layers[{i}] 不是 LayerConfig 类型")
                        raise ValueError(f"特征工程配置实例的 layers[{i}] 不是 LayerConfig 类型")

            return fe_instance
        except Exception as e:
            logger.error(f"创建特征工程配置失败: {e!s}")
            raise

    @staticmethod
    def _load_evaluation_config(
        config_data: dict[str, Any],
        noise_dim: int,
        dimensions: dict[str, Any]
    ) -> EvaluationConfig:
        """加载评估配置"""
        # 使用noise_dim和dimensions参数，避免未使用警告
        logger = get_logger(__name__)
        logger.debug(f"加载评估配置，noise_dim={noise_dim}, dimensions={dimensions}")

        try:
            eval_config = config_data['evaluation'] # 强制要求 'evaluation' 节存在
        except KeyError:
            raise ValueError("配置文件中必须包含 'evaluation' 部分")
        valid_fields = {f.name for f in dc_fields(EvaluationConfig)}
        filtered_config = {k: v for k, v in eval_config.items() if k in valid_fields}
        return EvaluationConfig(**filtered_config)
