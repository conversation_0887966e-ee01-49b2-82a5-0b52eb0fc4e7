"""训练配置模块 - 管理训练过程、优化器和评估相关配置"""

from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Any

import torch

# 导入AdaptiveLambdaGPConfig
from src.utils.adaptive_lambda_gp_config import AdaptiveLambdaGPConfig
from src.utils.config.base import BaseConfig

# Import BatchSizeOptimizerConfig from its new location
from src.utils.config.batch_size_optimizer import BatchSizeOptimizerConfig


@dataclass
class MixedPrecisionConfig:
    """混合精度训练配置"""
    enabled: bool
    dtype: str
    init_scale: float
    growth_factor: float
    backoff_factor: float
    growth_interval: int
    cast_model_outputs: bool

    def __post_init__(self):
        """初始化混合精度训练配置，检查必需字段并在缺失时抛出异常"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        # 验证参数有效性
        valid_dtypes = ["float16", "bfloat16", "float32"]
        if self.dtype not in valid_dtypes:
            error_msg = f"无效的数据类型: {self.dtype}，有效选项: {valid_dtypes}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if not isinstance(self.init_scale, int | float) or self.init_scale <= 0:
            error_msg = f"init_scale必须是大于0的数值，但获取到的是: {self.init_scale}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if not isinstance(self.growth_factor, int | float) or self.growth_factor <= 0:
            error_msg = f"growth_factor必须是大于0的数值，但获取到的是: {self.growth_factor}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if not isinstance(self.backoff_factor, int | float) or not (0 < self.backoff_factor < 1):
            error_msg = f"backoff_factor必须在(0, 1)范围内，但获取到的是: {self.backoff_factor}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if not isinstance(self.growth_interval, int) or self.growth_interval <= 0:
            error_msg = f"growth_interval必须是大于0的整数，但获取到的是: {self.growth_interval}"
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class OptimizerConfig:
    """优化器配置"""
    # 移除 Optional 和 = None，变为必需字段
    type: str
    generator_lr: float
    discriminator_lr: float
    weight_decay: float
    beta1: float
    beta2: float
    momentum: float
    nesterov: bool
    eps: float
    # 移除 dimensions 和 noise_dim

    def __post_init__(self):
        """初始化优化器配置，检查必需字段并在缺失时抛出异常"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        # 检查必需字段
        missing_fields = []

        # 检查必需字段是否存在 (移除 is None)
        if not hasattr(self, 'type'): missing_fields.append('type')
        if not hasattr(self, 'generator_lr'): missing_fields.append('generator_lr')
        if not hasattr(self, 'discriminator_lr'): missing_fields.append('discriminator_lr')
        if not hasattr(self, 'weight_decay'): missing_fields.append('weight_decay')
        if not hasattr(self, 'beta1'): missing_fields.append('beta1')
        if not hasattr(self, 'beta2'): missing_fields.append('beta2')
        if not hasattr(self, 'momentum'): missing_fields.append('momentum')
        if not hasattr(self, 'nesterov'): missing_fields.append('nesterov')
        if not hasattr(self, 'eps'): missing_fields.append('eps')

        # 如果有缺失字段，抛出异常
        if missing_fields:
            error_msg = f"优化器配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 验证参数有效性
        valid_types = ["adam", "sgd", "rmsprop", "adamw"]
        if self.type not in valid_types:
            error_msg = f"无效的优化器类型: {self.type}，有效选项: {valid_types}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 移除断言，因为字段不再是 Optional
        # assert self.generator_lr is not None, "generator_lr不能为None"
        # ... (移除其他断言) ...

        if float(self.generator_lr) < 0:
            error_msg = f"generator_lr必须大于等于0，但获取到的是: {self.generator_lr}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if float(self.discriminator_lr) < 0:
            error_msg = f"discriminator_lr必须大于等于0，但获取到的是: {self.discriminator_lr}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if float(self.weight_decay) < 0:
            error_msg = f"weight_decay必须大于等于0，但获取到的是: {self.weight_decay}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        beta1_float = float(self.beta1)
        if not 0 <= beta1_float < 1:
            error_msg = f"beta1必须在[0, 1)范围内，但获取到的是: {self.beta1}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        beta2_float = float(self.beta2)
        if not 0 <= beta2_float < 1:
            error_msg = f"beta2必须在[0, 1)范围内，但获取到的是: {self.beta2}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if float(self.momentum) < 0 or float(self.momentum) >= 1:
            error_msg = f"momentum必须在[0, 1)范围内，但获取到的是: {self.momentum}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if float(self.eps) <= 0:
            error_msg = f"eps必须大于0，但获取到的是: {self.eps}"
            logger.error(error_msg)
            raise ValueError(error_msg)

    # 移除过时的 get_optimizer 方法
    # def get_optimizer(self, parameters) -> torch.optim.Optimizer:
    #     ... (移除整个方法实现) ...


@dataclass
class EarlyStoppingConfig:
    """早停配置"""
    # 移除 Optional 和 = None，变为必需字段
    enabled: bool
    patience: int
    min_delta: float
    mode: str
    # 移除 dimensions 和 noise_dim

    def __post_init__(self):
        """初始化早停配置，检查必需字段并在缺失时抛出异常"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        # 检查必需字段
        missing_fields = []

        # 检查必需字段是否存在 (移除 is None)
        if not hasattr(self, 'enabled'): missing_fields.append('enabled')
        if not hasattr(self, 'patience'): missing_fields.append('patience')
        if not hasattr(self, 'min_delta'): missing_fields.append('min_delta')
        if not hasattr(self, 'mode'): missing_fields.append('mode')

        # 如果有缺失字段，抛出异常
        if missing_fields:
            error_msg = f"早停配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 验证参数有效性
        valid_modes = ["min", "max"]
        if self.mode not in valid_modes:
            error_msg = f"无效的早停模式: {self.mode}，有效选项: {valid_modes}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 移除断言，因为字段不再是 Optional
        # assert self.patience is not None, "patience不能为None"
        # assert self.min_delta is not None, "min_delta不能为None"

        if int(self.patience) < 0:
            error_msg = f"patience必须大于等于0，但获取到的是: {self.patience}"
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class CheckpointConfig:
    """检查点配置

    设计说明：
    1. 简化检查点配置，移除与早停重复的功能
    2. 只保留基本的保存频率和保留数量配置
    3. 最佳模型保存由早停机制统一管理
    """
    # 移除 Optional 和 = None，变为必需字段
    save_freq: int
    keep_last_n: int
    keep_best_k: int
    memory_optimization: bool
    enable_checkpointing: bool
    metric_name: str
    metric_mode: str
    # 移除兼容字段 frequency 和 keep_best_n
    # frequency: Optional[int] = None
    # keep_best_n: Optional[int] = None
    # 移除 dimensions 和 noise_dim

    def __post_init__(self):
        """初始化检查点配置，检查必需字段并在缺失时抛出异常"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        # 检查必需字段
        missing_fields = []

        # 检查必需字段是否存在 (移除 is None)
        if not hasattr(self, 'save_freq'): missing_fields.append('save_freq')
        if not hasattr(self, 'keep_last_n'): missing_fields.append('keep_last_n')
        if not hasattr(self, 'keep_best_k'): missing_fields.append('keep_best_k')
        if not hasattr(self, 'memory_optimization'): missing_fields.append('memory_optimization')
        if not hasattr(self, 'enable_checkpointing'): missing_fields.append('enable_checkpointing')
        if not hasattr(self, 'metric_name'):
            missing_fields.append('metric_name')
        if not hasattr(self, 'metric_mode'):
            missing_fields.append('metric_mode')

        # 如果有缺失字段，抛出异常
        if missing_fields:
            error_msg = f"检查点配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 移除断言，因为字段不再是 Optional
        # assert self.save_freq is not None, "save_freq不能为None"
        # ... (移除其他断言) ...

        # 移除兼容字段设置逻辑
        # if self.frequency is None:
        #     self.frequency = self.save_freq
        # if self.keep_best_n is None:
        #     self.keep_best_n = self.keep_best_k

        if int(self.save_freq) <= 0:
            error_msg = f"save_freq必须大于0，但获取到的是: {self.save_freq}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if int(self.keep_last_n) <= 0:
            error_msg = f"keep_last_n必须大于0，但获取到的是: {self.keep_last_n}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if int(self.keep_best_k) <= 0:
            error_msg = f"keep_best_k必须大于0，但获取到的是: {self.keep_best_k}"
            logger.error(error_msg)
            raise ValueError(error_msg)

    # 移除 from_dict 方法，加载由 ConfigLoader 处理
    # @classmethod
    # def from_dict(cls, config_dict: Dict[str, Any]) -> 'CheckpointConfig':
    #     ... (移除整个方法实现) ...

@dataclass
class LossConfig:
    """损失函数配置"""
    feature_matching_weight: float
    temporal_consistency_weight: float
    adversarial_weight: float

    def __post_init__(self):
        """初始化损失函数配置，验证参数有效性"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        # 验证参数有效性
        if self.feature_matching_weight < 0:
            error_msg = f"feature_matching_weight必须大于等于0，但获取到的是: {self.feature_matching_weight}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if self.temporal_consistency_weight < 0:
            error_msg = f"temporal_consistency_weight必须大于等于0，但获取到的是: {self.temporal_consistency_weight}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if self.adversarial_weight < 0:
            error_msg = f"adversarial_weight必须大于等于0，但获取到的是: {self.adversarial_weight}"
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class EvaluationConfig:
    """评估配置"""
    # 移除 Optional 和 = None，变为必需字段
    metrics: list[str]
    batch_size: int
    min_variance: float
    max_value: float
    prediction: dict[str, Any] # 保持字典，内部结构在 post_init 验证
    # 移除 dimensions 和 noise_dim

    def __post_init__(self):
        """初始化评估配置，检查必需字段并在缺失时抛出异常"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        # 检查必需字段
        missing_fields = []

        # 检查必需字段是否存在 (移除 is None)
        if not hasattr(self, 'metrics') or not isinstance(self.metrics, list):
             missing_fields.append('metrics (必须是列表)')
        if not hasattr(self, 'batch_size'):
            missing_fields.append('batch_size')
        if not hasattr(self, 'min_variance'):
            missing_fields.append('min_variance')
        if not hasattr(self, 'max_value'):
            missing_fields.append('max_value')
        if not hasattr(self, 'prediction') or not isinstance(self.prediction, dict):
             missing_fields.append('prediction (必须是字典)')

        # 如果有缺失字段，抛出异常
        if missing_fields:
            error_msg = f"评估配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 移除断言，因为字段不再是 Optional
        # assert self.metrics is not None, "metrics不能为None"
        # ... (移除其他断言) ...

        # 验证参数有效性
        if not isinstance(self.metrics, list) or len(self.metrics) == 0:
            error_msg = f"metrics必须是非空列表，但获取到的是: {self.metrics}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if int(self.batch_size) <= 0:
            error_msg = f"batch_size必须大于0，但获取到的是: {self.batch_size}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if float(self.min_variance) <= 0:
            error_msg = f"min_variance必须大于0，但获取到的是: {self.min_variance}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if float(self.max_value) <= 0:
            error_msg = f"max_value必须大于0，但获取到的是: {self.max_value}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 验证prediction字典
        if not isinstance(self.prediction, dict):
            error_msg = f"prediction必须是字典类型，但获取到的是: {type(self.prediction)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        required_prediction_keys = ["enabled", "batch_size", "output_dir", "save_format"]
        missing_prediction_keys = [key for key in required_prediction_keys if key not in self.prediction]
        if missing_prediction_keys:
            error_msg = f"prediction字典缺失必需键: {', '.join(missing_prediction_keys)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if not isinstance(self.prediction["enabled"], bool):
            error_msg = f"prediction.enabled必须是布尔类型，但获取到的是: {type(self.prediction['enabled'])}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if not isinstance(self.prediction["batch_size"], int) or self.prediction["batch_size"] <= 0:
            error_msg = f"prediction.batch_size必须是正整数，但获取到的是: {self.prediction['batch_size']}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if not isinstance(self.prediction["output_dir"], str) or not self.prediction["output_dir"]:
            error_msg = f"prediction.output_dir必须是非空字符串，但获取到的是: {self.prediction['output_dir']}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if not isinstance(self.prediction["save_format"], str) or self.prediction["save_format"] not in ["csv", "json", "pickle"]:
            error_msg = f"prediction.save_format必须是'csv', 'json'或'pickle'，但获取到的是: {self.prediction['save_format']}"
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class BalanceConfig:
    """训练平衡配置"""
    # 移除 Optional 和 = None，变为必需字段
    lower_threshold: float
    upper_threshold: float
    min_n_critic: int
    max_n_critic: int
    min_g_steps: int
    max_g_steps: int
    # 移除 dimensions 和 noise_dim

    def __post_init__(self):
        """初始化训练平衡配置，检查必需字段并在缺失时抛出异常"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        # 检查必需字段
        missing_fields = []

        # 检查必需字段是否存在 (移除 is None)
        if not hasattr(self, 'lower_threshold'):
            missing_fields.append('lower_threshold')
        if not hasattr(self, 'upper_threshold'):
            missing_fields.append('upper_threshold')
        if not hasattr(self, 'min_n_critic'):
            missing_fields.append('min_n_critic')
        if not hasattr(self, 'max_n_critic'):
            missing_fields.append('max_n_critic')
        if not hasattr(self, 'min_g_steps'):
            missing_fields.append('min_g_steps')
        if not hasattr(self, 'max_g_steps'):
            missing_fields.append('max_g_steps')

        # 如果有缺失字段，抛出异常
        if missing_fields:
            error_msg = f"训练平衡配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 移除断言，因为字段不再是 Optional
        # assert self.lower_threshold is not None, "lower_threshold不能为None"
        # ... (移除其他断言) ...

        # 验证参数有效性
        if float(self.lower_threshold) <= 0:
            error_msg = f"lower_threshold必须大于0，但获取到的是: {self.lower_threshold}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if float(self.upper_threshold) <= float(self.lower_threshold):
            error_msg = f"upper_threshold必须大于lower_threshold，但获取到的是: {self.upper_threshold} <= {self.lower_threshold}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if int(self.min_n_critic) < 1:
            error_msg = f"min_n_critic必须大于等于1，但获取到的是: {self.min_n_critic}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if int(self.max_n_critic) < int(self.min_n_critic):
            error_msg = f"max_n_critic必须大于等于min_n_critic，但获取到的是: {self.max_n_critic} < {self.min_n_critic}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if int(self.min_g_steps) < 1:
            error_msg = f"min_g_steps必须大于等于1，但获取到的是: {self.min_g_steps}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if int(self.max_g_steps) < int(self.min_g_steps):
            error_msg = f"max_g_steps必须大于等于min_g_steps，但获取到的是: {self.max_g_steps} < {self.min_g_steps}"
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class LrBalancerConfig:
    """学习率平衡器配置"""
    enabled: bool
    type: str
    target_ratio: float
    sensitivity: float
    min_lr: float
    max_lr: float
    epsilon: float
    # 增强版多指标平衡器的额外字段
    performance_weight: float = 0.3
    stability_weight: float = 0.2
    loss_ratio_weight: float = 0.5
    history_window: int = 5
    improvement_threshold: float = 0.05
    stability_threshold: float = 0.1
    # 新增：调整模式配置
    adjustment_mode: str = "adaptive"  # "sync", "inverse", "adaptive"
    mode_switch_threshold: float = 0.3  # 自适应模式切换阈值

    def __post_init__(self):
        """初始化学习率平衡器配置，检查必需字段并确保没有默认值回退"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        # 检查必需字段是否存在
        required = [
            'enabled', 'type', 'target_ratio', 'sensitivity',
            'min_lr', 'max_lr', 'epsilon'
        ]

        missing_fields = []
        for field in required:
            if not hasattr(self, field):
                missing_fields.append(field)

        if missing_fields:
            error_msg = f"学习率平衡器配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 验证参数有效性
        if not isinstance(self.enabled, bool):
            error_msg = f"enabled必须是布尔类型，但获取到的是: {type(self.enabled)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        valid_types = ["enhanced_multi_metric"]
        if self.type not in valid_types:
            error_msg = f"无效的学习率平衡器类型: {self.type}，有效选项: {valid_types}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 数值验证
        try:
            if float(self.target_ratio) <= 0:
                error_msg = f"target_ratio必须大于0，但获取到的是: {self.target_ratio}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            if float(self.sensitivity) <= 0:
                error_msg = f"sensitivity必须大于0，但获取到的是: {self.sensitivity}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            if float(self.min_lr) <= 0:
                error_msg = f"min_lr必须大于0，但获取到的是: {self.min_lr}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            if float(self.max_lr) <= 0:
                error_msg = f"max_lr必须大于0，但获取到的是: {self.max_lr}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            if float(self.min_lr) >= float(self.max_lr):
                error_msg = f"min_lr必须小于max_lr，但获取到的是: min_lr={self.min_lr}, max_lr={self.max_lr}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            if float(self.epsilon) <= 0:
                error_msg = f"epsilon必须大于0，但获取到的是: {self.epsilon}"
                logger.error(error_msg)
                raise ValueError(error_msg)
        except (TypeError, ValueError) as e:
            error_msg = f"无法将学习率平衡器参数转换为浮点数: {e!s}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 验证新增的调整模式参数
        valid_adjustment_modes = ["sync", "inverse", "adaptive"]
        if self.adjustment_mode not in valid_adjustment_modes:
            error_msg = f"无效的调整模式: {self.adjustment_mode}，有效选项: {valid_adjustment_modes}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        try:
            if float(self.mode_switch_threshold) <= 0 or float(self.mode_switch_threshold) > 1:
                error_msg = f"mode_switch_threshold必须在(0, 1]范围内，但获取到的是: {self.mode_switch_threshold}"
                logger.error(error_msg)
                raise ValueError(error_msg)
        except (TypeError, ValueError) as e:
            error_msg = f"无法将mode_switch_threshold转换为浮点数: {e!s}"
            logger.error(error_msg)
            raise ValueError(error_msg)

# 第二个AdaptiveLambdaGPConfig类已删除，使用从src.utils.adaptive_lambda_gp_config导入的类

@dataclass
class TrainingConfig(BaseConfig): # 恢复 BaseConfig 继承
    """训练配置"""
    # 移除 Optional 和 = None，变为必需字段
    batch_size: int
    num_epochs: int
    lambda_gp: float
    use_adaptive_lambda_gp: bool # 新增：启用自适应梯度惩罚权重
    num_workers: int
    dropout_rate: float
    seed: int
    save_dir: Path # 保持 Path 类型
    mixed_precision: MixedPrecisionConfig
    optimizer: OptimizerConfig
    lr_scheduler: dict[str, Any] # 保持字典，内部结构在 post_init 验证
    early_stopping: EarlyStoppingConfig
    checkpoint: CheckpointConfig
    batch_size_optimizer: BatchSizeOptimizerConfig
    lr_balancer: LrBalancerConfig
    balance: BalanceConfig
    dynamic_batch_size: bool # 假设 dynamic_batch_size 也是必需的
    gradient_explosion_threshold: float # 新增梯度爆炸阈值
    gradient_clip_val: float # 新增梯度裁剪值
    # 移除默认值，所有字段都变为必需 (规则 35)
    adaptive_lambda_gp: AdaptiveLambdaGPConfig # 自适应梯度惩罚权重配置，改为AdaptiveLambdaGPConfig类型
    n_critic: int | None = None # 判别器训练次数，可选参数，实际使用balance.min_n_critic
    # noise_dim 和 dimensions 从 BaseConfig 继承

    def __post_init__(self):
        """初始化训练配置，检查必需字段并在缺失时抛出异常"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        # 检查必需字段
        missing_fields = []

        # 检查必需字段是否存在 (移除 is None)
        required = [
            'batch_size', 'num_epochs', 'lambda_gp', 'use_adaptive_lambda_gp', 'num_workers',
            'dropout_rate', 'seed', 'save_dir', 'mixed_precision', 'optimizer',
            'lr_scheduler', 'early_stopping',
            'checkpoint', 'batch_size_optimizer', 'lr_balancer', 'balance',
            'dynamic_batch_size', 'gradient_explosion_threshold', 'gradient_clip_val',
            'adaptive_lambda_gp' # n_critic现在是可选的，从required列表中移除
        ]
        for field_name in required:
             if not hasattr(self, field_name):
                  missing_fields.append(field_name)

        # 检查嵌套类型
        nested_types = {
            'mixed_precision': MixedPrecisionConfig,
            'optimizer': OptimizerConfig,
            'early_stopping': EarlyStoppingConfig,
            'checkpoint': CheckpointConfig,
            'batch_size_optimizer': BatchSizeOptimizerConfig,
            'lr_balancer': LrBalancerConfig,
            'balance': BalanceConfig,
            'adaptive_lambda_gp': AdaptiveLambdaGPConfig  # 添加AdaptiveLambdaGPConfig类型验证
        }
        for field_name, expected_type in nested_types.items():
             if hasattr(self, field_name) and not isinstance(getattr(self, field_name), expected_type):
                  missing_fields.append(f'{field_name} (必须是 {expected_type.__name__} 类型)')

        # 检查 lr_scheduler 类型
        if hasattr(self, 'lr_scheduler') and not isinstance(self.lr_scheduler, dict):
             missing_fields.append('lr_scheduler (必须是字典类型)')

        # 检查 save_dir 类型
        if hasattr(self, 'save_dir') and not isinstance(self.save_dir, Path):
             # 尝试转换，如果失败则报错
             try:
                  self.save_dir = Path(self.save_dir)
             except TypeError:
                  missing_fields.append('save_dir (必须是有效的路径字符串或 Path 对象)')

        # 如果有缺失字段，抛出异常
        if missing_fields:
            error_msg = f"训练配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 移除断言，因为字段不再是 Optional
        # assert self.batch_size is not None, "batch_size不能为None"
        # ... (移除其他断言) ...

        # 验证参数有效性
        # 移除断言，因为字段不再是 Optional
        # assert self.batch_size is not None, "batch_size不能为None"
        # ... (移除其他断言) ...


        batch_size_int = int(self.batch_size)
        if batch_size_int <= 0:
            error_msg = f"batch_size必须大于0，但获取到的是: {self.batch_size}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        num_epochs_int = int(self.num_epochs)
        if num_epochs_int <= 0:
            error_msg = f"num_epochs必须大于0，但获取到的是: {self.num_epochs}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        lambda_gp_float = float(self.lambda_gp)
        if lambda_gp_float < 0:
            error_msg = f"lambda_gp必须大于等于0，但获取到的是: {self.lambda_gp}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # n_critic现在是可选的，只在提供时验证
        if self.n_critic is not None:
            n_critic_int = int(self.n_critic)
            if n_critic_int <= 0:
                error_msg = f"n_critic必须大于0，但获取到的是: {self.n_critic}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        dropout_rate_float = float(self.dropout_rate)
        if dropout_rate_float < 0 or dropout_rate_float >= 1:
            error_msg = f"dropout_rate必须在[0, 1)范围内，但获取到的是: {self.dropout_rate}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        gradient_explosion_threshold_float = float(self.gradient_explosion_threshold) # 验证新字段
        if gradient_explosion_threshold_float <= 0:
            error_msg = f"gradient_explosion_threshold必须大于0，但获取到的是: {self.gradient_explosion_threshold}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        gradient_clip_val_float = float(self.gradient_clip_val) # 验证新字段 gradient_clip_val
        if gradient_clip_val_float <= 0:
            error_msg = f"gradient_clip_val必须大于0，但获取到的是: {self.gradient_clip_val}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 验证lr_scheduler字典
        if not isinstance(self.lr_scheduler, dict):
            error_msg = f"lr_scheduler必须是字典类型，但获取到的是: {type(self.lr_scheduler)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if "enabled" not in self.lr_scheduler:
            error_msg = "lr_scheduler字典缺失enabled键"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 验证自适应梯度惩罚权重配置
        if self.use_adaptive_lambda_gp:
            if self.adaptive_lambda_gp is None:
                error_msg = "启用了自适应梯度惩罚权重 (use_adaptive_lambda_gp=True)，但未提供配置 (adaptive_lambda_gp=None)"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # 验证adaptive_lambda_gp是否为AdaptiveLambdaGPConfig类型
            if not isinstance(self.adaptive_lambda_gp, AdaptiveLambdaGPConfig):
                error_msg = f"adaptive_lambda_gp必须是AdaptiveLambdaGPConfig类型，但获取到的是: {type(self.adaptive_lambda_gp)}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # 确保AdaptiveLambdaGPConfig的enabled字段为True
            if not self.adaptive_lambda_gp.enabled:
                logger.warning("use_adaptive_lambda_gp为True，但adaptive_lambda_gp.enabled为False，将自动启用")
                self.adaptive_lambda_gp.enabled = True
