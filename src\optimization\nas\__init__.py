"""神经架构搜索(NAS)模块 - 自动搜索最优GAN网络结构

模块功能：
1. 搜索空间定义 - 定义生成器、判别器、特征编码器的搜索空间
2. 搜索策略实现 - DARTS和进化算法
3. 评估指标计算 - MAE、训练稳定性、推理速度、参数量
4. 资源约束管理 - GPU显存限制、时间预算控制
5. 分布式搜索支持 - 加速搜索过程

核心组件：
- SearchSpace: 搜索空间定义
- DARTSSearcher: 可微分架构搜索
- EvolutionarySearcher: 进化算法搜索
- NASEvaluator: 架构评估器
- NASManager: 搜索管理器
"""

from .search_space import (
    GeneratorSearchSpace,
    DiscriminatorSearchSpace,
    FeatureEncoderSearchSpace,
    TrainingStrategySearchSpace,
    LossSearchSpace,
    RegularizationSearchSpace,
    SearchSpaceManager,
    TrainingStrategyConfig,
    LossConfig,
    RegularizationConfig
)

from .searchers import (
    DARTSSearcher,
    EvolutionarySearcher,
    SearcherBase
)

from .evaluator import (
    NASEvaluator,
    ArchitectureMetrics,
    EvaluationResult
)

from .manager import (
    NASManager,
    SearchResult
)

from .utils import (
    ArchitectureEncoder,
    ResourceMonitor,
    SearchLogger
)

__all__ = [
    # 搜索空间
    'GeneratorSearchSpace',
    'DiscriminatorSearchSpace',
    'FeatureEncoderSearchSpace',
    'TrainingStrategySearchSpace',
    'LossSearchSpace',
    'RegularizationSearchSpace',
    'SearchSpaceManager',

    # 配置类
    'TrainingStrategyConfig',
    'LossConfig',
    'RegularizationConfig',

    # 搜索器
    'DARTSSearcher',
    'EvolutionarySearcher',
    'SearcherBase',

    # 评估器
    'NASEvaluator',
    'ArchitectureMetrics',
    'EvaluationResult',

    # 管理器
    'NASManager',
    'SearchResult',

    # 工具
    'ArchitectureEncoder',
    'ResourceMonitor',
    'SearchLogger'
]
