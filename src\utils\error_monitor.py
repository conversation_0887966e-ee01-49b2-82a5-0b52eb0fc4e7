"""
错误监控工具

提供异常处理和NaN检测功能，用于在训练和验证过程中检测和处理异常情况。
"""
import logging
import os
import time
import traceback

import torch

# 获取日志记录器
logger = logging.getLogger("ErrorMonitor")

class ErrorMonitor:
    """错误监控工具，用于检测和处理异常情况"""

    def __init__(
        self,
        log_path: str | None = None,
        enable_nan_detection: bool = True,
        enable_traceback: bool = True,
        max_errors: int = 5,
        error_cooldown: int = 60  # 秒
    ):
        """
        初始化错误监控工具

        Args:
            log_path: 错误日志文件路径（如果为None，则只记录到控制台）
            enable_nan_detection: 是否启用NaN检测
            enable_traceback: 是否记录完整堆栈信息
            max_errors: 最大错误数量，超过此数量将停止训练
            error_cooldown: 错误冷却时间（秒），在此时间内的相同错误只记录一次
        """
        self.enable_nan_detection = enable_nan_detection
        self.enable_traceback = enable_traceback
        self.max_errors = max_errors
        self.error_cooldown = error_cooldown

        # 错误计数和时间戳
        self.error_count = 0
        self.error_timestamps = {}
        self.last_error_type = None

        # 设置日志文件
        if log_path:
            os.makedirs(os.path.dirname(log_path), exist_ok=True)
            file_handler = logging.FileHandler(log_path, mode='w', encoding='utf-8')  # 使用覆写模式，指定UTF-8编码
            file_handler.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(name)s: %(message)s'))
            file_handler.setLevel(logging.DEBUG)  # 设置文件处理器级别
            logger.addHandler(file_handler)

        # 确保logger级别足够低以记录INFO级别的日志
        logger.setLevel(logging.DEBUG)

        logger.info(f"错误监控初始化完成，NaN检测: {enable_nan_detection}，堆栈跟踪: {enable_traceback}")

    def check_nan_tensors(self, tensors: dict[str, torch.Tensor]) -> tuple[bool, list[str]]:
        """
        检查张量中是否存在NaN或Inf值

        Args:
            tensors: 要检查的张量字典

        Returns:
            Tuple[bool, List[str]]: (是否有效, 问题张量列表)
        """
        if not self.enable_nan_detection:
            return True, []

        invalid_tensors = []

        for name, tensor in tensors.items():
            if tensor is None:
                continue

            if not isinstance(tensor, torch.Tensor):
                continue

            if torch.isnan(tensor).any() or torch.isinf(tensor).any():
                # 计算NaN和Inf的数量和比例
                total_elements = tensor.numel()
                nan_count = torch.isnan(tensor).sum().item()
                inf_count = torch.isinf(tensor).sum().item()
                nan_percentage = (nan_count / total_elements) * 100 if total_elements > 0 else 0
                inf_percentage = (inf_count / total_elements) * 100 if total_elements > 0 else 0

                error_msg = (
                    f"{name}数值不稳定:\n"
                    f"- 形状: {tensor.shape}\n"
                    f"- NaN: {nan_count}/{total_elements} ({nan_percentage:.2f}%)\n"
                    f"- Inf: {inf_count}/{total_elements} ({inf_percentage:.2f}%)"
                )

                logger.error(error_msg)
                invalid_tensors.append(name)

        return len(invalid_tensors) == 0, invalid_tensors

    def check_nan_grads(self, model: torch.nn.Module) -> tuple[bool, list[str]]:
        """
        检查模型梯度中是否存在NaN或Inf值

        Args:
            model: PyTorch模型

        Returns:
            Tuple[bool, List[str]]: (是否有效, 问题参数列表)
        """
        if not self.enable_nan_detection:
            return True, []

        invalid_params = []

        for name, param in model.named_parameters():
            if param.grad is not None and (torch.isnan(param.grad).any() or torch.isinf(param.grad).any()):
                # 计算NaN和Inf的数量和比例
                total_elements = param.grad.numel()
                nan_count = torch.isnan(param.grad).sum().item()
                inf_count = torch.isinf(param.grad).sum().item()
                nan_percentage = (nan_count / total_elements) * 100 if total_elements > 0 else 0
                inf_percentage = (inf_count / total_elements) * 100 if total_elements > 0 else 0

                error_msg = (
                    f"{name}梯度数值不稳定:\n"
                    f"- 形状: {param.grad.shape}\n"
                    f"- NaN: {nan_count}/{total_elements} ({nan_percentage:.2f}%)\n"
                    f"- Inf: {inf_count}/{total_elements} ({inf_percentage:.2f}%)"
                )

                logger.error(error_msg)
                invalid_params.append(name)

        return len(invalid_params) == 0, invalid_params

    def handle_exception(self, e: Exception, context: str = "") -> tuple[bool, bool]:
        """
        处理异常，记录详细信息

        Args:
            e: 异常对象
            context: 上下文信息

        Returns:
            Tuple[bool, bool]: (是否应该继续执行, 是否是OOM异常)

        注意：在开发阶段，任何异常都会导致程序立即停止执行，返回值始终为(False, is_oom)
        """
        # 获取异常类型
        error_type = type(e).__name__
        error_msg_str = str(e)

        # 检查是否是OOM异常
        is_oom = error_type == "RuntimeError" and "CUDA out of memory" in error_msg_str

        # 更新时间戳
        current_time = time.time()
        self.error_timestamps[error_type] = current_time
        self.last_error_type = error_type

        # 增加错误计数
        self.error_count += 1

        # 构建错误消息
        error_msg = f"异常: {error_type}: {error_msg_str}"
        if context:
            error_msg = f"{context} - {error_msg}"

        # 记录堆栈跟踪
        if self.enable_traceback:
            error_msg += f"\n堆栈跟踪:\n{traceback.format_exc()}"

        # 记录错误 - 开发阶段所有错误都使用error级别
        logger.error(error_msg)

        # 记录系统信息
        if torch.cuda.is_available():
            try:
                gpu_info = (
                    f"GPU状态:\n"
                    f"- 设备: {torch.cuda.get_device_name(0)}\n"
                    f"- 内存分配: {torch.cuda.memory_allocated() / 1024**2:.2f} MB\n"
                    f"- 内存缓存: {torch.cuda.memory_reserved() / 1024**2:.2f} MB\n"
                    f"- 最大内存分配: {torch.cuda.max_memory_allocated() / 1024**2:.2f} MB"
                )
                logger.error(gpu_info)
            except Exception as gpu_e:
                logger.error(f"获取GPU信息失败: {gpu_e!s}")

        # 开发阶段，任何异常都会导致程序立即停止执行
        logger.critical("检测到异常，立即停止执行")

        # 始终返回False，表示不应该继续执行
        return False, is_oom

    def early_stop_on_nan(self, loss: torch.Tensor, step: int, epoch: int) -> bool:
        """
        检查损失值是否为NaN或Inf，如果是则提前停止训练

        Args:
            loss: 损失值
            step: 当前步骤
            epoch: 当前轮次

        Returns:
            bool: 是否应该停止训练

        注意：在开发阶段，任何NaN/Inf都会导致程序立即停止执行，返回值始终为True
        """
        if not self.enable_nan_detection:
            return False

        if torch.isnan(loss).any() or torch.isinf(loss).any():
            error_msg = (
                f"检测到NaN/Inf损失值，立即停止训练:\n"
                f"- 轮次: {epoch}\n"
                f"- 步骤: {step}\n"
                f"- 损失值: {loss.item() if not torch.isnan(loss).all() and not torch.isinf(loss).all() else 'NaN/Inf'}"
            )
            logger.error(error_msg)
            logger.critical("检测到NaN/Inf，立即停止执行")

            # 增加错误计数
            self.error_count += 1

            # 开发阶段，任何NaN/Inf都会导致程序立即停止执行
            return True

        return False

    def reset(self):
        """重置错误计数和时间戳"""
        self.error_count = 0
        self.error_timestamps = {}
        self.last_error_type = None
        logger.info("错误监控已重置")


# 创建全局实例
error_monitor = ErrorMonitor()

def configure_error_monitor(
    log_path: str | None = None,
    enable_nan_detection: bool = True,
    enable_traceback: bool = True,
    max_errors: int = 5,
    error_cooldown: int = 60
):
    """
    配置全局错误监控实例

    Args:
        log_path: 错误日志文件路径
        enable_nan_detection: 是否启用NaN检测
        enable_traceback: 是否记录完整堆栈信息
        max_errors: 最大错误数量
        error_cooldown: 错误冷却时间（秒）
    """
    global error_monitor
    error_monitor = ErrorMonitor(
        log_path=log_path,
        enable_nan_detection=enable_nan_detection,
        enable_traceback=enable_traceback,
        max_errors=max_errors,
        error_cooldown=error_cooldown
    )
    return error_monitor
