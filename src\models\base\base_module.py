"""基础模块 - 提供深度学习模型的基础功能

本模块提供了深度学习模型的基础功能，包括：
1. 参数初始化
2. 设备管理
3. 梯度检查
4. 张量验证
5. 日志记录
"""

import traceback

import torch
from torch import nn

from src.utils.cuda import cuda_manager
from src.utils.logger import get_logger


class BaseModule(nn.Module):
    """基础模块 - 提供深度学习模型的基础功能"""

    def __init__(self, name: str = "BaseModule"):
        """初始化基础模块

        Args:
            name: 模块名称
        """
        super().__init__()
        self.name = name
        self.logger = get_logger(name)

        # 必须获取有效的设备配置
        if cuda_manager is None:
            raise RuntimeError("cuda_manager未初始化")

        try:
            self.device = cuda_manager.device
        except Exception as e:
            raise RuntimeError(f"获取设备失败: {e!s}") from e

        # 记录初始化信息
        self.logger.debug(f"{name} 初始化开始 - 设备: {self.device}")

    def _init_weights(self, module):
        """初始化模块权重

        Args:
            module: 要初始化的模块

        Raises:
            ValueError: 如果模块类型不支持自定义初始化
        """
        try:
            # 定义支持的模块类型列表
            supported_modules = [
                # PyTorch标准模块
                nn.Linear, nn.Conv1d, nn.LayerNorm, nn.GRU, nn.LSTM,
                # 以下是不需要自定义初始化的模块类型
                nn.LeakyReLU, nn.ReLU, nn.Sigmoid, nn.Tanh, nn.Dropout, nn.Softplus,
                nn.Sequential, nn.ModuleList, nn.ModuleDict, nn.Identity, nn.MultiheadAttention,
                # 自定义模块类型（通过类名字符串匹配）
                'DynamicFeatureFusion', 'FeatureEncoder', 'NoiseEncoder', 'NoiseProcessor',
                'SequenceGenerator', 'MultiScaleFeatureExtractor', 'MultiHeadAttention',
                'MultiLayerAttention', 'TemporalCoherence', 'OutputProjection',
                # 新增的自定义模块类型（来自警告日志）
                'CausalAttention', 'LeadingSignalAmplifier', 'AdaptiveTimeWindow',
                'HierarchicalTemporalEncoder', 'TemporalResidualConnection', 'MultiScaleTemporalModule',
                'TemporalMultiHeadWrapper', 'ConditionalNoiseGenerator', 'LayeredNoiseInjector',
                'UncertaintyAwareNoise', 'AdaptiveNoiseModule',
                # 修复：添加自定义激活函数支持
                'Swish'
            ]

            # 检查模块类型是否支持
            module_type_supported = False
            module_class_name = module.__class__.__name__

            for supported_type in supported_modules:
                # 如果是类型对象，使用isinstance检查
                if (isinstance(supported_type, type) and isinstance(module, supported_type)) or (isinstance(supported_type, str) and module_class_name == supported_type):
                    module_type_supported = True
                    break

            # 如果模块类型不支持，抛出异常而不是警告
            if not module_type_supported:
                error_msg = f"模块类型 {module_class_name} 不在支持的初始化类型列表中: {supported_modules}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # 对支持的模块类型进行初始化
            if isinstance(module, nn.Linear):
                # 线性层使用Kaiming初始化
                nn.init.kaiming_normal_(module.weight, a=0.01, mode='fan_in', nonlinearity='leaky_relu')
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
                self.logger.debug(f"初始化线性层: {module} - Kaiming初始化")
            elif isinstance(module, nn.Conv1d):
                # 一维卷积层使用Kaiming初始化
                nn.init.kaiming_normal_(module.weight, a=0.01, mode='fan_in', nonlinearity='leaky_relu')
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
                self.logger.debug(f"初始化一维卷积层: {module} - Kaiming初始化")
            elif isinstance(module, nn.LayerNorm):
                # 层归一化使用常数初始化
                nn.init.constant_(module.weight, 1.0)
                nn.init.constant_(module.bias, 0)
                self.logger.debug(f"初始化层归一化层: {module} - 常数初始化")
            elif isinstance(module, nn.GRU | nn.LSTM):
                # 循环层使用正交初始化
                for name, param in module.named_parameters():
                    if 'weight' in name:
                        nn.init.orthogonal_(param)
                    elif 'bias' in name:
                        nn.init.constant_(param, 0)
                self.logger.debug(f"初始化循环层: {module.__class__.__name__} - 正交初始化")
            # 其他支持的模块类型不需要特殊初始化
            elif isinstance(module, nn.LeakyReLU | nn.ReLU | nn.Sigmoid | nn.Tanh | nn.Dropout | nn.Softplus | nn.Sequential | nn.ModuleList | nn.ModuleDict | nn.Identity | nn.MultiheadAttention):
                # 这些模块不需要初始化权重
                self.logger.debug(f"模块 {module.__class__.__name__} 不需要初始化权重")
            # 自定义模块类型不需要特殊初始化（因为它们已经在各自的__init__中初始化了）
            elif module_class_name in [t for t in supported_modules if isinstance(t, str)]:
                # 这些模块已经在各自的__init__中初始化了
                self.logger.debug(f"自定义模块 {module_class_name} 已在其__init__中初始化")
        except Exception as e:
            self.logger.error(f"初始化权重失败: {module.__class__.__name__} - {e!s}")
            raise

    def count_parameters(self) -> int:
        """计算模型参数数量

        Returns:
            int: 参数数量
        """
        try:
            trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
            total_params = sum(p.numel() for p in self.parameters())
            self.logger.debug(f"模型 {self.name} 参数统计: 可训练参数={trainable_params:,}, 总参数={total_params:,}, 可训练比例={trainable_params/max(1,total_params)*100:.2f}%")
            return trainable_params
        except Exception as e:
            self.logger.error(f"统计模型参数失败: {e!s}")
            return 0

    def validate_tensor(self, tensor: torch.Tensor, name: str, expected_dims: int | None = None):
        """验证张量

        Args:
            tensor: 要验证的张量
            name: 张量名称
            expected_dims: 期望的维度数

        Raises:
            ValueError: 如果张量无效
        """
        if tensor is None:
            raise ValueError(f"{name} 不能为None")

        if not isinstance(tensor, torch.Tensor):
            raise TypeError(f"{name} 必须是torch.Tensor类型，当前类型: {type(tensor)}")

        if expected_dims is not None and tensor.dim() != expected_dims:
            raise ValueError(f"{name} 维度必须为{expected_dims}，当前维度: {tensor.dim()}")

        if torch.isnan(tensor).any():
            raise ValueError(f"{name} 包含NaN值")

        if torch.isinf(tensor).any():
            raise ValueError(f"{name} 包含Inf值")

    def _check_numerical_stability(self, tensor: torch.Tensor, name: str, fix_tensor: bool = False) -> torch.Tensor | None:
        """检查数值稳定性

        Args:
            tensor: 要检查的张量
            name: 张量名称
            fix_tensor: 是否修复张量中的NaN和Inf值，子类可能会实现此功能

        Returns:
            Optional[torch.Tensor]: 如果fix_tensor为True且子类实现了修复功能，可能返回修复后的张量；否则返回None

        Raises:
            ValueError: 如果张量包含NaN或Inf值且fix_tensor为False
        """
        has_nan = torch.isnan(tensor).any()
        has_inf = torch.isinf(tensor).any()

        if has_nan or has_inf:
            if not fix_tensor:
                if has_nan:
                    raise ValueError(f"{name} 包含NaN值")
                if has_inf:
                    raise ValueError(f"{name} 包含Inf值")
            else:
                # 基类不实现修复功能，返回None
                # 子类可以重写此方法并返回修复后的张量
                self.logger.warning(f"{name} 包含NaN或Inf值，但基类不实现修复功能")

        return None

    def _log_tensor_info(self, tensor: torch.Tensor, name: str):
        """记录张量信息

        Args:
            tensor: 要记录的张量
            name: 张量名称

        Raises:
            RuntimeError: 当张量类型不正确或计算统计量失败时
        """
        try:
            # 检查张量类型
            if tensor.dtype == torch.bool:
                error_msg = f"{name} 类型为 bool，无法计算统计量"
                self.logger.error(error_msg)
                raise RuntimeError(error_msg)

            # 记录张量信息
            self.logger.debug(
                f"{name} 信息:\n"
                f"- 形状: {tensor.shape}\n"
                f"- 设备: {tensor.device}\n"
                f"- 类型: {tensor.dtype}\n"
                f"- 均值: {tensor.mean().item():.4f}\n"
                f"- 标准差: {tensor.std().item():.4f}\n"
                f"- 最小值: {tensor.min().item():.4f}\n"
                f"- 最大值: {tensor.max().item():.4f}"
            )
        except Exception as e:
            error_msg = f"{name} 信息记录失败: {e!s}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    def _handle_error(self, error: Exception, message: str):
        """处理错误

        Args:
            error: 异常对象
            message: 错误消息

        Raises:
            RuntimeError: 在记录错误日志后抛出异常
        """
        error_msg = f"{message}: {error!s}\n{traceback.format_exc()}"
        self.logger.error(error_msg)
        # 在记录错误日志后抛出异常
        raise RuntimeError(error_msg) from error

    def _check_shape_compatibility(self, *tensors, expected_shapes=None):
        """检查张量形状兼容性

        Args:
            *tensors: 要检查的张量
            expected_shapes: 期望的形状

        Raises:
            ValueError: 如果张量形状不兼容
        """
        if expected_shapes is None:
            return

        for i, (tensor, expected_shape) in enumerate(zip(tensors, expected_shapes, strict=False)):
            if tensor is None:
                continue

            actual_shape = tensor.shape

            # 检查维度数
            if len(actual_shape) != len(expected_shape):
                # 特殊处理：如果期望是1D但实际是3D，且最后一个维度匹配
                if len(expected_shape) == 1 and len(actual_shape) == 3 and expected_shape[0] == actual_shape[-1]:
                    continue
                else:
                    raise ValueError(
                        f"张量{i}维度数不匹配: "
                        f"期望{len(expected_shape)}维，实际{len(actual_shape)}维"
                    )

            # 检查每个维度
            for dim, (actual, expected) in enumerate(zip(actual_shape, expected_shape, strict=False)):
                if expected is not None and actual != expected:
                    # 特殊处理：如果期望是1D但实际是3D，且最后一个维度匹配
                    if len(expected_shape) == 1 and len(actual_shape) == 3 and dim == 2 and expected == actual:
                        continue
                    else:
                        raise ValueError(
                            f"张量{i}在维度{dim}上不匹配: "
                            f"期望{expected}，实际{actual}"
                        )

    def _check_device_consistency(self, *tensors):
        """检查张量设备一致性

        Args:
            *tensors: 要检查的张量

        Raises:
            ValueError: 如果张量设备不一致
        """
        if not tensors:
            return

        reference_device = tensors[0].device

        for i, tensor in enumerate(tensors[1:], 1):
            if tensor is None:
                continue

            if tensor.device != reference_device:
                raise ValueError(
                    f"张量{i}设备不一致: "
                    f"期望{reference_device}，实际{tensor.device}"
                )
