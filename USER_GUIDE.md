# 时间序列预测系统使用指南

本文档详细介绍了时间序列预测系统的各种运行模式和使用方法。

## 系统概述

本系统是一个基于GAN的时间序列预测系统，专注于预测value15指标。系统提供了完整的数据处理、模型训练、评估和预测功能，通过分析其他特征的先行信号来预测value15的未来变化。

## 运行模式

系统提供了四种主要的运行模式，必须通过命令行参数 `--mode` 显式指定：

### 1. 数据处理模式 (process)

数据处理模式专门用于执行数据处理流水线，而不进行训练或预测。这个模式对于数据探索、特征工程实验和数据质量检查非常有用。

#### 命令格式

```bash
# 执行数据处理并保存结果到指定目录
python main.py --mode process --config config.yaml --output_dir my_processed_data --save_processed
```

#### 必需参数

- `--mode process`: 指定数据处理模式
- `--config`: 配置文件路径
- `--output_dir`: 处理后数据的保存目录

#### 可选参数

- `--save_processed`: 是否保存处理后的数据

#### 数据处理流程

数据处理流程包括以下8个主要步骤：

1. **数据加载**：从原始数据源加载数据
2. **数据验证**：验证数据质量和完整性
3. **数据清洗**：处理缺失值和异常值
4. **特征编码**：对分类特征进行编码
5. **特征工程**：创建新的衍生特征
6. **特征选择**：选择最相关的特征子集
7. **滑动窗口创建**：创建时间序列窗口
8. **模型输入准备**：准备模型所需的输入格式

#### 数据清洗配置

可以通过配置文件控制数据清洗行为：

```yaml
data:
  preprocessing:
    cleaning:
      outlier_threshold: 3.0  # 异常值阈值(基于MAD)
      missing_value_strategy: "median"  # 缺失值处理策略(median/mean/drop)
```

#### 输出文件

当使用 `--save_processed` 参数时，系统会生成以下文件：

- **processed_data.npz**：NumPy压缩格式的处理后数据，包含特征和目标数组
- **processed_data.pkl**：Python Pickle格式的完整数据，包含特征、目标和元数据
- **README.txt**：详细的数据处理结果说明，包括数据形状、处理步骤和使用方法

### 2. 训练模式 (train)

训练模式用于训练GAN模型，以便进行时间序列预测。

#### 命令格式

```bash
# 使用指定配置文件训练模型
python main.py --mode train --config config.yaml

# 从检查点恢复训练
python main.py --mode train --config config.yaml --resume --checkpoint outputs/models/GAN_epoch_5.pt
```

#### 必需参数

- `--mode train`: 指定训练模式
- `--config`: 配置文件路径

#### 可选参数

- `--resume`: 是否从检查点恢复训练
- `--checkpoint`: 检查点路径（用于恢复训练）

#### 训练流程

训练过程包括以下主要步骤：

1. 加载配置和初始化资源
2. 数据准备（执行完整的数据处理流水线）
3. 模型初始化（生成器和判别器）
4. 训练循环
   - 生成器训练
   - 判别器训练
   - 验证评估
   - 检查点保存
5. 最终模型保存

#### 训练参数配置

可以通过修改 `config.yaml` 中的 `training` 部分来自定义训练参数：

```yaml
training:
  batch_size: 128              # 批大小
  num_epochs: 100              # 训练轮数
  num_workers: 0               # 数据加载工作进程数
  optimizer:
    generator_lr: 0.001        # 生成器学习率
    discriminator_lr: 0.001    # 判别器学习率
    beta1: 0.5                 # Adam优化器参数
    beta2: 0.999               # Adam优化器参数
  lr_scheduler:
    enabled: true              # 启用学习率调度器
    factor: 0.9                # 学习率衰减因子
    patience: 3                # 等待轮数
  early_stopping:
    enabled: true              # 启用早停
    patience: 3                # 早停等待轮数
    min_delta: 1e-4            # 最小改进阈值
```

### 3. 评估模式 (evaluate)

评估模式用于评估已训练模型的性能。

#### 命令格式

```bash
# 评估指定模型
python main.py --mode evaluate --config config.yaml --model outputs/models/GAN_epoch_10.pt
```

#### 必需参数

- `--mode evaluate`: 指定评估模式
- `--config`: 配置文件路径
- `--model`: 模型路径

#### 评估指标

系统专注于计算核心评估指标：

- **MAE（平均绝对误差）**：预测值与实际值绝对差异的平均值，是主要的评估指标

#### 评估配置

可以通过配置文件控制评估行为：

```yaml
evaluation:
  batch_size: 128              # 评估批大小
  metrics: ["mae"]             # 计算的评估指标（专注于MAE）
  max_value: 1e6               # 最大值限制
  min_variance: 1e-6           # 最小方差阈值
```

#### 输出结果

评估结果将保存在 `outputs/results/evaluation_results.txt` 文件中，同时在控制台显示。

### 4. 预测模式 (predict)

预测模式用于使用训练好的模型进行时间序列预测。

#### 命令格式

```bash
# 对指定输入数据进行预测并保存到指定位置
python main.py --mode predict --config config.yaml --model outputs/models/GAN_epoch_10.pt --input data/test.csv --output outputs/predictions
```

#### 必需参数

- `--mode predict`: 指定预测模式
- `--config`: 配置文件路径
- `--model`: 模型路径
- `--input`: 输入数据路径
- `--output`: 输出结果路径

#### 预测流程

预测过程包括以下主要步骤：

1. 加载配置和模型
2. 数据准备（执行数据处理流水线）
3. 模型预测
4. 结果保存

#### 预测配置

可以通过配置文件控制预测行为：

```yaml
prediction:
  batch_size: 128              # 预测批大小
  confidence_level: 0.95       # 置信水平
  return_confidence: true      # 是否返回置信区间
  n_samples: 100               # 采样数量
  num_workers: 0               # 工作进程数
```

#### 输出结果

预测结果将保存为NumPy数组格式（.npy文件），可以使用以下代码加载：

```python
import numpy as np
predictions = np.load('outputs/predictions/predictions.npy')
```

## 配置文件说明

系统使用 `config.yaml` 进行配置，主要配置项包括：

### 数据配置

```yaml
data:
  window_size: 48              # 时间窗口大小
  stride: 8                    # 滑动步长
  train_ratio: 0.7             # 训练集比例
  val_ratio: 0.15              # 验证集比例
  test_ratio: 0.15             # 测试集比例
  target: value15              # 目标变量
  batch_size: 128              # 批大小
  num_workers: 0               # 数据加载工作进程数
  preprocessing:
    cleaning:
      outlier_threshold: 3.0   # 异常值阈值
      missing_value_strategy: median  # 缺失值处理策略
```

### 模型配置

```yaml
model:
  type: gan                    # 模型类型
  generator_type: lstm         # 生成器类型
  hidden_dim: 48               # 隐藏层维度
  noise_dim: 48                # 噪声维度
  num_layers: 2                # 层数
  dropout_rate: 0.297          # Dropout率
  generator:
    num_layers: 2              # 生成器层数
    activation_type: tanh      # 激活函数
    use_layer_norm: true       # 是否使用层归一化
    use_residual: true         # 是否使用残差连接
  discriminator:
    hidden_dim: 96             # 判别器隐藏层维度
    num_layers: 4              # 判别器层数
    learning_rate: 0.0002      # 判别器学习率
```

### 训练配置

```yaml
training:
  batch_size: 128              # 批大小
  num_epochs: 100              # 训练轮数
  num_workers: 0               # 工作进程数
  optimizer:
    generator_lr: 0.001        # 生成器学习率
    discriminator_lr: 0.001    # 判别器学习率
    beta1: 0.5                 # Adam优化器参数
    beta2: 0.999               # Adam优化器参数
  lr_scheduler:
    enabled: true              # 启用学习率调度器
    factor: 0.9                # 衰减因子
    patience: 3                # 等待轮数
  early_stopping:
    enabled: true              # 启用早停
    patience: 3                # 早停等待轮数
    min_delta: 1e-4            # 最小改进阈值
```

### 路径配置

```yaml
paths:
  raw_data: data/raw/combined_data.csv    # 原始数据路径
  model_dir: outputs/models               # 模型保存目录
  logs_dir: logs                          # 日志保存目录
  results_dir: outputs/results            # 结果保存目录
  checkpoint_dir: outputs/checkpoints     # 检查点保存目录
```

### 系统配置

```yaml
system:
  device: cuda                 # 计算设备
  cuda:
    enabled: true              # 启用CUDA
    device_id: 0               # GPU设备ID
    memory_fraction: 0.95      # 内存使用比例
```

### 日志配置

```yaml
logging:
  level: DEBUG                 # 日志级别
  console:
    enabled: true              # 启用控制台输出
    level: DEBUG               # 控制台日志级别
  file:
    enabled: true              # 启用文件输出
    level: DEBUG               # 文件日志级别
    path: logs/app.log         # 日志文件路径
```

## 常见问题解答

### 参数错误

#### 缺少必需参数

如果遇到"必须提供--output_dir参数"等错误，请检查：

1. 确保为每种模式提供了所有必需参数
2. 参数名称拼写正确
3. 参数值格式正确

#### 参数组合错误

不同模式需要不同的参数组合：

- **数据处理模式**：`--mode process --config --output_dir` (可选：`--save_processed`)
- **训练模式**：`--mode train --config` (可选：`--resume --checkpoint`)
- **评估模式**：`--mode evaluate --config --model`
- **预测模式**：`--mode predict --config --model --input --output`

**注意**：由于当前代码实现的限制，某些参数在命令行中被标记为必需，但实际上只在特定模式下使用。如果遇到参数错误，请为所有模式提供所有必需参数，系统会自动忽略不相关的参数。

### 内存和性能问题

#### 内存使用率超过限制

如果遇到"内存使用率超过限制"的错误，可以尝试：

1. 减小批大小（`training.batch_size`）
2. 减小窗口大小（`data.window_size`）
3. 增加滑动步长（`data.stride`）
4. 调整CUDA内存使用比例（`system.cuda.memory_fraction`）
5. 减少特征数量

#### GPU内存不足

如果遇到CUDA内存错误：

1. 降低批大小
2. 减少模型复杂度（隐藏层维度、层数）
3. 启用梯度检查点（如果支持）
4. 使用CPU模式（设置`system.device: cpu`）

### 模型训练问题

#### 模型训练不稳定

GAN模型训练可能不稳定，可以尝试：

1. 调整学习率（`training.optimizer.generator_lr`和`discriminator_lr`）
2. 启用学习率调度器（`training.lr_scheduler.enabled: true`）
3. 使用梯度裁剪（`training.gradient_clip_val`）
4. 调整判别器和生成器的平衡参数
5. 启用早停机制（`training.early_stopping.enabled: true`）

#### 训练过程中出现NaN

如果训练过程中出现NaN值：

1. 降低学习率
2. 检查数据是否包含异常值
3. 启用梯度裁剪
4. 检查模型初始化

### 数据相关问题

#### 数据加载失败

如果数据加载失败：

1. 检查数据文件路径是否正确（`paths.raw_data`）
2. 确保数据文件格式正确（CSV格式）
3. 检查数据文件是否包含必需的列
4. 验证数据文件编码格式

#### 预测结果不准确

如果预测结果不准确，可以尝试：

1. 增加训练轮数（`training.num_epochs`）
2. 调整模型复杂度
3. 改进特征工程配置
4. 调整数据预处理参数
5. 使用更多的训练数据

## 高级用法

### 自定义数据处理

可以通过修改配置文件来自定义数据处理流程：

#### 特征工程配置

```yaml
feature_engineering:
  enable: true
  time_series_features:
    lag_features:
      enable: true
      max_lag: 10              # 最大滞后期
    diff_features:
      enable: true
      orders: [1, 2]           # 差分阶数
    window_features:
      enable: true
      window_sizes: [3, 12, 24]  # 窗口大小
```

#### 特征选择配置

```yaml
feature_selection:
  enable: true
  method: "default_multi_stage"
  importance_threshold: 0.01   # 重要性阈值
  lagged_corr:
    min_abs_corr: 0.1         # 最小相关性
    max_lag: 28               # 最大滞后期
```

### 自定义模型架构

可以通过配置文件调整模型架构：

#### 生成器配置

```yaml
model:
  generator:
    num_layers: 2             # 层数
    activation_type: tanh     # 激活函数
    use_layer_norm: true      # 层归一化
    use_residual: true        # 残差连接
```

#### 判别器配置

```yaml
model:
  discriminator:
    hidden_dim: 96            # 隐藏层维度
    num_layers: 4             # 层数
    use_spectral_norm: true   # 谱归一化
```

### 监控和调试

#### 启用详细日志

```yaml
logging:
  level: DEBUG                # 设置为DEBUG级别
  module_levels:
    src.models.gan: DEBUG     # 特定模块的日志级别
```

#### 启用监控

```yaml
monitoring:
  enable_model_stats: true    # 模型统计监控
  enable_error_monitor: true  # 错误监控
  stats_log_frequency: 10     # 统计日志频率
```

### 性能优化

#### 批大小优化

```yaml
training:
  batch_size_optimizer:
    enabled: true             # 启用动态批大小优化
    initial_batch_size: 128   # 初始批大小
    memory_utilization_target: 0.8  # 内存使用目标
```

#### 学习率平衡

```yaml
training:
  lr_balancer:
    enabled: true             # 启用学习率平衡
    type: "enhanced_multi_metric"  # 平衡器类型
    target_ratio: 1.2         # 目标比率
```

## 快速开始示例

### 基本训练流程

```bash
# 1. 数据处理（可选，用于检查数据）
# 注意：由于参数解析限制，需要提供所有必需参数，但系统会忽略不相关的参数
python main.py --mode process --config config.yaml --output_dir processed_data --save_processed --model dummy --input dummy --output dummy

# 2. 训练模型
python main.py --mode train --config config.yaml --output_dir dummy --model dummy --input dummy --output dummy

# 3. 评估模型
python main.py --mode evaluate --config config.yaml --model outputs/models/GAN_epoch_10.pt --output_dir dummy --input dummy --output dummy

# 4. 进行预测
python main.py --mode predict --config config.yaml --model outputs/models/GAN_epoch_10.pt --input data/test.csv --output outputs/predictions --output_dir dummy
```

### 从检查点恢复训练

```bash
# 从特定检查点恢复训练
python main.py --mode train --config config.yaml --resume --checkpoint outputs/checkpoints/GAN_epoch_5.pt --output_dir dummy --model dummy --input dummy --output dummy
```

### 推荐使用方式：run.py脚本

为了避免参数冗余问题，系统提供了`run.py`便捷脚本，它会自动处理参数要求：

```bash
# 1. 数据处理
python run.py process --config config.yaml --output_dir processed_data --save_processed

# 2. 训练模型
python run.py train --config config.yaml

# 3. 从检查点恢复训练
python run.py train --config config.yaml --resume --checkpoint outputs/checkpoints/GAN_epoch_5.pt

# 4. 评估模型
python run.py evaluate --config config.yaml --model outputs/models/GAN_epoch_10.pt

# 5. 进行预测
python run.py predict --config config.yaml --model outputs/models/GAN_epoch_10.pt --input data/test.csv --output outputs/predictions
```

#### run.py脚本的优势

1. **简化参数**：只需要提供相关模式的必要参数
2. **自动处理**：自动添加虚拟参数以满足main.py的要求
3. **错误检查**：提供更友好的错误提示
4. **使用方便**：命令行更简洁易用

## 注意事项

1. **数据格式**：确保输入数据为CSV格式，包含日期列和数值特征列
2. **GPU内存**：根据GPU内存大小调整批大小和模型复杂度
3. **配置文件**：修改配置文件后建议先进行数据处理模式测试
4. **检查点**：训练过程中会自动保存检查点，可用于恢复训练
5. **日志监控**：查看日志文件了解训练进度和潜在问题

## 版本信息

- 系统版本：2.0.0
- 支持的Python版本：3.8+
- 主要依赖：PyTorch, NumPy, Pandas, YAML

---

*本文档最后更新时间：2024年*
