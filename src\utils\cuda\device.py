"""CUDA 设备管理模块"""

from typing import Any

import torch

from src.utils.logger import get_logger

from .types import CUDAInitError


class CUDADeviceManager:
    """CUDA设备管理器，负责设备初始化和基本操作"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        """初始化设备管理器"""
        self._logger = get_logger(__name__)
        self._initialized = False
        self._device = None
        self._initialize_device()

    def _initialize_device(self):
        """初始化CUDA设备"""
        try:
            # 检查是否禁用了CUDA
            import os
            if os.environ.get('CUDA_VISIBLE_DEVICES') == '':
                self._logger.info("CUDA已通过环境变量禁用，使用CPU模式")
                self._device = torch.device('cpu')
                self._initialized = True
                return

            # 1. 检查CUDA可用性
            if not torch.cuda.is_available():
                error_msg = "CUDA不可用，这是一个关键错误"
                self._logger.error(error_msg)
                raise CUDAInitError(error_msg)

            if torch.cuda.device_count() == 0:
                error_msg = "未检测到可用CUDA设备，这是一个关键错误"
                self._logger.error(error_msg)
                raise CUDAInitError(error_msg)

            # 2. 初始化CUDA设备
            self._device = torch.device('cuda:0')
            torch.cuda.init()

            # 3. 预热GPU
            self._warmup_gpu()

            # 4. 记录设备信息
            device_name = torch.cuda.get_device_name(0)
            total_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            cuda_version = f"{torch.__version__} (CUDA)"

            self._logger.info(
                f"CUDA设备初始化成功:\n"
                f"- 设备名称: {device_name}\n"
                f"- 总内存: {total_memory:.1f}GB\n"
                f"- CUDA版本: {cuda_version}\n"
                f"- 设备索引: 0"
            )

            # 5. 验证设备可用性
            self._verify_device()
            self._initialized = True

        except Exception as e:
            error_msg = f"CUDA初始化失败: {e!s}"
            self._logger.error(error_msg)
            raise CUDAInitError(error_msg)

    def _verify_device(self):
        """验证CUDA设备可用性"""
        try:
            # 创建测试张量
            test_tensor = torch.tensor([1.0], device=self._device)
            if test_tensor.device.type != 'cuda':
                raise CUDAInitError("CUDA设备验证失败")

            # 验证基本运算
            result = test_tensor + test_tensor
            if not torch.allclose(result, torch.tensor([2.0], device=self._device)):
                raise CUDAInitError("CUDA基本运算验证失败")

            self._logger.debug("CUDA设备验证通过")

        except Exception as e:
            raise CUDAInitError(f"设备验证失败: {e!s}")

    def _warmup_gpu(self):
        """预热GPU，确保CUDA上下文正确初始化"""
        try:
            # 创建测试张量，设置requires_grad=True
            x = torch.randn(100, 100, device=self._device, requires_grad=True)
            y = torch.randn(100, 100, device=self._device, requires_grad=True)

            # 执行一些基本运算
            z = torch.matmul(x, y)
            z = torch.relu(z)
            loss = z.mean()

            # 执行反向传播
            loss.backward()

            # 清理
            del x, y, z, loss
            torch.cuda.empty_cache()

            self._logger.debug(
                f"GPU预热完成:\n"
                f"- 设备: {torch.cuda.get_device_name(0)}"
            )

        except Exception as e:
            raise CUDAInitError(f"GPU预热失败: {e!s}")

    @property
    def device(self) -> torch.device:
        """获取当前设备"""
        if self._device is None:
            raise CUDAInitError("CUDA设备未完成初始化")
        return self._device

    @property
    def is_cuda_available(self) -> bool:
        """检查CUDA是否可用"""
        return self._device is not None and self._device.type == 'cuda'

    @property
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self._initialized

    def get_device_info(self) -> dict[str, Any]:
        """获取设备详细信息"""
        if not self.is_cuda_available:
            return {'device_type': 'cpu', 'device_name': 'CPU'}

        try:
            device_props = torch.cuda.get_device_properties(0)  # 使用设备索引0而不是self._device
            return {
                'device_type': 'cuda',
                'device_name': device_props.name,
                'device_index': 0,  # 直接使用0而不是self._device.index
                'compute_capability': f"{device_props.major}.{device_props.minor}",
                'total_memory_gb': device_props.total_memory / (1024**3),
                'cuda_version': f"{torch.__version__} (CUDA)",
                'cudnn_version': torch.backends.cudnn.version() if torch.backends.cudnn.is_available() else 'N/A',
                'cudnn_enabled': torch.backends.cudnn.enabled,
                'multi_processor_count': device_props.multi_processor_count
            }
        except Exception as e:
            self._logger.error(f"获取设备信息失败: {e!s}")
            raise RuntimeError(f"获取设备信息失败: {e!s}")

    def move_to_device(self, tensor: torch.Tensor, non_blocking: bool = False, stream: torch.cuda.Stream | None = None) -> torch.Tensor:
        """移动张量到当前设备

        Args:
            tensor: 要移动的张量
            non_blocking: 是否使用非阻塞传输
            stream: 指定的CUDA流，如果为None则使用当前流

        Returns:
            移动后的张量
        """
        if not isinstance(tensor, torch.Tensor):
            raise TypeError(f"输入必须是torch.Tensor, 实际是 {type(tensor)}")

        if not self.is_cuda_available:
            error_msg = "CUDA不可用，无法移动张量到GPU"
            self._logger.error(error_msg)
            raise RuntimeError(error_msg)

        if tensor.device == self.device:
            return tensor

        try:
            # 如果指定了流，在该流上下文中执行传输
            if stream is not None and self.is_cuda_available:
                with torch.cuda.stream(stream):
                    moved = tensor.to(self.device, non_blocking=non_blocking)
            else:
                moved = tensor.to(self.device, non_blocking=non_blocking)

            self._logger.debug(f"已将张量从{tensor.device}移动到{self.device} (non_blocking={non_blocking})")
            return moved
        except Exception as e:
            self._logger.error(f"移动张量到设备失败: {e!s}")
            raise RuntimeError(f"移动张量到设备失败: {e!s}")


# 全局单例
try:
    cuda_device_manager = CUDADeviceManager()
except Exception as e:
    logger = get_logger("CUDADeviceManager")
    logger.error(f"CUDA设备管理器初始化失败: {e!s}")
    raise RuntimeError(f"CUDA设备管理器初始化失败: {e!s}")
