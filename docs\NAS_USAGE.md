# 神经架构搜索 (NAS) 使用指南

## 概述

本项目实现了专门针对多通道时序预测GAN的神经架构搜索功能，能够自动发现比手工设计更优的网络结构。

## 功能特性

### 🔍 搜索空间
- **生成器架构**: 层数(2-5层)、隐藏维度(64-256)、注意力头数(2-8)、激活函数类型
- **判别器架构**: 分支结构组合、注意力机制选择、层数和宽度配置
- **特征编码器**: 多尺度卷积核大小、时序建模窗口大小、先行信号放大因子
- **训练策略**: 优化器类型(4种)、学习率配置、训练平衡策略、学习率调度器(5种)
- **损失函数**: 损失权重组合、梯度惩罚参数、损失类型(4种)、损失稳定性配置
- **正则化**: Dropout配置、权重正则化、噪声注入、归一化类型(4种)

### 🚀 搜索策略
- **可微分架构搜索 (DARTS)**: 基于梯度的高效搜索
- **进化算法**: 基于种群的全局搜索
- **早停机制**: 快速筛选劣质架构
- **资源约束**: 支持GPU显存和时间预算限制

### 📊 评估指标
- **主要指标**: MAE在测试集上的表现
- **辅助指标**: 训练稳定性、推理速度、模型参数量
- **复合指标**: 平衡预测精度与计算效率

## 快速开始

### 1. 基本使用

```python
from src.optimization.nas.integration import GANTrainerWithNAS
from src.optimization.nas.manager import NASConfig
from src.utils.config_manager import ConfigManager

# 加载配置
config = ConfigManager("configs/nas_config.yaml")

# 创建NAS配置
nas_config = NASConfig(
    search_strategy="evolutionary",
    max_iterations=30,
    time_budget_hours=8.0,
    population_size=15
)

# 创建NAS训练器
trainer = GANTrainerWithNAS(
    config=config,
    train_loader=train_loader,
    val_loader=val_loader,
    nas_config=nas_config,
    enable_nas=True
)

# 执行NAS增强训练
results = trainer.train(epochs=20)
```

### 2. 命令行使用

```bash
# 使用进化算法搜索
python examples/nas_example.py \
    --strategy evolutionary \
    --max_iterations 30 \
    --time_budget 8 \
    --population_size 15

# 使用DARTS搜索
python examples/nas_example.py \
    --strategy darts \
    --max_iterations 20 \
    --time_budget 6
```

以下是几个包含更完整参数的运行命令示例：

```bash
# 示例 1: 使用进化算法，并指定输出目录和日志级别
python examples/nas_example.py \
    --strategy evolutionary \
    --max_iterations 50 \
    --time_budget 12.0 \
    --population_size 20 \
    --output_dir outputs/my_nas_experiment \
    --log_level DEBUG

# 示例 2: 使用DARTS，并指定基线和最终训练轮数
python examples/nas_example.py \
    --strategy darts \
    --max_iterations 40 \
    --time_budget 10.0 \
    --baseline_epochs 10 \
    --final_epochs 30

# 示例 3: 禁用NAS功能，仅进行标准训练 (此时NAS相关参数无效)
python examples/nas_example.py \
    --enable_nas False \
    --config_file configs/my_custom_config.yaml \
    --final_epochs 50
```

### 3. NAS 系统入口点

NAS 系统的入口主要有两种方式：

1.  **命令行入口**：
    这是最直接和常用的方式，通过运行 `examples/nas_example.py` 脚本并传递命令行参数来启动 NAS 实验。

2.  **编程接口入口 (Python API)**：
    NAS 系统的核心功能被封装在 `src/optimization/nas/` 目录下的模块中，特别是 `src.optimization.nas.integration.GANTrainerWithNAS` 类和 `src.optimization.nas.manager.NASConfig` 类。您可以在自己的 Python 脚本中导入并使用这些类，以编程方式集成 NAS 功能。

    例如，以下代码片段展示了如何在 Python 代码中直接使用 NAS 模块：
    ```python
    from src.optimization.nas.integration import GANTrainerWithNAS
    from src.optimization.nas.manager import NASConfig
    from src.utils.config_manager import ConfigManager
    # ... 准备 train_loader 和 val_loader ...

    config = ConfigManager("configs/nas_config.yaml")
    nas_config = NASConfig(
        search_strategy="evolutionary",
        max_iterations=30,
        time_budget_hours=8.0,
        population_size=15
    )
    trainer = GANTrainerWithNAS(
        config=config,
        train_loader=train_loader,
        val_loader=val_loader,
        nas_config=nas_config,
        enable_nas=True
    )
    results = trainer.train(epochs=20)
    ```
    这种方式提供了更大的灵活性，允许开发者将 NAS 功能嵌入到更复杂的训练或自动化流程中。

## 配置说明

### NAS配置参数

```yaml
nas:
  # 搜索策略
  search_strategy: "evolutionary"  # "evolutionary" 或 "darts"

  # 搜索预算
  max_iterations: 30
  time_budget_hours: 8.0

  # 评估配置
  max_epochs_per_eval: 5
  early_stop_patience: 2
  memory_limit_mb: 2800

  # 进化算法配置
  evolutionary:
    population_size: 15
    mutation_rate: 0.3
    crossover_rate: 0.7

  # 资源约束
  constraints:
    gpu_memory_limit_gb: 3.0
    max_parameters: 10000000
```

### 搜索空间配置

```yaml
search_space:
  # 架构参数
  generator:
    num_layers_range: [2, 5]
    hidden_dim_options: [64, 128, 256]
    attention_heads_options: [2, 4, 8]
    activation_types: ["relu", "leaky_relu", "gelu", "swish", "tanh"]

  discriminator:
    num_layers_range: [3, 6]
    hidden_dim_options: [96, 192, 384]
    attention_mechanisms: ["multi_head", "multi_scale", "temporal_wrapper", "adaptive"]

  feature_encoder:
    num_scales_range: [2, 5]
    amplification_factor_range: [1.0, 3.0]
    kernel_size_options: [3, 5, 7, 9, 11]

  # 高优先级搜索参数 (新增)
  training_strategy:
    optimizer_types: ["adam", "adamw", "rmsprop", "sgd"]
    lr_range: [0.0001, 0.01]
    lr_scheduler_types: ["plateau", "cosine", "exponential", "step", "linear"]
    n_critic_range: [1, 5]

  loss_config:
    adversarial_weight_range: [0.5, 2.0]
    feature_matching_weight_range: [0.1, 5.0]
    lambda_gp_range: [0.01, 1.0]
    loss_types: ["wgan_gp", "lsgan", "vanilla", "hinge"]

  regularization:
    dropout_range: [0.0, 0.5]
    gradient_clip_range: [0.1, 5.0]
    normalization_types: ["batch_norm", "layer_norm", "instance_norm", "none"]
```

### 命令行参数详解

以下是运行命令 `python examples/nas_example.py` 的各个参数的详细解释：

1.  `--strategy`
    *   **类型**：字符串
    *   **可选值**：`evolutionary` (进化算法), `darts` (可微分架构搜索)
    *   **默认值**：`evolutionary`
    *   **说明**：指定用于神经架构搜索的策略。进化算法适合全局搜索，而 DARTS 是一种基于梯度的更高效搜索方法。

2.  `--max_iterations`
    *   **类型**：整数
    *   **默认值**：`30`
    *   **说明**：定义 NAS 搜索过程的最大迭代次数。每次迭代都会评估一组新的候选架构。

3.  `--time_budget`
    *   **类型**：浮点数
    *   **默认值**：`8.0`
    *   **说明**：设置 NAS 搜索过程的时间预算，单位为小时。当达到此时间限制时，搜索将停止。

4.  `--population_size`
    *   **类型**：整数
    *   **默认值**：`15`
    *   **说明**：仅当 `--strategy` 为 `evolutionary` 时有效。指定进化算法中每一代种群的大小，即同时评估的候选架构数量。

5.  `--enable_nas`
    *   **类型**：布尔标志 (使用 `action='store_true'`，存在即为 True)
    *   **默认值**：`True`
    *   **说明**：一个开关，用于决定是否启用 NAS 功能。如果设置为 `False`，脚本将执行标准的模型训练，而不进行架构搜索。

6.  `--baseline_epochs`
    *   **类型**：整数
    *   **默认值**：`5`
    *   **说明**：在进行神经架构搜索之前，用于训练基线模型的轮数 (epochs)。基线模型用于提供性能对比的基准。

7.  `--final_epochs`
    *   **类型**：整数
    *   **默认值**：`20`
    *   **说明**：在 NAS 搜索完成后，使用找到的最佳架构进行最终模型训练的轮数。这个阶段会进行更充分的训练以获得最终模型。

8.  `--config_file`
    *   **类型**：字符串
    *   **默认值**：`config.yaml`
    *   **说明**：指定主配置文件的路径。这个文件包含了数据处理、模型结构（非 NAS 搜索部分）、训练参数等通用配置。

9.  `--output_dir`
    *   **类型**：字符串
    *   **默认值**：`outputs/nas_example`
    *   **说明**：指定所有实验输出（如最佳架构配置、搜索摘要、模型文件和日志）的保存目录。

10. `--log_level`
    *   **类型**：字符串
    *   **可选值**：`DEBUG`, `INFO`, `WARNING`, `ERROR`
    *   **默认值**：`INFO`
    *   **说明**：设置日志的详细程度。`DEBUG` 提供最详细的信息，`ERROR` 只记录错误。

这些参数共同构成了 NAS 实验的配置，允许用户根据需求灵活地调整搜索过程和资源消耗。

## 训练流程

### 三阶段训练

1. **基线模型训练** (5轮)
   - 使用当前配置训练基线模型
   - 评估基线性能作为对比基准

2. **神经架构搜索** (8小时预算)
   - 自动搜索最优网络结构
   - 快速评估候选架构
   - 记录搜索过程和结果

3. **优化模型训练** (完整训练)
   - 使用搜索到的最优架构
   - 完整训练获得最终模型

### 训练监控

```python
# 实时监控搜索进度
trainer.search_logger.log_resource_usage(resource_monitor)

# 获取搜索摘要
summary = trainer.get_search_summary()
print(f"最佳MAE: {summary['best_mae']:.4f}")
print(f"改进幅度: {summary['improvement_percent']:.2f}%")
```

## 结果分析

### 性能对比

```python
# 分析搜索结果
results = trainer.train(epochs=20)

baseline_mae = results['performance_summary']['baseline_mae']
final_mae = results['performance_summary']['final_mae']
improvement = results['performance_summary']['improvement_achieved']

print(f"基线MAE: {baseline_mae:.4f}")
print(f"优化后MAE: {final_mae:.4f}")
print(f"性能改进: {improvement:.2f}%")
```

### 架构分析

```python
# 查看最佳架构配置
best_arch = results['best_architecture']
gen_config = best_arch['generator']
disc_config = best_arch['discriminator']
training_config = best_arch['training_strategy']
loss_config = best_arch['loss_config']
reg_config = best_arch['regularization']

# 架构参数
print(f"生成器层数: {gen_config['num_layers']}")
print(f"生成器隐藏维度: {gen_config['hidden_dim']}")
print(f"判别器层数: {disc_config['num_layers']}")

# 训练策略参数 (新增)
print(f"优化器类型: {training_config['optimizer_type']}")
print(f"生成器学习率: {training_config['generator_lr']:.4f}")
print(f"判别器训练次数: {training_config['n_critic']}")

# 损失函数参数 (新增)
print(f"对抗损失权重: {loss_config['adversarial_weight']:.2f}")
print(f"梯度惩罚权重: {loss_config['lambda_gp']:.3f}")
print(f"损失函数类型: {loss_config['discriminator_loss_type']}")

# 正则化参数 (新增)
print(f"生成器Dropout: {reg_config['generator_dropout']:.3f}")
print(f"归一化类型: {reg_config['normalization_type']}")
print(f"梯度裁剪值: {reg_config['gradient_clip_val']:.2f}")
```

## 高级功能

### 1. 自定义搜索空间

```python
from src.optimization.nas.search_space import SearchSpaceManager

# 创建自定义搜索空间
search_space = SearchSpaceManager(memory_limit_gb=3.0)

# 修改架构搜索范围
search_space.generator_space.hidden_dim_range = (128, 512)
search_space.discriminator_space.num_layers_range = (4, 8)

# 修改训练策略搜索范围 (新增)
search_space.training_strategy_space.lr_range = (0.0005, 0.005)
search_space.training_strategy_space.n_critic_range = (1, 3)

# 修改损失函数搜索范围 (新增)
search_space.loss_space.adversarial_weight_range = (0.8, 1.2)
search_space.loss_space.lambda_gp_range = (0.05, 0.5)

# 修改正则化搜索范围 (新增)
search_space.regularization_space.dropout_range = (0.1, 0.3)
search_space.regularization_space.gradient_clip_range = (0.5, 2.0)
```

### 2. 自定义评估指标

```python
from src.optimization.nas.evaluator import NASEvaluator

class CustomNASEvaluator(NASEvaluator):
    def _calculate_metrics(self, model, best_mae, mae_history, training_time):
        metrics = super()._calculate_metrics(model, best_mae, mae_history, training_time)

        # 添加自定义指标
        metrics.custom_score = self._calculate_custom_score(model)

        return metrics
```

### 3. 分布式搜索

```python
# 启用分布式搜索 (实验性功能)
nas_config = NASConfig(
    enable_distributed=True,
    num_workers=4,
    search_strategy="evolutionary"
)
```

## 最佳实践

### 1. 资源管理
- 设置合理的GPU内存限制 (建议2800MB)
- 控制搜索时间预算 (建议8-12小时)
- 使用早停机制快速筛选架构

### 2. 搜索策略选择
- **进化算法**: 适合全局搜索，对超参数不敏感
- **DARTS**: 搜索效率高，但需要调整学习率

### 3. 评估配置
- 减少每次评估的训练轮数 (5轮足够)
- 使用验证集进行快速评估
- 设置合理的早停耐心值

### 4. 结果验证
- 对比基线模型性能
- 验证架构的泛化能力
- 分析计算效率权衡

## 故障排除

### 常见问题

1. **内存不足**
   ```
   解决方案: 减小batch_size或population_size
   ```

2. **搜索时间过长**
   ```
   解决方案: 减少max_iterations或启用早停
   ```

3. **架构评估失败**
   ```
   解决方案: 检查搜索空间约束，确保架构有效性
   ```

### 调试模式

```python
# 启用调试模式
nas_config = NASConfig(
    max_iterations=5,  # 减少迭代次数
    time_budget_hours=1.0,  # 缩短时间预算
    population_size=5  # 减小种群大小
)
```

## 输出文件

### 搜索结果
- `outputs/nas/best_architecture.json`: 最佳架构配置
- `outputs/nas/search_summary.json`: 搜索摘要
- `logs/nas/nas_experiment_*.json`: 详细搜索日志

### 模型文件
- `outputs/nas/baseline_model.pth`: 基线模型
- `outputs/nas/optimized_model.pth`: 优化后模型

## 性能基准

### 预期改进
- **MAE改进**: 10-25% (通过优化训练策略和损失函数)
- **训练稳定性**: 提升15-30% (通过优化学习率和正则化)
- **参数效率**: 减少20-40%参数量 (通过架构和正则化优化)
- **收敛速度**: 提升20-35% (通过优化器和学习率调度)
- **泛化能力**: 提升10-20% (通过正则化和dropout优化)

### 时间成本
- **搜索时间**: 6-12小时 (取决于配置)
- **总训练时间**: 不超过原训练时间的3倍
- **内存使用**: 控制在3GB以内

## 扩展开发

### 添加新的搜索策略

```python
from src.optimization.nas.searchers import SearcherBase

class CustomSearcher(SearcherBase):
    def search(self):
        # 实现自定义搜索逻辑
        pass
```

### 集成新的评估指标

```python
from src.optimization.nas.evaluator import ArchitectureMetrics

@dataclass
class ExtendedMetrics(ArchitectureMetrics):
    custom_metric: float = 0.0
```

## 扩展搜索参数详解

### 训练策略参数
NAS系统现在可以搜索最优的训练配置：

```python
# 优化器类型搜索
optimizer_types = ["adam", "adamw", "rmsprop", "sgd"]

# 学习率配置搜索
generator_lr_range = (0.0001, 0.01)
discriminator_lr_range = (0.0001, 0.01)
lr_ratio_range = (0.5, 2.0)  # G/D学习率比例

# 学习率调度器搜索
scheduler_types = ["plateau", "cosine", "exponential", "step", "linear"]

# 训练平衡搜索
n_critic_range = (1, 5)  # 判别器训练次数
g_steps_range = (1, 3)   # 生成器训练次数
```

### 损失函数参数
自动优化损失函数配置：

```python
# 损失权重搜索
adversarial_weight_range = (0.5, 2.0)
feature_matching_weight_range = (0.1, 5.0)
temporal_consistency_weight_range = (0.01, 1.0)
regression_weight_range = (1.0, 50.0)

# 梯度惩罚搜索
lambda_gp_range = (0.01, 1.0)
gp_target_norm_range = (0.5, 2.0)

# 损失函数类型搜索
loss_types = ["wgan_gp", "lsgan", "vanilla", "hinge"]
```

### 正则化参数
优化模型正则化策略：

```python
# Dropout配置搜索
generator_dropout_range = (0.0, 0.5)
discriminator_dropout_range = (0.0, 0.5)
feature_encoder_dropout_range = (0.0, 0.5)

# 权重正则化搜索
weight_decay_range = (0.0, 0.01)
gradient_clip_range = (0.1, 5.0)

# 归一化类型搜索
normalization_types = ["batch_norm", "layer_norm", "instance_norm", "none"]

# 噪声注入搜索
noise_std_range = (0.0, 0.5)
input_noise_std_range = (0.0, 0.1)
```

### 搜索空间统计
扩展后的搜索空间规模：

- **总参数类别**: 6大类 (架构 + 训练策略 + 损失 + 正则化)
- **离散参数**: 25+ 个
- **连续参数**: 30+ 个
- **枚举选项**: 优化器(4) × 调度器(5) × 损失类型(4) × 归一化(4) = 320种基础组合
- **总搜索空间**: 数十亿种配置组合

## 参考资料

- [DARTS论文](https://arxiv.org/abs/1806.09055)
- [进化算法NAS综述](https://arxiv.org/abs/1912.00848)
- [时序预测架构设计](https://arxiv.org/abs/2001.00179)
- [扩展搜索参数文档](./NAS_EXTENDED_SEARCH_PARAMETERS.md)
