"""激活函数工厂模块 - 提供统一的激活函数创建接口

模块路径: src/models/gan/components/activation_factory.py

功能说明：
1. 支持多种激活函数类型：ReLU, LeakyReLU, GELU, Swish, Tanh等
2. 提供统一的创建接口
3. 支持配置参数传递
"""

import torch
import torch.nn as nn
from typing import Dict, Any

from src.utils.logger import get_logger

logger = get_logger("ActivationFactory")


class Swish(nn.Module):
    """Swish激活函数实现 - f(x) = x * sigmoid(x)"""

    def __init__(self):
        super().__init__()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return x * torch.sigmoid(x)


def create_activation(activation_type: str, **kwargs) -> nn.Module:
    """创建激活函数

    Args:
        activation_type: 激活函数类型
        **kwargs: 激活函数参数

    Returns:
        nn.Module: 激活函数实例

    Raises:
        ValueError: 不支持的激活函数类型
    """
    activation_type = activation_type.lower()

    if activation_type == "relu":
        return nn.ReLU(inplace=kwargs.get('inplace', False))

    elif activation_type == "leaky_relu":
        negative_slope = kwargs.get('negative_slope', 0.01)
        return nn.LeakyReLU(negative_slope=negative_slope, inplace=kwargs.get('inplace', False))

    elif activation_type == "gelu":
        return nn.GELU()

    elif activation_type == "swish":
        return Swish()

    elif activation_type == "tanh":
        return nn.Tanh()

    elif activation_type == "sigmoid":
        return nn.Sigmoid()

    elif activation_type == "elu":
        alpha = kwargs.get('alpha', 1.0)
        return nn.ELU(alpha=alpha, inplace=kwargs.get('inplace', False))

    else:
        supported_types = ["relu", "leaky_relu", "gelu", "swish", "tanh", "sigmoid", "elu"]
        error_msg = f"不支持的激活函数类型: {activation_type}，支持的类型: {supported_types}"
        logger.error(error_msg)
        raise ValueError(error_msg)


def get_activation_info(activation_type: str) -> Dict[str, Any]:
    """获取激活函数信息

    Args:
        activation_type: 激活函数类型

    Returns:
        Dict[str, Any]: 激活函数信息
    """
    activation_type = activation_type.lower()

    info_map = {
        "relu": {
            "name": "ReLU",
            "description": "Rectified Linear Unit - f(x) = max(0, x)",
            "range": "(0, +∞)",
            "differentiable": True,
            "monotonic": True
        },
        "leaky_relu": {
            "name": "Leaky ReLU",
            "description": "Leaky Rectified Linear Unit - f(x) = max(αx, x)",
            "range": "(-∞, +∞)",
            "differentiable": True,
            "monotonic": True
        },
        "gelu": {
            "name": "GELU",
            "description": "Gaussian Error Linear Unit - f(x) = x * Φ(x)",
            "range": "(-∞, +∞)",
            "differentiable": True,
            "monotonic": False
        },
        "swish": {
            "name": "Swish",
            "description": "Swish activation - f(x) = x * sigmoid(x)",
            "range": "(-∞, +∞)",
            "differentiable": True,
            "monotonic": False
        },
        "tanh": {
            "name": "Tanh",
            "description": "Hyperbolic Tangent - f(x) = tanh(x)",
            "range": "(-1, 1)",
            "differentiable": True,
            "monotonic": True
        },
        "sigmoid": {
            "name": "Sigmoid",
            "description": "Sigmoid function - f(x) = 1/(1+e^(-x))",
            "range": "(0, 1)",
            "differentiable": True,
            "monotonic": True
        },
        "elu": {
            "name": "ELU",
            "description": "Exponential Linear Unit - f(x) = x if x>0 else α(e^x-1)",
            "range": "(-α, +∞)",
            "differentiable": True,
            "monotonic": True
        }
    }

    if activation_type not in info_map:
        supported_types = list(info_map.keys())
        error_msg = f"不支持的激活函数类型: {activation_type}，支持的类型: {supported_types}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    return info_map[activation_type]


def test_activation(activation_type: str, test_input: torch.Tensor = None) -> torch.Tensor:
    """测试激活函数

    Args:
        activation_type: 激活函数类型
        test_input: 测试输入，默认为标准测试张量

    Returns:
        torch.Tensor: 激活函数输出
    """
    if test_input is None:
        test_input = torch.tensor([-2.0, -1.0, 0.0, 1.0, 2.0])

    activation = create_activation(activation_type)
    output = activation(test_input)

    logger.info(f"激活函数 {activation_type} 测试:")
    logger.info(f"输入: {test_input.tolist()}")
    logger.info(f"输出: {output.tolist()}")

    return output


def test_all_activations():
    """测试所有支持的激活函数"""
    print("=== 测试所有激活函数 ===")

    activations = ["relu", "leaky_relu", "tanh", "sigmoid", "elu", "gelu", "swish"]
    results = {}

    for activation_type in activations:
        print(f"\n--- 测试 {activation_type} ---")
        try:
            activation = create_activation(activation_type)
            print(f"✅ 成功创建激活函数: {type(activation).__name__}")

            # 测试前向传播
            x = torch.randn(2, 3, 4)
            y = activation(x)
            print(f"✅ 前向传播成功: 输入形状 {x.shape} -> 输出形状 {y.shape}")

            results[activation_type] = True
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results[activation_type] = False

    print(f"\n=== 测试结果汇总 ===")
    for activation_type, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{activation_type}: {status}")

    all_passed = all(results.values())
    print(f"\n总体结果: {'✅ 全部通过' if all_passed else '❌ 部分失败'}")
    return all_passed


def test_swish_specifically():
    """专门测试Swish激活函数"""
    print("=== 专门测试Swish激活函数 ===")

    try:
        # 测试创建
        swish = create_activation("swish")
        print(f"✅ 成功创建Swish: {type(swish).__name__}")

        # 测试不同输入
        test_cases = [
            torch.tensor([-2.0, -1.0, 0.0, 1.0, 2.0]),
            torch.randn(3, 4),
            torch.randn(2, 3, 4)
        ]

        for i, x in enumerate(test_cases):
            y = swish(x)
            print(f"✅ 测试用例 {i+1}: 输入形状 {x.shape} -> 输出形状 {y.shape}")

            # 验证Swish的数学性质: f(x) = x * sigmoid(x)
            expected = x * torch.sigmoid(x)
            if torch.allclose(y, expected, atol=1e-6):
                print(f"✅ 数学验证通过: Swish(x) = x * sigmoid(x)")
            else:
                print(f"❌ 数学验证失败")
                return False

        print("✅ Swish激活函数测试全部通过")
        return True

    except Exception as e:
        print(f"❌ Swish测试失败: {e}")
        return False
