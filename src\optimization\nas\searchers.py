"""搜索策略模块 - 实现DARTS和进化算法搜索策略

模块路径: src/optimization/nas/searchers.py

功能说明：
1. SearcherBase: 搜索器基类
2. DARTSSearcher: 可微分架构搜索实现
3. EvolutionarySearcher: 进化算法搜索实现
4. 支持早停和资源约束
"""

from __future__ import annotations

import random
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

from src.utils.logger import get_logger

from .evaluator import EvaluationResult, NASEvaluator
from .search_space import ArchitectureConfig, SearchSpaceManager
from .utils import ResourceMonitor


class SearcherBase(ABC):
    """搜索器基类"""

    def __init__(self,
                 search_space: SearchSpaceManager,
                 evaluator: NASEvaluator,
                 max_iterations: int = 50,
                 time_budget_hours: float = 24.0):
        """
        Args:
            search_space: 搜索空间管理器
            evaluator: 架构评估器
            max_iterations: 最大搜索迭代次数
            time_budget_hours: 时间预算 (小时)
        """
        self.logger = get_logger(self.__class__.__name__)
        self.search_space = search_space
        self.evaluator = evaluator
        self.max_iterations = max_iterations
        self.time_budget_seconds = time_budget_hours * 3600

        self.resource_monitor = ResourceMonitor()
        self.search_history: List[EvaluationResult] = []
        self.best_result: Optional[EvaluationResult] = None

        self.logger.info(f"搜索器初始化 - 最大迭代: {max_iterations}, "
                        f"时间预算: {time_budget_hours}小时")

    @abstractmethod
    def search(self) -> EvaluationResult:
        """执行架构搜索"""
        pass

    def _check_stopping_criteria(self, start_time: float, iteration: int) -> bool:
        """检查停止条件"""
        # 检查时间预算
        elapsed_time = time.time() - start_time
        if elapsed_time > self.time_budget_seconds:
            self.logger.info(f"达到时间预算限制: {elapsed_time/3600:.2f}小时")
            return True

        # 检查迭代次数
        if iteration >= self.max_iterations:
            self.logger.info(f"达到最大迭代次数: {iteration}")
            return True

        # 检查内存使用
        memory_usage = self.resource_monitor.get_gpu_memory_usage()
        if memory_usage > 2900:  # 接近3GB限制
            self.logger.warning(f"内存使用接近限制: {memory_usage:.1f}MB")
            return True

        return False

    def _update_best_result(self, result: EvaluationResult):
        """更新最佳结果"""
        if result.success and (self.best_result is None or
                              result.is_better_than(self.best_result)):
            self.best_result = result
            self.logger.info(f"发现更好的架构 - 总体分数: {result.metrics.overall_score:.4f}, "
                           f"MAE: {result.metrics.mae_score:.4f}")


class DARTSSearcher(SearcherBase):
    """可微分架构搜索 (DARTS) 实现"""

    def __init__(self,
                 search_space: SearchSpaceManager,
                 evaluator: NASEvaluator,
                 max_iterations: int = 30,
                 time_budget_hours: float = 12.0,
                 learning_rate: float = 0.025,
                 momentum: float = 0.9):
        super().__init__(search_space, evaluator, max_iterations, time_budget_hours)
        self.learning_rate = learning_rate
        self.momentum = momentum

        # DARTS特定参数
        self.architecture_weights = self._initialize_architecture_weights()
        self.optimizer = torch.optim.SGD(
            self.architecture_weights.values(),
            lr=learning_rate,
            momentum=momentum
        )

    def _initialize_architecture_weights(self) -> Dict[str, torch.Tensor]:
        """初始化架构权重"""
        weights = {}

        # 生成器层数权重 (2-5层)
        weights['gen_layers'] = torch.randn(4, requires_grad=True)  # 4个选择

        # 生成器隐藏维度权重 (64, 128, 256)
        weights['gen_hidden'] = torch.randn(3, requires_grad=True)  # 3个选择

        # 注意力头数权重 (2, 4, 8)
        weights['attention_heads'] = torch.randn(3, requires_grad=True)

        # 判别器分支权重
        weights['disc_branches'] = torch.randn(3, requires_grad=True)  # 3个分支

        # 注意力机制权重
        weights['attention_types'] = torch.randn(5, requires_grad=True)  # 5种注意力

        self.logger.info("DARTS架构权重初始化完成")
        return weights

    def search(self) -> EvaluationResult:
        """执行DARTS搜索"""
        start_time = time.time()
        self.logger.info("开始DARTS架构搜索")

        for iteration in range(self.max_iterations):
            if self._check_stopping_criteria(start_time, iteration):
                break

            try:
                # 从当前权重采样架构
                architecture = self._sample_from_weights()

                # 评估架构
                result = self.evaluator.evaluate_architecture(architecture)
                self.search_history.append(result)

                if result.success:
                    # 更新架构权重
                    self._update_weights(result)
                    self._update_best_result(result)

                self.logger.info(f"DARTS迭代 {iteration+1}/{self.max_iterations} - "
                               f"MAE: {result.metrics.mae_score:.4f}, "
                               f"成功: {result.success}")

            except Exception as e:
                self.logger.error(f"DARTS迭代 {iteration} 失败: {e}")
                continue

        elapsed_time = time.time() - start_time
        best_mae = self.best_result.metrics.mae_score if self.best_result and hasattr(self.best_result, 'metrics') and hasattr(self.best_result.metrics, 'mae_score') else float('nan')
        self.logger.info(f"DARTS搜索完成 - 耗时: {elapsed_time/3600:.2f}小时, "
                        f"最佳MAE: {best_mae:.4f if not pd.isna(best_mae) else 'N/A'}")

        return self.best_result if self.best_result is not None else self._get_fallback_result()

    def _sample_from_weights(self) -> ArchitectureConfig:
        """从权重分布中采样架构"""
        # 使用Gumbel-Softmax采样
        temperature = 1.0

        # 生成器层数 (2-5)
        gen_layers_probs = F.gumbel_softmax(self.architecture_weights['gen_layers'],
                                           tau=temperature, hard=True)
        gen_layers = 2 + torch.argmax(gen_layers_probs).item()

        # 生成器隐藏维度
        gen_hidden_probs = F.gumbel_softmax(self.architecture_weights['gen_hidden'],
                                           tau=temperature, hard=True)
        hidden_dims = [64, 128, 256]
        hidden_idx = int(torch.argmax(gen_hidden_probs).item())
        gen_hidden = hidden_dims[hidden_idx] if 0 <= hidden_idx < len(hidden_dims) else hidden_dims[0]

        # 注意力头数
        attention_heads_probs = F.gumbel_softmax(self.architecture_weights['attention_heads'],
                                                tau=temperature, hard=True)
        head_options = [2, 4, 8]
        head_idx = int(torch.argmax(attention_heads_probs).item())
        attention_heads = head_options[head_idx] if 0 <= head_idx < len(head_options) else head_options[0]

        # 创建完整架构配置
        return self.search_space.sample_architecture()  # 简化实现

    def _update_weights(self, result: EvaluationResult):
        """根据评估结果更新权重"""
        if not result.success:
            return

        # 计算奖励信号
        reward = 1.0 / (1.0 + result.metrics.mae_score)  # MAE越小奖励越大

        # 使用REINFORCE更新权重
        self.optimizer.zero_grad()

        # 简化的梯度更新
        for weight_name, weight_tensor in self.architecture_weights.items():
            if weight_tensor.grad is not None:
                weight_tensor.grad.data.mul_(reward)

        self.optimizer.step()

    def _get_fallback_result(self) -> EvaluationResult:
        """获取回退结果"""
        if self.search_history:
            # 返回历史中最好的结果
            best_in_history = max(self.search_history,
                                key=lambda x: x.metrics.overall_score if x.success else 0)
            return best_in_history

        # 返回随机架构
        random_arch = self.search_space.sample_architecture()
        return self.evaluator.evaluate_architecture(random_arch)


class EvolutionarySearcher(SearcherBase):
    """进化算法搜索实现"""

    def __init__(self,
                 search_space: SearchSpaceManager,
                 evaluator: NASEvaluator,
                 max_iterations: int = 50,
                 time_budget_hours: float = 24.0,
                 population_size: int = 20,
                 mutation_rate: float = 0.3,
                 crossover_rate: float = 0.7):
        super().__init__(search_space, evaluator, max_iterations, time_budget_hours)
        self.population_size = population_size
        self.mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate

        self.population: List[ArchitectureConfig] = []
        self.fitness_scores: List[float] = []

    def search(self) -> EvaluationResult:
        """执行进化搜索"""
        start_time = time.time()
        self.logger.info(f"开始进化算法搜索 - 种群大小: {self.population_size}")

        # 初始化种群
        self._initialize_population()

        generation = 0
        while generation < self.max_iterations:
            if self._check_stopping_criteria(start_time, generation):
                break

            try:
                # 评估种群
                self._evaluate_population()

                # 选择、交叉、变异
                self._evolve_population()

                generation += 1

                best_fitness = max(self.fitness_scores) if self.fitness_scores else 0
                self.logger.info(f"进化代数 {generation} - 最佳适应度: {best_fitness:.4f}")

            except Exception as e:
                self.logger.error(f"进化代数 {generation} 失败: {e}")
                break

        elapsed_time = time.time() - start_time
        self.logger.info(f"进化搜索完成 - 耗时: {elapsed_time/3600:.2f}小时")

        return self.best_result or self._get_fallback_result()

    def _initialize_population(self):
        """初始化种群"""
        self.population = []
        for _ in range(self.population_size):
            try:
                architecture = self.search_space.sample_architecture()
                self.population.append(architecture)
            except Exception as e:
                self.logger.warning(f"初始化个体失败: {e}")

        self.logger.info(f"种群初始化完成 - 实际大小: {len(self.population)}")

    def _evaluate_population(self):
        """评估种群"""
        self.fitness_scores = []

        for i, architecture in enumerate(self.population):
            try:
                result = self.evaluator.evaluate_architecture(architecture)
                self.search_history.append(result)

                if result.success:
                    fitness = result.metrics.overall_score
                    self._update_best_result(result)
                else:
                    fitness = 0.0

                self.fitness_scores.append(fitness)

                self.logger.debug(f"个体 {i+1}/{len(self.population)} - "
                                f"适应度: {fitness:.4f}")

            except Exception as e:
                # 检查是否是架构配置错误（embed_dim 不能被 num_heads 整除）
                error_str = str(e)
                if "embed_dim" in error_str and "num_heads" in error_str and "整除" in error_str:
                    self.logger.error(f"评估个体 {i} 失败 - 架构配置错误: {e}")
                    raise RuntimeError(f"架构配置错误: {e}") from e
                elif "Expected all tensors to be on the same device" in error_str:
                    self.logger.error(f"评估个体 {i} 失败 - 设备不匹配错误: {e}")
                    raise RuntimeError(f"设备不匹配错误: {e}") from e
                else:
                    self.logger.error(f"评估个体 {i} 失败: {e}")
                    raise RuntimeError(f"个体评估失败: {e}") from e

    def _evolve_population(self):
        """进化种群"""
        new_population = []

        # 精英保留
        elite_size = max(1, self.population_size // 10)
        elite_indices = np.argsort(self.fitness_scores)[-elite_size:]
        for idx in elite_indices:
            new_population.append(self.population[idx])

        # 生成新个体
        while len(new_population) < self.population_size:
            if random.random() < self.crossover_rate and len(self.population) >= 2:
                # 交叉
                parent1, parent2 = self._tournament_selection(2)
                child = self._crossover(parent1, parent2)
            else:
                # 变异
                parent = self._tournament_selection(1)[0]
                child = self._mutate(parent)

            new_population.append(child)

        self.population = new_population[:self.population_size]

    def _tournament_selection(self, num_parents: int) -> List[ArchitectureConfig]:
        """锦标赛选择"""
        selected = []
        for _ in range(num_parents):
            tournament_size = 3
            candidates = random.sample(range(len(self.population)),
                                     min(tournament_size, len(self.population)))
            best_idx = max(candidates, key=lambda i: self.fitness_scores[i])
            selected.append(self.population[best_idx])
        return selected

    def _crossover(self, parent1: ArchitectureConfig, parent2: ArchitectureConfig) -> ArchitectureConfig:
        """交叉操作"""
        # 简化的交叉：随机选择每个组件
        child = self.search_space.sample_architecture()

        # 随机继承父代特征
        if random.random() < 0.5:
            child.generator.num_layers = parent1.generator.num_layers
            child.generator.hidden_dim = parent1.generator.hidden_dim
        else:
            child.generator.num_layers = parent2.generator.num_layers
            child.generator.hidden_dim = parent2.generator.hidden_dim

        if random.random() < 0.5:
            child.discriminator.num_layers = parent1.discriminator.num_layers
            child.discriminator.hidden_dim = parent1.discriminator.hidden_dim
        else:
            child.discriminator.num_layers = parent2.discriminator.num_layers
            child.discriminator.hidden_dim = parent2.discriminator.hidden_dim

        return child

    def _mutate(self, parent: ArchitectureConfig) -> ArchitectureConfig:
        """变异操作"""
        child = self.search_space.sample_architecture()

        # 保留大部分父代特征，随机变异少数特征
        if random.random() > self.mutation_rate:
            child.generator.num_layers = parent.generator.num_layers
        if random.random() > self.mutation_rate:
            child.generator.hidden_dim = parent.generator.hidden_dim
        if random.random() > self.mutation_rate:
            child.discriminator.num_layers = parent.discriminator.num_layers
        if random.random() > self.mutation_rate:
            child.discriminator.hidden_dim = parent.discriminator.hidden_dim

        return child

    def _get_fallback_result(self) -> EvaluationResult:
        """获取回退结果"""
        if self.search_history:
            best_in_history = max(self.search_history,
                                key=lambda x: x.metrics.overall_score if x.success else 0)
            return best_in_history

        # 返回随机架构
        random_arch = self.search_space.sample_architecture()
        return self.evaluator.evaluate_architecture(random_arch)
