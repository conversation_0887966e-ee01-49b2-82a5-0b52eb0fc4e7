"""NAS工具模块 - 提供架构编码、资源监控、日志记录等工具

模块路径: src/optimization/nas/utils.py

功能说明：
1. ArchitectureEncoder: 架构配置编码解码
2. ResourceMonitor: 资源使用监控
3. SearchLogger: 搜索过程日志记录
4. 分布式搜索支持工具
"""

from __future__ import annotations

import json
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import torch
import psutil

from src.utils.logger import get_logger

from .search_space import ArchitectureConfig


class ArchitectureEncoder:
    """架构配置编码器"""

    def __init__(self):
        self.logger = get_logger("ArchitectureEncoder")

    def encode(self, config: ArchitectureConfig) -> str:
        """将架构配置编码为字符串"""
        try:
            config_dict = config.to_dict()
            return json.dumps(config_dict, sort_keys=True)
        except Exception as e:
            self.logger.error(f"编码架构配置失败: {e}")
            return ""

    def decode(self, encoded_str: str) -> Optional[ArchitectureConfig]:
        """从字符串解码架构配置"""
        try:
            config_dict = json.loads(encoded_str)
            return ArchitectureConfig.from_dict(config_dict)
        except Exception as e:
            self.logger.error(f"解码架构配置失败: {e}")
            return None

    def save_to_file(self, config: ArchitectureConfig, file_path: Path):
        """保存架构配置到文件"""
        try:
            config_dict = config.to_dict()
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            self.logger.info(f"架构配置已保存到: {file_path}")
        except Exception as e:
            self.logger.error(f"保存架构配置失败: {e}")

    def load_from_file(self, file_path: Path) -> Optional[ArchitectureConfig]:
        """从文件加载架构配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            config = ArchitectureConfig.from_dict(config_dict)
            self.logger.info(f"架构配置已从文件加载: {file_path}")
            return config
        except Exception as e:
            self.logger.error(f"加载架构配置失败: {e}")
            return None


class ResourceMonitor:
    """资源使用监控器"""

    def __init__(self):
        self.logger = get_logger("ResourceMonitor")
        self.start_time = time.time()

    def get_gpu_memory_usage(self) -> float:
        """获取GPU内存使用量 (MB)"""
        try:
            if torch.cuda.is_available():
                memory_allocated = torch.cuda.memory_allocated() / (1024 ** 2)
                return memory_allocated
            return 0.0
        except Exception as e:
            self.logger.warning(f"获取GPU内存使用量失败: {e}")
            return 0.0

    def get_gpu_memory_reserved(self) -> float:
        """获取GPU保留内存量 (MB)"""
        try:
            if torch.cuda.is_available():
                memory_reserved = torch.cuda.memory_reserved() / (1024 ** 2)
                return memory_reserved
            return 0.0
        except Exception as e:
            self.logger.warning(f"获取GPU保留内存量失败: {e}")
            return 0.0

    def get_cpu_usage(self) -> float:
        """获取CPU使用率 (%)"""
        try:
            return psutil.cpu_percent(interval=1)
        except Exception as e:
            self.logger.warning(f"获取CPU使用率失败: {e}")
            return 0.0

    def get_memory_usage(self) -> Dict[str, float]:
        """获取系统内存使用情况"""
        try:
            memory = psutil.virtual_memory()
            return {
                'total_gb': memory.total / (1024 ** 3),
                'available_gb': memory.available / (1024 ** 3),
                'used_gb': memory.used / (1024 ** 3),
                'percent': memory.percent
            }
        except Exception as e:
            self.logger.warning(f"获取内存使用情况失败: {e}")
            return {'total_gb': 0, 'available_gb': 0, 'used_gb': 0, 'percent': 0}

    def get_elapsed_time(self) -> float:
        """获取运行时间 (秒)"""
        return time.time() - self.start_time

    def log_resource_status(self):
        """记录当前资源状态"""
        gpu_memory = self.get_gpu_memory_usage()
        gpu_reserved = self.get_gpu_memory_reserved()
        cpu_usage = self.get_cpu_usage()
        memory_info = self.get_memory_usage()
        elapsed_time = self.get_elapsed_time()

        self.logger.info(
            f"资源状态 - "
            f"GPU内存: {gpu_memory:.1f}MB (保留: {gpu_reserved:.1f}MB), "
            f"CPU: {cpu_usage:.1f}%, "
            f"系统内存: {memory_info['used_gb']:.1f}/{memory_info['total_gb']:.1f}GB "
            f"({memory_info['percent']:.1f}%), "
            f"运行时间: {elapsed_time/3600:.2f}小时"
        )

    def check_memory_limit(self, limit_mb: float) -> bool:
        """检查是否超过内存限制"""
        current_usage = self.get_gpu_memory_usage()
        return current_usage > limit_mb

    def clear_gpu_cache(self):
        """清理GPU缓存"""
        try:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                self.logger.debug("GPU缓存已清理")
        except Exception as e:
            self.logger.warning(f"清理GPU缓存失败: {e}")


class SearchLogger:
    """搜索过程日志记录器"""

    def __init__(self, log_dir: Path, experiment_name: str = "nas_search"):
        self.logger = get_logger("SearchLogger")
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)

        self.experiment_name = experiment_name
        self.start_time = datetime.now()

        # 创建实验日志文件
        timestamp = self.start_time.strftime("%Y%m%d_%H%M%S")
        self.log_file = self.log_dir / f"{experiment_name}_{timestamp}.json"

        # 初始化日志数据
        self.log_data = {
            'experiment_name': experiment_name,
            'start_time': self.start_time.isoformat(),
            'search_results': [],
            'best_architectures': [],
            'resource_usage': [],
            'metadata': {}
        }

        self.logger.info(f"搜索日志初始化 - 日志文件: {self.log_file}")

    def log_search_result(self,
                         iteration: int,
                         architecture: ArchitectureConfig,
                         metrics: Dict[str, Any],
                         success: bool,
                         evaluation_time: float):
        """记录搜索结果"""
        result_entry = {
            'iteration': iteration,
            'timestamp': datetime.now().isoformat(),
            'architecture': architecture.to_dict(),
            'metrics': metrics,
            'success': success,
            'evaluation_time': evaluation_time
        }

        self.log_data['search_results'].append(result_entry)
        self._save_log()

    def log_best_architecture(self,
                             iteration: int,
                             architecture: ArchitectureConfig,
                             metrics: Dict[str, Any]):
        """记录最佳架构"""
        best_entry = {
            'iteration': iteration,
            'timestamp': datetime.now().isoformat(),
            'architecture': architecture.to_dict(),
            'metrics': metrics
        }

        self.log_data['best_architectures'].append(best_entry)
        self._save_log()

        self.logger.info(f"记录最佳架构 - 迭代 {iteration}, "
                        f"MAE: {metrics.get('mae_score', 'N/A')}")

    def log_resource_usage(self, resource_monitor: ResourceMonitor):
        """记录资源使用情况"""
        resource_entry = {
            'timestamp': datetime.now().isoformat(),
            'gpu_memory_mb': resource_monitor.get_gpu_memory_usage(),
            'gpu_reserved_mb': resource_monitor.get_gpu_memory_reserved(),
            'cpu_percent': resource_monitor.get_cpu_usage(),
            'system_memory': resource_monitor.get_memory_usage(),
            'elapsed_time_hours': resource_monitor.get_elapsed_time() / 3600
        }

        self.log_data['resource_usage'].append(resource_entry)

        # 每10次记录保存一次
        if len(self.log_data['resource_usage']) % 10 == 0:
            self._save_log()

    def set_metadata(self, key: str, value: Any):
        """设置元数据"""
        self.log_data['metadata'][key] = value
        self._save_log()

    def finalize_search(self,
                       total_iterations: int,
                       best_architecture: Optional[ArchitectureConfig],
                       best_metrics: Optional[Dict[str, Any]]):
        """完成搜索记录"""
        end_time = datetime.now()

        self.log_data.update({
            'end_time': end_time.isoformat(),
            'total_duration_hours': (end_time - self.start_time).total_seconds() / 3600,
            'total_iterations': total_iterations,
            'final_best_architecture': best_architecture.to_dict() if best_architecture else None,
            'final_best_metrics': best_metrics
        })

        self._save_log()

        self.logger.info(f"搜索完成 - 总迭代: {total_iterations}, "
                        f"耗时: {self.log_data['total_duration_hours']:.2f}小时")

    def _save_log(self):
        """保存日志到文件"""
        try:
            with open(self.log_file, 'w', encoding='utf-8') as f:
                json.dump(self.log_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存搜索日志失败: {e}")

    def get_search_summary(self) -> Dict[str, Any]:
        """获取搜索摘要"""
        results = self.log_data['search_results']
        successful_results = [r for r in results if r['success']]

        if not successful_results:
            return {'status': 'no_successful_results'}

        mae_scores = [r['metrics'].get('mae_score', float('inf'))
                     for r in successful_results]

        return {
            'total_iterations': len(results),
            'successful_iterations': len(successful_results),
            'success_rate': len(successful_results) / len(results) if results else 0,
            'best_mae': min(mae_scores) if mae_scores else float('inf'),
            'average_mae': sum(mae_scores) / len(mae_scores) if mae_scores else float('inf'),
            'total_evaluation_time': sum(r['evaluation_time'] for r in results),
            'average_evaluation_time': sum(r['evaluation_time'] for r in results) / len(results) if results else 0
        }


class DistributedSearchCoordinator:
    """分布式搜索协调器"""

    def __init__(self, num_workers: int = 1):
        self.logger = get_logger("DistributedSearchCoordinator")
        self.num_workers = num_workers

        # 简化实现 - 单机多进程
        self.logger.info(f"分布式搜索协调器初始化 - 工作进程数: {num_workers}")

    def distribute_search_tasks(self,
                               total_iterations: int,
                               search_configs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分配搜索任务"""
        # 简化实现 - 平均分配迭代次数
        iterations_per_worker = total_iterations // self.num_workers
        remaining_iterations = total_iterations % self.num_workers

        distributed_configs = []
        for i in range(self.num_workers):
            config = search_configs[i % len(search_configs)].copy()
            config['max_iterations'] = iterations_per_worker
            if i < remaining_iterations:
                config['max_iterations'] += 1
            config['worker_id'] = i
            distributed_configs.append(config)

        self.logger.info(f"任务分配完成 - {self.num_workers}个工作进程")
        return distributed_configs

    def aggregate_results(self, worker_results: List[Any]) -> Any:
        """聚合工作进程结果"""
        # 简化实现 - 选择最佳结果
        if not worker_results:
            return None

        best_result = max(worker_results,
                         key=lambda x: x.metrics.overall_score if hasattr(x, 'metrics') else 0)

        self.logger.info("结果聚合完成")
        return best_result
