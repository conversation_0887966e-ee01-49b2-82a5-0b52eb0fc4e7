"""指标计算器模块 - 专注于MAE指标计算

本模块简化为只计算MAE (Mean Absolute Error) 指标：
1. MAE指标计算 - 平均绝对误差
2. 移除其他指标以简化评估流程

主要用途：
- 模型评估 (仅MAE)
- 预测结果分析
- 性能比较
"""


import torch
import torch.nn.functional as f

from src.utils.logger import get_logger


class MetricsCalculator:
    """指标计算器 - 负责计算各种评估指标"""

    def __init__(self, config):
        """初始化指标计算器

        Args:
            config: 配置对象，不能为None

        Raises:
            ValueError: 如果config为None
        """
        self.logger = get_logger(self.__class__.__name__)

        # 验证配置不为None
        if config is None:
            error_msg = "指标计算器配置不能为None"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        self.config = config

        # 初始化指标参数 (将在_configure_metrics中设置)
        self.metrics_list = []

        # 配置指标计算器
        self._configure_metrics()

        self.logger.info("指标计算器初始化完成")

    def _configure_metrics(self):
        """配置指标计算器 - 简化为只支持MAE指标

        Raises:
            ValueError: 如果缺少必要的配置项
        """
        try:
            # 固定使用MAE指标，不再依赖配置文件
            self.metrics_list = ["mae"]
            self.logger.info("指标计算器配置为只计算MAE指标")
        except Exception as e:
            error_msg = f"配置指标计算器失败: {e!s}"
            self.logger.error(error_msg)
            raise ValueError(error_msg) from e

    def calculate_metrics(
        self,
        predictions: torch.Tensor,
        targets: torch.Tensor,
        metrics_list: list[str] | None = None
    ) -> dict[str, float]:
        """计算评估指标

        Args:
            predictions: 预测值 [batch_size, seq_length, ...]
            targets: 目标值 [batch_size, seq_length, ...]
            metrics_list: 指标列表，如果为None则使用默认列表

        Returns:
            Dict[str, float]: 指标字典

        Raises:
            ValueError: 如果预测和目标维度不匹配
        """
        # 验证输入维度
        if predictions.dim() != targets.dim():
            raise ValueError(
                f"预测和目标维度不匹配: 预测维度{predictions.dim()}D, 目标维度{targets.dim()}D"
            )

        if predictions.shape[:-1] != targets.shape[:-1]:
            raise ValueError(
                f"预测和目标形状不匹配: 预测形状{predictions.shape}, 目标形状{targets.shape}"
            )

        # --- 添加空输入验证 ---
        if predictions.numel() == 0 or targets.numel() == 0:
            raise ValueError("输入张量 predictions 和 targets 不能为空")
        # --- 验证结束 ---

        # 记录输入参数
        self.logger.debug(
            f"开始计算指标\n"
            f"- 预测形状: {predictions.shape}\n"
            f"- 目标形状: {targets.shape}\n"
            f"- 指标列表: {metrics_list if metrics_list else '默认'}"
        )

        # 使用默认指标列表
        if metrics_list is None:
            metrics_list = self.metrics_list

        # 初始化指标字典并只计算MAE指标
        metrics = {}
        if "mae" in metrics_list:
            metrics["mae"] = self.calculate_mae(predictions, targets)
        else:
            # 如果指标列表中没有MAE，强制计算MAE
            self.logger.warning("指标列表中没有MAE，强制计算MAE指标")
            metrics["mae"] = self.calculate_mae(predictions, targets)

        return metrics

    # 移除MSE和RMSE方法，专注于MAE计算

    # 移除calculate_trend_accuracy方法

    def calculate_mae(
        self,
        predictions: torch.Tensor,
        targets: torch.Tensor
    ) -> float:
        """计算平均绝对误差 (MAE)

        Args:
            predictions: 预测值 [batch_size, seq_length, ...]
            targets: 目标值 [batch_size, seq_length, ...]

        Returns:
            float: 平均绝对误差
        """
        # 使用 L1 Loss 计算 MAE
        return f.l1_loss(predictions, targets).item()

    # 移除calculate_smape方法
