"""序列生成模块 - 负责时序数据生成
模块路径: src/models/gan/components/sequence_generator.py

继承自:
- src/models/base/base_module.py: BaseModule

协作模块:
- src/models/gan/components/temporal.py
- src/models/gan/components/output_projection.py
"""
from torch import Tensor, nn

from src.models.base.base_module import BaseModule
from src.utils.config_manager import ConfigManager  # 导入 ConfigManager

from .adaptive_attention import MultiLayerAttention  # 导入多层注意力机制
from .common import OutputProjection  # Import from new location
from .temporal import TemporalCoherence
from .activation_factory import create_activation


class SequenceGenerator(BaseModule):
    def __init__(self, config: ConfigManager, input_dim: int, hidden_dim: int):
        super().__init__("SequenceGenerator")
        self.config = config # 保存配置对象

        # 从配置中读取参数
        try:
            model_config = config.model
            # 强制要求配置中存在 generator_type, num_layers, n_heads, dropout_rate
            # 添加类型检查和断言，确保 model_config 是包含所需属性的类型
            from src.utils.config.model import GANModelConfig  # 导入 GANModelConfig
            if not isinstance(model_config, GANModelConfig):
                 raise TypeError(f"model_config 必须是 GANModelConfig 类型，但得到 {type(model_config)}")

            if not hasattr(model_config, 'generator_type'):
                raise ValueError("配置 model 中缺少 generator_type")
            # 添加显式 None 检查
            if model_config.generator_type is None:
                raise ValueError("配置 model.generator_type 不能为空")
            generator_type = model_config.generator_type.lower()
            assert isinstance(generator_type, str), "model.generator_type 必须是字符串"

            if not hasattr(model_config, 'num_layers'):
                raise ValueError("配置 model 中缺少 num_layers")
            num_layers = model_config.num_layers
            assert isinstance(num_layers, int), "model.num_layers 必须是整数"

            if not hasattr(model_config, 'n_heads'):
                raise ValueError("配置 model 中缺少 n_heads")
            num_heads = model_config.n_heads
            assert isinstance(num_heads, int), "model.n_heads 必须是整数"

            if not hasattr(model_config, 'dropout_rate'):
                raise ValueError("配置 model 中缺少 dropout_rate")
            dropout_rate = model_config.dropout_rate
            assert isinstance(dropout_rate, float), "model.dropout_rate 必须是浮点数"

            # 修复：读取生成器特定配置
            generator_config = getattr(model_config, 'generator', None)
            if generator_config:
                # 读取激活函数类型
                activation_type = getattr(generator_config, 'activation_type', 'tanh')
                use_layer_norm = getattr(generator_config, 'use_layer_norm', True)
                use_residual = getattr(generator_config, 'use_residual', True)
                noise_injection_layers = getattr(generator_config, 'noise_injection_layers', [])
                self.logger.info(f"生成器配置: activation={activation_type}, layer_norm={use_layer_norm}, residual={use_residual}, noise_layers={noise_injection_layers}")
            else:
                # 使用默认值
                activation_type = 'tanh'
                use_layer_norm = True
                use_residual = True
                noise_injection_layers = []
                self.logger.warning("未找到生成器特定配置，使用默认值")

            # 存储配置参数
            self.activation_type = activation_type
            self.use_layer_norm = use_layer_norm
            self.use_residual = use_residual
            self.noise_injection_layers = noise_injection_layers

            self.logger.info(f"从配置加载生成器参数: type={generator_type}, layers={num_layers}, heads={num_heads}, dropout={dropout_rate}, activation={activation_type}")
        except AttributeError as e:
            self.logger.error(f"配置读取错误: {e} - 请确保 config.model 包含 generator_type, num_layers, n_heads, dropout_rate")
            raise ValueError(f"配置读取错误: {e}") from e

        # 参数验证
        if input_dim <= 0:
            error_msg = f"输入维度必须为正数，当前值: {input_dim}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if hidden_dim <= 0:
            error_msg = f"隐藏维度必须为正数，当前值: {hidden_dim}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if num_layers <= 0:
            error_msg = f"层数必须为正数，当前值: {num_layers}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 根据配置选择 RNN 类型并实例化
        rnn_params = {
            'input_size': input_dim,
            'hidden_size': hidden_dim,
            'num_layers': num_layers,
            'batch_first': True,
            'bidirectional': True, # 保持双向
            'dropout': dropout_rate if num_layers > 1 else 0 # 仅在多层时应用dropout
        }
        if generator_type == 'lstm':
            self.rnn = nn.LSTM(**rnn_params)
            self.logger.info("使用 LSTM 作为 RNN 层")
        elif generator_type == 'gru':
            self.rnn = nn.GRU(**rnn_params)
            self.logger.info("使用 GRU 作为 RNN 层")
        else:
            raise ValueError(f"不支持的生成器类型: {generator_type} (应为 'lstm' 或 'gru')")

        # 添加双向输出处理层
        self.bidirectional_processor = nn.Linear(hidden_dim * 2, hidden_dim)

        # 使用多层注意力机制替换单层注意力
        self.temporal_attention = MultiLayerAttention(
            embed_dim=hidden_dim,
            num_heads=num_heads,  # 使用配置中的注意力头数
            num_layers=2,  # 注意力层数固定为2 (保持不变，或后续也改为可配置)
            dropout=dropout_rate
        )

        self.temporal_coherence = TemporalCoherence(hidden_dim=hidden_dim) # Renamed variable for clarity
        # 修复：使用配置的激活函数
        self.projection = OutputProjection(
            input_dim=hidden_dim,
            output_dim=1,
            activation_type=self.activation_type
        )

        # 应用权重初始化
        self.apply(self._init_weights)
        self.logger.info(f"序列生成器初始化完成，参数数量: {self.count_parameters():,}")

    def forward(self, features: Tensor) -> Tensor:
        """生成时序数据

        Args:
            features: [batch, seq_len, input_dim]

        Returns:
            生成的序列 [batch, seq_len, 1]
        """
        import time
        from datetime import datetime

        import torch

        start_time = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 序列生成器开始处理 - 输入形状: {features.shape}")

        # 验证输入张量
        validation_start = time.time()
        self.validate_tensor(features, "输入特征", expected_dims=3)
        self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 输入验证完成 - 耗时: {time.time() - validation_start:.2f}秒")

        # 使用双向RNN处理输入特征
        rnn_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始RNN处理")
        sequence, _ = self.rnn(features)
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] RNN处理完成 - 形状: {sequence.shape}, 耗时: {time.time() - rnn_start:.2f}秒")
        self._check_numerical_stability(sequence, f"{self.rnn.__class__.__name__}输出") # 使用实际RNN类型

        # 处理双向RNN的输出
        bidirectional_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始处理双向RNN输出")
        # 双向RNN输出形状为 [batch, seq_len, hidden_dim*2]
        # 需要将其转换为 [batch, seq_len, hidden_dim]
        sequence = self.bidirectional_processor(sequence)
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 双向RNN处理完成 - 形状: {sequence.shape}, 耗时: {time.time() - bidirectional_start:.2f}秒")
        self._check_numerical_stability(sequence, "双向处理后")

        # 调试日志：记录各阶段维度
        self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 残差连接前检查 - 输入特征shape: {features.shape}, RNN输出shape: {sequence.shape}")
        self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] RNN配置 - input_size: {self.rnn.input_size}, hidden_size: {self.rnn.hidden_size}, bidirectional: {getattr(self.rnn, 'bidirectional', 'N/A')}") # 安全获取bidirectional

        # 应用层归一化和残差连接
        residual_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始应用残差连接")
        if features.size(-1) == sequence.size(-1):
            self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 应用残差连接: 特征维度匹配 {features.size(-1)}")
            sequence = sequence + features
        else:
            self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 跳过残差连接: 特征维度不匹配 (features:{features.size(-1)} != sequence:{sequence.size(-1)})")
            self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 详细维度信息 - features: {features.size()}, sequence: {sequence.size()}")
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 残差连接处理完成 - 耗时: {time.time() - residual_start:.2f}秒")

        # 应用多层注意力机制
        attention_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 多层注意力开始处理 - 输入形状: {sequence.shape}")
        sequence, _ = self.temporal_attention(sequence)
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 多层注意力处理完成 - 总耗时: {time.time() - attention_start:.2f}ms, 输出形状: {sequence.shape}")
        self._check_numerical_stability(sequence, "注意力处理后")

        # 应用时序一致性处理
        temporal_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始时序一致性处理")
        sequence = self.temporal_coherence(sequence)
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 时序一致性处理完成 - 形状: {sequence.shape}, 耗时: {time.time() - temporal_start:.2f}秒")
        self._check_numerical_stability(sequence, "时序一致性处理后")

        # 最终投影到输出维度
        projection_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始输出投影")
        output = self.projection(sequence)
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 输出投影完成 - 形状: {output.shape}, 耗时: {time.time() - projection_start:.2f}秒")
        self._check_numerical_stability(output, "最终输出")

        # 检查内存使用情况
        if torch.cuda.is_available():
            mem_allocated = torch.cuda.memory_allocated() / (1024 ** 2)
            mem_reserved = torch.cuda.memory_reserved() / (1024 ** 2)
            self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] CUDA内存使用情况 - 已分配: {mem_allocated:.2f}MB, 已保留: {mem_reserved:.2f}MB")

        end_time = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 序列生成器输出: shape={output.shape}, 总耗时: {end_time - start_time:.2f}秒")

        return output
